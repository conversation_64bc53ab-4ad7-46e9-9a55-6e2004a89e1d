<linker>
  <assembly fullname="BehaviorDesigner.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BehaviorDesigner.Runtime.BehaviorTree" preserve="all" />
    <type fullname="BehaviorDesigner.Runtime.ExternalBehaviorTree" preserve="all" />
    <type fullname="BehaviorDesigner.Runtime.BehaviorSource" preserve="nothing" serialized="true" />
    <type fullname="BehaviorDesigner.Runtime.FieldSerializationData" preserve="nothing" serialized="true" />
    <type fullname="BehaviorDesigner.Runtime.SharedBool" preserve="nothing" serialized="true" />
    <type fullname="BehaviorDesigner.Runtime.TaskSerializationData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AiBusinessDefault" preserve="all" />
    <type fullname="AiCarHorn" preserve="all" />
    <type fullname="AiCarMusic" preserve="all" />
    <type fullname="AiCarRescueCheck" preserve="all" />
    <type fullname="ApartmentValueGoal" preserve="all" />
    <type fullname="AppearanceSetter" preserve="all" />
    <type fullname="BankBalanceGoal" preserve="all" />
    <type fullname="BaseHuman" preserve="all" />
    <type fullname="BedController" preserve="all" />
    <type fullname="BigAmbitions.Rivals.SpecialRival" preserve="all" />
    <type fullname="BlackjackEmployeeController" preserve="all" />
    <type fullname="BoatGoal" preserve="all" />
    <type fullname="BuildingOutsideHangoutZone" preserve="all" />
    <type fullname="BuildingOutsideMusic" preserve="all" />
    <type fullname="BuildingRegistrationsGoal" preserve="all" />
    <type fullname="Buildings.Building" preserve="all" />
    <type fullname="Buildings.BuildingTypes.Shared.BusinessRequirement.AnyPrimaryProduct" preserve="all" />
    <type fullname="Buildings.BuildingTypes.Shared.BusinessRequirement.ItemsOfTypeInBuilding" preserve="all" />
    <type fullname="Buildings.BuildingTypes.Shared.BusinessRequirement.SpecificItemsInBuilding" preserve="all" />
    <type fullname="Buildings.BuildingTypes.Shared.BusinessRequirement.SpecificItemsInShelves" preserve="all" />
    <type fullname="Buildings.BuildingTypes.Special.PlaySpotsManager" preserve="all" />
    <type fullname="Buildings.GasStationSettings" preserve="all" />
    <type fullname="Buildings.ImportExportSettings" preserve="all" />
    <type fullname="Buildings.InteriorInstallationFirmSettings" preserve="all" />
    <type fullname="Buildings.MarketingAgencySettings" preserve="all" />
    <type fullname="Buildings.MovingServiceSettings" preserve="all" />
    <type fullname="Buildings.NightclubBusinessSimulator" preserve="all" />
    <type fullname="Buildings.Office.OfficeBusinessSimulator" preserve="all" />
    <type fullname="Buildings.RecruitmentAgencySettings" preserve="all" />
    <type fullname="Buildings.Retail.Businesses.Gym.GymBusinessSimulator" preserve="all" />
    <type fullname="Buildings.Retail.Businesses.Hairdresser.HairdresserBusinessSimulator" preserve="all" />
    <type fullname="Buildings.Retail.Simulation.FullServiceBusinessSimulator" preserve="all" />
    <type fullname="Buildings.Retail.Simulation.SelfServiceBusinessSimulator" preserve="all" />
    <type fullname="Buildings.SpecialService" preserve="all" />
    <type fullname="Buildings.SpecialServiceSettings" preserve="all" />
    <type fullname="Buildings.WholesaleStoreSettings" preserve="all" />
    <type fullname="BuildingSizeGoal" preserve="all" />
    <type fullname="BuildingTypeData" preserve="all" />
    <type fullname="BusinessType" preserve="all" />
    <type fullname="BusinessWeeklyIncomeGoal" preserve="all" />
    <type fullname="CarController" preserve="all" />
    <type fullname="CarFeatures" preserve="all" />
    <type fullname="CarRepairGoal" preserve="all" />
    <type fullname="CasinoBoatVisitGoal" preserve="all" />
    <type fullname="CasinoCustomer" preserve="all" />
    <type fullname="CasinoGameController" preserve="all" />
    <type fullname="CasinoWinGoal" preserve="all" />
    <type fullname="Character.AnimationObjectSpawner" preserve="all" />
    <type fullname="Character.AnimationTriggerEvents" preserve="all" />
    <type fullname="Characters.BoredAnimations" preserve="all" />
    <type fullname="CleaningStationController" preserve="all" />
    <type fullname="ComputerController" preserve="all" />
    <type fullname="Controllers.CashRegisterController" preserve="all" />
    <type fullname="Controllers.CoatCheckController" preserve="all" />
    <type fullname="Controllers.ConveyorBeltController" preserve="all" />
    <type fullname="Controllers.DJBoothController" preserve="all" />
    <type fullname="Controllers.FitnessPlanningBoardController" preserve="all" />
    <type fullname="Controllers.HairdresserChairController" preserve="all" />
    <type fullname="Controllers.InputStationController" preserve="all" />
    <type fullname="Controllers.IRSStationController" preserve="all" />
    <type fullname="Controllers.ItemWithTextController" preserve="all" />
    <type fullname="Controllers.MachineWithRecipeEntityController" preserve="all" />
    <type fullname="Controllers.OutputStationController" preserve="all" />
    <type fullname="Controllers.ProductionLineEntityController" preserve="all" />
    <type fullname="Controllers.ProductionLineEntityVariation" preserve="all" />
    <type fullname="Controllers.SeatController" preserve="all" />
    <type fullname="Controllers.SecurityGuardLockerController" preserve="all" />
    <type fullname="Controllers.ShowcaseVehicleController" preserve="all" />
    <type fullname="Controllers.SignController" preserve="all" />
    <type fullname="Controllers.StateItemController" preserve="all" />
    <type fullname="Controllers.TreadmillController" preserve="all" />
    <type fullname="Controllers.TurnstileBarrier" preserve="all" />
    <type fullname="DecorativeBookshelfHolderController" preserve="all" />
    <type fullname="DecorativeItemHolderController" preserve="all" />
    <type fullname="DeliverySpot" preserve="all" />
    <type fullname="DeliveryTruckDriverController" preserve="all" />
    <type fullname="DiscoLight" preserve="all" />
    <type fullname="DJBoothMusicPlayer" preserve="all" />
    <type fullname="DoorController" preserve="all" />
    <type fullname="DummyPedestrianSpawner" preserve="all" />
    <type fullname="EducationDoorController" preserve="all" />
    <type fullname="EmployeeMaxLevelGoal" preserve="all" />
    <type fullname="EmployeeStations.WaitingLine" preserve="all" />
    <type fullname="EmployeeStations.WaitingLineAnchor" preserve="all" />
    <type fullname="EmploymentGoal" preserve="all" />
    <type fullname="Entities.Employee.JobDemands.JobDemand" preserve="all" />
    <type fullname="FridgeController" preserve="all" />
    <type fullname="FullServiceCustomer" preserve="all" />
    <type fullname="Furniture.Requirements.HasItemTypeAttached" preserve="all" />
    <type fullname="Furniture.Requirements.IsNotDuplicated" preserve="all" />
    <type fullname="Furniture.Requirements.IsPlacedAtHeight" preserve="all" />
    <type fullname="Furniture.Requirements.IsPlacedInBusinessWithCustomerType" preserve="all" />
    <type fullname="Furniture.Requirements.IsPlacedInTheEntrance" preserve="all" />
    <type fullname="FurnitureStoreSettings" preserve="all" />
    <type fullname="GasPaidGoal" preserve="all" />
    <type fullname="GymCustomer" preserve="all" />
    <type fullname="HairdresserCustomer" preserve="all" />
    <type fullname="HandTruck" preserve="all" />
    <type fullname="HospitalizationGoal" preserve="all" />
    <type fullname="HumanHider" preserve="all" />
    <type fullname="IndoorLightController" preserve="all" />
    <type fullname="InteriorDesignerPaidGoal" preserve="all" />
    <type fullname="ItemController" preserve="all" />
    <type fullname="Items.SpecialItems.ShowcaseShelfController" preserve="all" />
    <type fullname="ItemsInStockGoal" preserve="all" />
    <type fullname="JobBoardController" preserve="all" />
    <type fullname="LightRotatorComponent" preserve="all" />
    <type fullname="LoudspeakerController" preserve="all" />
    <type fullname="MopController" preserve="all" />
    <type fullname="NightclubCustomer" preserve="all" />
    <type fullname="OutsideBenchController" preserve="all" />
    <type fullname="PalletController" preserve="all" />
    <type fullname="ParkingTicketGoal" preserve="all" />
    <type fullname="Pedestrian" preserve="all" />
    <type fullname="PlayerActivity.WorkoutExercise" preserve="all" />
    <type fullname="PlayerActivity.WorkoutGroup" preserve="all" />
    <type fullname="PlayerAgeGoal" preserve="all" />
    <type fullname="PreviewTerminalController" preserve="all" />
    <type fullname="Producer" preserve="all" />
    <type fullname="PublicShowerController" preserve="all" />
    <type fullname="QualitySettingsData" preserve="all" />
    <type fullname="RandomLightColor" preserve="all" />
    <type fullname="RandomVehicleColor" preserve="all" />
    <type fullname="RandomVehicleDirtiness" preserve="all" />
    <type fullname="RealEstateGoal" preserve="all" />
    <type fullname="RouletteEmployeeController" preserve="all" />
    <type fullname="ScooterController" preserve="all" />
    <type fullname="ScreenVideoController" preserve="all" />
    <type fullname="Seasons.ItemWithSeasonalVisualsController" preserve="all" />
    <type fullname="Seasons.SeasonalItemController" preserve="all" />
    <type fullname="SelfServiceCustomer" preserve="all" />
    <type fullname="SingleCoroutineStarterStopper" preserve="all" />
    <type fullname="SlotMachineController" preserve="all" />
    <type fullname="SpecialEmployeeController" preserve="all" />
    <type fullname="SpecialNpcData" preserve="all" />
    <type fullname="SpecialServices.Bank.BankSettings" preserve="all" />
    <type fullname="StepTrigger" preserve="all" />
    <type fullname="StorageShelfController" preserve="all" />
    <type fullname="TaxesPaidGoal" preserve="all" />
    <type fullname="TaxiController" preserve="all" />
    <type fullname="TaxiRideGoal" preserve="all" />
    <type fullname="ThirdPersonCharacter" preserve="all" />
    <type fullname="TotalDailyCustomerGoal" preserve="all" />
    <type fullname="TVController" preserve="all" />
    <type fullname="UI.Apps.Contacts.ContactPreset" preserve="all" />
    <type fullname="UI.Elements.ProgressBar" preserve="all" />
    <type fullname="UI.Load.LoadingScreen" preserve="all" />
    <type fullname="UI.Smartphone.Apps.Contacts.ContactCategory" preserve="all" />
    <type fullname="UiOrder" preserve="all" />
    <type fullname="UncleFredObjectivesGoal" preserve="all" />
    <type fullname="UniformLockerController" preserve="all" />
    <type fullname="ValuationGoal" preserve="all" />
    <type fullname="VehicleDeformationController" preserve="all" />
    <type fullname="VehicleGoal" preserve="all" />
    <type fullname="VehicleParkingHelper" preserve="all" />
    <type fullname="Vehicles.Components.VehicleBlinker" preserve="all" />
    <type fullname="Vehicles.Components.VehicleLightsToggle" preserve="all" />
    <type fullname="Vehicles.VehicleTypes.VehicleType" preserve="all" />
    <type fullname="VehicleSpawnerController" preserve="all" />
    <type fullname="WardrobeController" preserve="all" />
    <type fullname="WorkoutMachineController" preserve="all" />
    <type fullname="CustomerType" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.JobDemandName" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.CleanWorkplace" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.DaysWorkingPerWeek" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.FreeOnDays" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.HasHealthInsurance" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.HasItemInBuilding" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.HasNoShiftBetweenHours" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.HoursWorkingPerWeek" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.MinimumPlayerHappiness" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.NoSpecificShift" preserve="nothing" serialized="true" />
    <type fullname="Entities.Employee.JobDemands.Requirements.WorksOnItem" preserve="nothing" serialized="true" />
    <type fullname="Entities.HealthInsurancePlanType" preserve="nothing" serialized="true" />
    <type fullname="WorkShiftType" preserve="nothing" serialized="true" />
    <type fullname="Buildings.BuildingSize" preserve="nothing" serialized="true" />
    <type fullname="Buildings.BuildingType" preserve="nothing" serialized="true" />
    <type fullname="Buildings.NpcSpawnSettings" preserve="nothing" serialized="true" />
    <type fullname="EmployeePreset" preserve="nothing" serialized="true" />
    <type fullname="Entities.DummyAiData" preserve="nothing" serialized="true" />
    <type fullname="Entities.MarketingTypeName" preserve="nothing" serialized="true" />
    <type fullname="Enums.BusinessTypeName" preserve="nothing" serialized="true" />
    <type fullname="Job" preserve="nothing" serialized="true" />
    <type fullname="JobName" preserve="nothing" serialized="true" />
    <type fullname="LogoSettings" preserve="nothing" serialized="true" />
    <type fullname="OpeningHourSlot" preserve="nothing" serialized="true" />
    <type fullname="ScheduleDay" preserve="nothing" serialized="true" />
    <type fullname="SignAppearanceSettings" preserve="nothing" serialized="true" />
    <type fullname="VideoClipData/VideoType" preserve="nothing" serialized="true" />
    <type fullname="WorkShift" preserve="nothing" serialized="true" />
    <type fullname="Blueprints.DataElement" preserve="nothing" serialized="true" />
    <type fullname="BusinessProduct" preserve="nothing" serialized="true" />
    <type fullname="DayFactorMultiplier" preserve="nothing" serialized="true" />
    <type fullname="Entities.CustomerDemandSet" preserve="nothing" serialized="true" />
    <type fullname="Entities.CustomerDemandType" preserve="nothing" serialized="true" />
    <type fullname="HourlyFactorMultiplier" preserve="nothing" serialized="true" />
    <type fullname="RadioStation" preserve="nothing" serialized="true" />
    <type fullname="Vehicles.VehicleTypes.VehicleTypeName" preserve="nothing" serialized="true" />
    <type fullname="Buildings.BuildingTypes.Shared.BusinessRequirement.BusinessRequirementName" preserve="nothing" serialized="true" />
    <type fullname="UI.Tasks.QuestAffectingChange" preserve="nothing" serialized="true" />
    <type fullname="AI.Citizens.CitizenData" preserve="nothing" serialized="true" />
    <type fullname="CharacterData" preserve="nothing" serialized="true" />
    <type fullname="Controllers.PlayerItemPurchaserSettings" preserve="nothing" serialized="true" />
    <type fullname="DiplomaName" preserve="nothing" serialized="true" />
    <type fullname="EmployeeStations.WaitingLineTransforms" preserve="nothing" serialized="true" />
    <type fullname="Helpers.HappinessModifierType" preserve="nothing" serialized="true" />
    <type fullname="Order" preserve="nothing" serialized="true" />
    <type fullname="PlayerActivity.EntertainDevice" preserve="nothing" serialized="true" />
    <type fullname="PlayerActivity.RestEnvironment" preserve="nothing" serialized="true" />
    <type fullname="PlayerActivity.SleepEnvironment" preserve="nothing" serialized="true" />
    <type fullname="PlayerActivity.StudyDiploma" preserve="nothing" serialized="true" />
    <type fullname="SeatSpot" preserve="nothing" serialized="true" />
    <type fullname="SlotMachineController/SlotElementWeight" preserve="nothing" serialized="true" />
    <type fullname="VehicleDeformationController/VehicleDeformation" preserve="nothing" serialized="true" />
    <type fullname="VehicleDeformationController/VehicleDeformation/VehicleDeformationPoint" preserve="nothing" serialized="true" />
    <type fullname="VehicleInstance" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Rivals.RivalData" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Rivals.RivalTimeline" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Rivals.TimelineEntry" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions.Characters, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AI.Citizens.CitizenDataExamples" preserve="all" />
    <type fullname="BigAmbitions.Characters.Appearance.AppearanceElementVariant" preserve="all" />
    <type fullname="BigAmbitions.Characters.Skills.SkillData" preserve="all" />
    <type fullname="BigAmbitions.Characters.SkinColor" preserve="all" />
    <type fullname="Characters.EmojiSystem.CharacterEmoji" preserve="all" />
    <type fullname="AI.Citizens.CitizenDataExample" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Characters.Gender" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Characters.Skills.SkillName" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Characters.Appearance.AppearanceElementData" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Characters.Appearance.AppearanceElementType" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Characters.Appearance.AppearanceElementColor" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions.Factories, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BigAmbitions.Factories.ProductionLineVisualsController" preserve="all" />
    <type fullname="BigAmbitions.Factories.Recipes.Recipe" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.AnimationEffectComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.ConveyorMovementComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.MachineWithRecipeMovementComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.ParticleEffectComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.RoboticArmMovementComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.ShaderEffectComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.SoundEffectComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.SplitterMovementComponent" preserve="all" />
    <type fullname="BigAmbitions.Factories.Recipes.Recipe/RecipeColorShaderData" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Factories.Recipes.RecipeItem" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.ShaderEffectComponent/ShaderFloatData" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.TriggerAnimationEffect" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Factories.Visuals.Components.TriggerSoundEffect" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions.InteriorDesigner, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="InteriorMaterialPreset" preserve="all" />
    <type fullname="InteriorMaterialPreset/Variant" preserve="nothing" serialized="true" />
    <type fullname="InteriorMaterialPreset/Variant/MaterialData" preserve="nothing" serialized="true" />
    <type fullname="InteriorMaterialPreset/Variant/MaterialData/PropertyType" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions.Items, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BigAmbitions.Items.ComputerAttachmentPoint" preserve="all" />
    <type fullname="BigAmbitions.Items.DefaultAttachmentPoint" preserve="all" />
    <type fullname="BigAmbitions.Items.Item" preserve="all" />
    <type fullname="BigAmbitions.Items.SeatAttachmentPoint" preserve="all" />
    <type fullname="BigAmbitions.Items.WorkSurfaceAttachmentPoint" preserve="all" />
    <type fullname="BigAmbitions.Items.ItemName" preserve="nothing" serialized="true" />
    <type fullname="AttachmentPointType" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Items.CustomColorChannel" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Items.Item/ItemProducerSettings" preserve="nothing" serialized="true" />
    <type fullname="Seasons.ItemSeason" preserve="nothing" serialized="true" />
    <type fullname="StreetName" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Items.ItemInstance" preserve="nothing" serialized="true" />
    <type fullname="ShelfItem" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions.Neighborhoods, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BigAmbitions.Neighborhoods.NeighborhoodData" preserve="all" />
    <type fullname="BigAmbitions.Neighborhoods.Neighbourhood" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.Neighborhoods.IdealAvailableBuildingsInNeighborhood" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BigAmbitions.Seasons, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Seasons.Season" preserve="all" />
    <type fullname="Date" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="ExternalPlugins, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="GleyTrafficSystem.EngineSoundComponent" preserve="all" />
    <type fullname="GleyTrafficSystem.VehicleComponent" preserve="all" />
    <type fullname="GleyTrafficSystem.VehicleLightsComponent" preserve="all" />
    <type fullname="GleyUrbanAssets.VisibilityScript" preserve="all" />
    <type fullname="MTAssets.SkinnedMeshCombiner.SkinnedMeshCombiner" preserve="all" />
    <type fullname="NWH.Common.CoM.VariableCenterOfMass" preserve="all" />
    <type fullname="NWH.Common.Vehicles.FrictionPreset" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Damage.DamageHandler" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.GroundDetection.GroundDetectionPreset" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.GroundDetection.SurfacePreset" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.LOD" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Modules.ArcadeModule.ArcadeModuleWrapper" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Modules.FlipOver.FlipOverModuleWrapper" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Modules.Fuel.FuelModuleWrapper" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Modules.MotorcycleModule.MotorcycleModuleWrapper" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Modules.SpeedLimiter.SpeedLimiterModuleWrapper" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.TransmissionGearingProfile" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.StateSettings" preserve="all" />
    <type fullname="NWH.VehiclePhysics2.VehicleController" preserve="all" />
    <type fullname="NWH.WheelController3D.StandardGroundDetection" preserve="all" />
    <type fullname="NWH.WheelController3D.WheelController" preserve="all" />
    <type fullname="NWH.WheelController3D.WheelControllerManager" preserve="all" />
    <type fullname="VehicleNavMeshObstacleToggler" preserve="all" />
    <type fullname="GleyTrafficSystem.Wheel" preserve="nothing" serialized="true" />
    <type fullname="MTAssets.SkinnedMeshCombiner.SkinnedMeshCombiner/AllInOneParams" preserve="nothing" serialized="true" />
    <type fullname="MTAssets.SkinnedMeshCombiner.SkinnedMeshCombiner/JustMaterialColorsParams" preserve="nothing" serialized="true" />
    <type fullname="MTAssets.SkinnedMeshCombiner.SkinnedMeshCombiner/OneMeshPerMaterialParams" preserve="nothing" serialized="true" />
    <type fullname="MTAssets.SkinnedMeshCombiner.SkinnedMeshCombiner/OnlyAnima2dMeshes" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Brakes" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.EffectManager" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.ExhaustFlash" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.ExhaustSmoke" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.LightSource" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.LightsMananger" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.SkidmarkManager" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.SurfaceParticleManager" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Effects.VehicleLight" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.GroundDetection.GroundDetection" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.GroundDetection.SurfaceMap" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Input.VehicleInputHandler" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Modules.ArcadeModule.ArcadeModule" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Modules.FlipOver.FlipOverModule" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Modules.Fuel.FuelModule" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Modules.ModuleManager" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Modules.MotorcycleModule.MotorcycleModule" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Modules.SpeedLimiter.SpeedLimiterModule" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.ClutchComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.DifferentialComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.EngineComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.EngineComponent/ForcedInduction" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.Powertrain" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.TransmissionComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.Wheel.WheelGroup" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.Wheel.WheelGroupSelector" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Powertrain.WheelComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.AirBrakeComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.BlinkerComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.CrashComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.EngineFanComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.EngineRunningComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.EngineStartStopComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.ExhaustPopComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.GearChangeComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.HornComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.ReverseBeepComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.SuspensionBumpComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.TransmissionWhineComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.TurboFlutterComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.TurboWhistleComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.WheelSkidComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundComponents.WheelTireNoiseComponent" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Sound.SoundManager" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.StateDefinition" preserve="nothing" serialized="true" />
    <type fullname="NWH.VehiclePhysics2.Steering" preserve="nothing" serialized="true" />
    <type fullname="NWH.WheelController3D.Damper" preserve="nothing" serialized="true" />
    <type fullname="NWH.WheelController3D.Friction" preserve="nothing" serialized="true" />
    <type fullname="NWH.WheelController3D.Spring" preserve="nothing" serialized="true" />
    <type fullname="NWH.WheelController3D.Wheel" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="HGPlugins, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Localizor.LanguageChangeEvent.TextLocalizationComponent" preserve="all" />
    <type fullname="MultiAudioSource" preserve="all" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.AI.Navigation, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Unity.AI.Navigation.NavMeshModifier" preserve="all" />
  </assembly>
  <assembly fullname="Unity.Animation.Rigging, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animations.Rigging.BoneRenderer" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.Rig" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.RigBuilder" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.TwoBoneIKConstraint" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.RigEffectorData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Animations.Rigging.RigEffectorData/Style" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Animations.Rigging.RigLayer" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Animations.Rigging.TwoBoneIKConstraintData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.HighDefinition.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.HighDefinition.DecalProjector" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalLightData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalReflectionData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDRenderPipelineAsset" preserve="all" />
    <type fullname="UnityEngine.Rendering.GlobalXRSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.BoolScalableSetting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CookieAtlasGraphicsFormat" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CookieAtlasResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CubeReflectionResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FloatScalableSetting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GPUCacheSettingSRP" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalDecalSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalLightLoopSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalLightingQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalLowResolutionTransparencySettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalPostProcessSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalPostProcessingQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDShadowInitParameters" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDShadowInitParameters/HDShadowAtlasInitParams" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.IntScalableSetting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.LocalVolumetricFogResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteFrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteLightLoopSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.PlanarReflectionAtlasResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ReflectionAndPlanarProbeFormat" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ReflectionProbeTextureCacheResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.RenderPipelineSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.RenderPipelineSettings/LightSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.RenderPipelineSettings/PlanarReflectionAtlasResolutionScalableSetting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.RenderPipelineSettings/ReflectionProbeResolutionScalableSetting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ScalableSettingSchemaId" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.SkyResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.VirtualTexturingSettingsSRP" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.BoolScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/BufferClearing" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/Culling" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/Frustum" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/Volumes" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettingsOverride" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettingsOverrideMask" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDProbe/RenderData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.InfluenceVolume" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.IntScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteCaptureSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/CubeReflectionResolutionScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/Frustum" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/Lighting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/PlanarReflectionAtlasResolutionScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/ProxySettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettingsOverride" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProxyVolume" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshPro" preserve="all" />
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEditor.Audio.AudioMixerSnapshotController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AI.NavMeshAgent" preserve="all" />
    <type fullname="UnityEngine.AI.NavMeshObstacle" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Audio.AudioMixer" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="all" />
    <type fullname="UnityEngine.AudioClip" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ComputeShader" preserve="all" />
    <type fullname="UnityEngine.Cubemap" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightingSettings" preserve="all" />
    <type fullname="UnityEngine.LODGroup" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.ReflectionProbe" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Texture2DArray" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.Boolean]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.Single]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[UnityEngine.Collision]" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
    <type fullname="UnityEngine.ParticleSystem/MinMaxGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.PhysicMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.VFXModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.VFX.VFXRenderer" preserve="all" />
    <type fullname="UnityEngine.VFX.VisualEffect" preserve="all" />
    <type fullname="UnityEngine.VFX.VisualEffectAsset" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.VideoModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Video.VideoClip" preserve="all" />
  </assembly>
  <assembly fullname="BigAmbitions.PlacementSystem">
    <type fullname="GroundIndicator" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="DayNightCycle">
    <type fullname="BigAmbitions.DayNightCycle.DayOfWeekOrdered" preserve="nothing" serialized="true" />
    <type fullname="BigAmbitions.DayNightCycle.Timestamp" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="HGExtensions">
    <type fullname="Enums.Priority" preserve="nothing" serialized="true" />
    <type fullname="SerializableColor" preserve="nothing" serialized="true" />
    <type fullname="SerializableQuaternion" preserve="nothing" serialized="true" />
    <type fullname="SerializableVector3" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Core.Runtime">
    <type fullname="UnityEngine.Rendering.BitArray128" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.GlobalDynamicResolutionSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ProbeVolumeBlendingTextureMemoryBudget" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ProbeVolumeSHBands" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ProbeVolumeTextureMemoryBudget" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
</linker>