{"streetname_firstavenue": "1st Avenue", "streetname_eighthstreet": "8th Street", "streetname_fifthavenue": "5th Avenue", "streetname_fifthstreet": "5th Street", "streetname_firststreet": "1st Street", "streetname_fourthavenue": "4th Avenue", "streetname_fourthstreet": "4th Street", "streetname_ninthstreet": "9th Street", "streetname_secondavenue": "2nd Avenue", "streetname_secondstreet": "2nd Street", "streetname_seventhstreet": "7th Street", "streetname_sixthavenue": "6th Avenue", "streetname_sixthstreet": "6th Street", "streetname_tenthstreet": "10th Street", "streetname_thirdavenue": "3rd Avenue", "streetname_thirdstreet": "3rd Street", "businesstype_bank": "Bank", "businesstype_fastfoodrestaurant": "Fastfood Restaurant", "businesstype_giftshop": "<PERSON><PERSON><PERSON><PERSON>", "businesstype_supermarket": "Supermarkt", "businesstype_school": "School", "businesstype_autorepairshop": "Garage", "businesstype_clothingstore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "businesstype_cardealership": "Autodealer", "businesstype_appliancestore": "Elektronica winkel", "businesstype_wholesalestore": "Groothandel", "businesstype_recruitmentagency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "businesstype_furniturestore": "<PERSON><PERSON><PERSON> winkel", "common_subwaystation": "Metrostation", "businesstype_jewelrystore": "Juwelier", "streetname_broadwaystreet": "Broadway", "businesstype_coffeeshop": "Café", "businesstype_liquorstore": "<PERSON><PERSON>j<PERSON><PERSON><PERSON>", "businesstype_marketingagency": "Marketingbureau", "businesstype_officesupplystore": "Kantoorart<PERSON><PERSON> winkel", "businesstype_lawfirm": "Advocate<PERSON><PERSON><PERSON>", "businesstype_headquarters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "help_businesstype_giftshop_content": "**Cadeauwinkels** zijn detailhand<PERSON>bedrijven.\n\nKlanten zijn zelfzuchtig.\n\nHet bedrijf heeft de volgende meubels nodig om te kunnen functioneren:\n\n* [Stapel winkelmandjes](meubels-stapel winkelmandjes)\n* [<PERSON><PERSON>](meubels-itemgroep verkooppunt)\n* Ten minste één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen voornamelijk:\n\n* [<PERSON><PERSON> (Goedkoop)](producten-goedkoopcadeau)\n* [<PERSON><PERSON> (Duur)](producten-duurcadeau)\n* [Paraplu](producten-paraplu)\n\nEn kan daarnaast verkopen:\n\n* [Arty Fish Smartwatch](producten-smartwatch2)\n* [Bloem (Goedkoop)](producten-goedkopebloem)\n* [Bloem (Duur)](producten-duurbloem)\n* [Prentenboek](producten-prentenboek)\n* [Rhythm By Tre](producten-koptelefoon01)\n* [Frisdrank <PERSON>](producten-sodacan)\n* [ZanaMan Smartwatch](producten-smartwatch1)\n\nMedewerkers met de volgende vaardigheden kunnen worden toegewezen:\n\n* [Klantenservice](vaardigheid-klantenservice)\n* [Schoonmaak](vaardigheid-schoonmaken)", "common_business_types": "Bedrijfstypes", "common_sellable_products": "Verkoopbare producten", "help_itemname_stackofshoppingbaskets_content": "**Stapel winkelmandjes** zijn verp<PERSON>t in alle winkels waar de klanten hun producten zelf pakken.\n\n**Klantcapaciteit:** 30\n\nHet meubilair is te koop op de volgende locaties:\n* [Vierkante Apparaten](adres:16 4a)\n* [AJ Pederson & Zoon](adres:13 5a)\n* [Essentiële apparaten](adres:16 11s)", "help_itemname_cheapgift_content": "**Goedkoop cadeau** is een producttype dat voornamelijk in [Souvenirwinkels](businesstypes-giftshop) wordt verkocht.\n\nDaarnaast kan het ook verkocht worden via:\n \n* [B<PERSON><PERSON>enwinkels](businesstypes-florist)\n* [<PERSON><PERSON><PERSON>win<PERSON>](businesstypes-bookstore)\n\nHet product kan geplaatst worden in de volgende meubels:\n\n* [Ronde Stelling](furniture-roundedshelf)\n* [Productpaneel](furniture-productpanel)\n\nHet product kan gekocht worden op de volgende locaties:\n\n* [Metro Wholesale(address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nHet product kan geïmporteerd worden op de volgende locaties:\n\n* [JetCargo Imports](address: 1 pier)\n* [United Ocean Import](address: 3 pier)\n\nHet product kan gemaakt worden volgens het volgende recept:\n\n* [Goedkoop cadeau recept](recipes-cheapgiftrecipe", "help_itemname_roundedshelf_content": "**Rounded Shelf** kan worden gebruikt om volgende producten te verkopen:\n\n* [Goedko<PERSON> Cadeau](products-cheapgift)\n* [<PERSON><PERSON>](products-expensivegift)\n* [Goedkope Bloemen](products-cheapflower)\n* [Dure Bloemen](products-expensiveflower)\n\n**Productcapaciteit:**\n* Cadeaus: 200\n* Goedkope Bloemen: 40\n* Dure Bloemen: 25\n**Klantcapaciteit:** 15\n\nDe meubels zijn te koop op de volgende locaties:\n* [Square Appliances](address:16 4a)", "help_itemname_expensivegift_content": "**Duur <PERSON>au** is een type product dat voornamelijk word verkocht in [Cadeauwinkels](businesstypes-giftshop).\n\nOok kan het worden verkocht via [Boemisten](businesstypes-florist).\n\nHet product kan in de volgenden schappen geplaatst worden:\n* [Ronde Stelling](furniture-roundedshelf)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc](adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [United Ocean Import](adres: 3 pier)\n\nHet product kan gemaakt worden via de volgende recepten:\n* [Duur cadeau recept](recipes-expensivegiftrecipe)", "help_businesstype_coffeeshop_content": "**Cafés** zijn winkels.\n\nKlanten worden bediend door medewerkers.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [<PERSON><PERSON>](furniture-cashregister)\n* Minstens één product om te verkopen (see below)\n\nDit soort bedrijven verkopen voornamelijk:\n\n* [Croissant](products-croissant)\n* [Ko<PERSON><PERSON> koffie](products-cupofcoffee)\n* [Cupcake](products-cupcake)\n* [Donut](products-donut)\n\nEn kan daarnaast verkopen:\n\n* [Appel](products-apple)\n* [Banaan](products-banana)\n* [IJs](products-icecream)\n* [Salade](products-salad)\n* [Blikje met frisdrank](products-sodacan)\n **Opmerking:** E<PERSON> [staande digitale weegschaal] (furniture-standingdigitalscale) is vereist om groenten en fruit te verkopen.\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klanten bedienen](skill-customerservice)\n* [Schoon maken](skill-cleaning)", "help_businesstype_fastfoodrestaurant_content": "**Fastfoodrestaurants** zijn detailhandelszaken.\n\nKlanten worden bediend door medewerkers.\n\nHet bedrijf heeft het volgende meubilair nodig om te functioneren:\n\n* [<PERSON><PERSON>](furniture-cashregister)\n* Minstens één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen hoofdzakelijk:\n\n* [Burger](products-burger)\n* [Kips<PERSON><PERSON> Kabob](products-kabob)\n* [Frietjes](products-frenchfries)\n* [Hotdog](products-hotdog)\n* [IJs](products-icecream)\n* [Pizza](products-pizza)\n* [Salade](products-salad)\n* [Frisdrankblikje](products-sodacan)\n\nEn kunnen ook het volgende verkopen:\n\n* [A<PERSON>](products-apple)\n* [Banaan](products-banana)\n **Opmerking:** Een [Staande Digitale Weegschaal] (furniture-standingdigitalscale) is vereist om Groenten en Fruit te verkopen.\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klantenservice] (skill-customerservice)\n* [Schoonmaak] (skill-cleaning)", "help_businesstype_supermarket_content": "**Supermarkten** zijn detailhandelszaken.\n\nKlanten bedienen zichzelf.\n\nHet bedrijf heeft het volgende meubilair nodig om te functioneren:\n\n* [Stapel winkelmandjes] (furniture-stackofshoppingbaskets)\n* [Verkooppunt](furniture-itemgrouppointofsale)\n* Minstens één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen hoofdzakelijk:\n\n* [Vers Voedsel](products-freshfood)\n* [Diepvriesvoedsel](products-frozenfood)\n* [IJs](products-icecream)\n* [Frisdrankblikje](products-sodacan)\n\nEn kunnen ook het volgende verkopen:\n\n* [<PERSON><PERSON>](products-apple)\n* [Banaan](products-banana)\n* [Fles Wijn](products-bottleofwine)\n* [Wortel](products-carrot)\n* [Croissant](products-croissant)\n* [Cupcake](products-cupcake)\n* [Donut](products-donut)\n* [Salade](products-lettuce)\n* [Peer](products-pear)\n* [Tomaat](products-tomato)\n **Opmerking:** Een [Staande Digitale Weegschaal] (furniture-standingdigitalscale) is vereist om Fruit en Groenten te verkopen.\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klantenservice] (skill-customerservice)\n* [Schoonmaak] (skill-cleaning)", "help_businesstype_jewelrystore_content": "**Juwelierszaken** zijn detailhandelsbedrijven.\n\nKlanten bedienen zichzelf.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [Stapel winkelmandjes] (furniture-stackofshoppingbaskets)\n* [Verkooppunt](furniture-itemgrouppointofsale)\n* Minstens één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen voornamelijk:\n\n* [<PERSON><PERSON><PERSON> (goedkoop)](products-cheapjewelry)\n* [<PERSON><PERSON><PERSON> (duur)](products-expensivejewelry)\n* [Arty Fish Smartwatch] (products-smartwatch2)\n* [ZanaMan Smartwatch](products-smartwatch1)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klantenservice] (skill-customerservice)\n* [Reiniging] (skill-cleaning)", "help_businesstype_clothingstore_content": "**Kledingwinkels** zijn detailhandelsbedrijven. \n\nKlanten zijn zelfzuchtig.\n\nHet bedrijf heeft de volgende meubels nodig om te functioneren:\n\n* [Stapel winkelmandjes](furniture-stackofshoppingbaskets)\n* [Point of Sales](furniture-itemgrouppointofsale)\n* Ten minste één product om te verkopen (zie hieronder)\n\nBedrij<PERSON> van dit type verkopen voornamelijk:\n\n* [<PERSON><PERSON><PERSON> (Classic Cheap Female)](products-classiccheapfemaleclothing)\n* [<PERSON><PERSON><PERSON> (Classic Cheap Male)](products-classiccheapmaleclothing)\n* [Kleding (Classic Expensive Female)](products-classicexpensivefemaleclothing)\n* [Kled<PERSON> (Classic Expensive Male)](products-classicexpensivemaleclothing)\n* [Kleding (Modern Cheap Female)](products-moderncheapfemaleclothing)\n* [Kled<PERSON> (Modern Cheap Male)](products-moderncheapmaleclothing)\n* [Kleding (Modern Expensive Vrouwelijk)](products-modernexpensivefemaleclothing)\n* [Kleding (Modern Expensive Man)](products-modernexpensivemaleclothing)\n\nEn kan daarnaast verkopen:\n\n* [<PERSON><PERSON><PERSON> (Goedkoop)](products-cheapjewelry)\n* [<PERSON><PERSON><PERSON> (Duur)](products-expensivejewelry)\n\nMedewerkers met de volgende vaardigheden kunnen worden toegewezen:\n\n* [Klantenservice](skill-customerservice)\n* [Schoonmaken](skill-cleaning)", "help_businesstype_liquorstore_content": "**Slijterijen** zijn detailhandelszaken.\n\nKlanten bedienen zichzelf.\n\nHet bedrijf heeft het volgende meubilair nodig om te functioneren:\n\n* [Stapel winkelmandjes] (furniture-stackofshoppingbaskets)\n* [Verkooppunt](furniture-itemgrouppointofsale)\n* Minstens één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen hoofdzakelijk:\n\n* [Bier](products-beer)\n* [Fles Wijn](products-bottleofwine)\n* [Sigaar](products-cigar)\n* [Whisky](products-whisky)\n\nEn kunnen ook het volgende verkopen:\n\n* [Mar<PERSON>ita](products-margarita)\n* [Martin<PERSON>](products-martini)\n* [Frisdrankblikje](products-sodacan)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klantenservice] (skill-customerservice)\n* [Schoonmaak] (skill-cleaning)", "help_businesstype_lawfirm_content": "**Advocatenkantoren** zijn kanto<PERSON><PERSON>.\n\nKlanten worden digitaal geholpen en zijn niet fysiek aanwezig in de gebouwen.\n\nHet bedrijf heeft het volgende meubilair nodig om te functioneren:\n\n* [Computerwerkplek] (furniture-computerworkstation)\n\nBedrijven van dit type kunnen het volgende verkopen:\n\n* [Uurtarief advocaat](fees-hourlylawyerfee)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Advocaa<PERSON>](skill-lawyer)\n* [<PERSON><PERSON><PERSON><PERSON>ak] (skill-cleaning)", "help_businesstype_headquarters_content": "**Hoofdkantoor** zijn speciale bedrijven die in elk kantoorgebouw kunnen worden gevestigd.\n\nDit bedrijfstype bedient nooit klanten. In plaats daarvan kunnen alle operationele medewerkers van een organisatie worden geplaatst.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [Computerwerkstation] (meubel-computerwerkstation)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Inkoopagent](vaardigheidsinkoopagent)\n* [Logistiek Manager](vaardigheid-logistiekmanager)\n* [HR Manager](skill-hrmanager)\n* [Headhunter](skill-headhunter)\n* [Reiniging] (vaardigheidsreiniging)", "help_itemname_freshfood_content": "**Vers voedsel** is een type product dat voornamelijk wordt verkocht door [Supermarkten](businesstypes-supermarket).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Industriële koelkast](furniture-industrialfridge)\n\nHet product kan op de volgende locaties worden gekocht:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n* [Total Produce Trading](address:6 6a)\n\nHet product kan worden geïmporteerd vanuit de volgende locaties:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_frozenfood_content": "**Frozen Food** is een type product dat voornamelijk wordt verkocht door [Supermarkten](bedrijfstypes-supermarkt).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Kleine industriële vriezer](meubel-kleine industriële vriezer)\n* [Industriële vriezer](meubel-industriële vriezer)\n\nHet product kan op de volgende locaties worden gekocht:\n* [Metro Groothandel](adres:2 5a)\n* [NY Distro Inc](adres:37 1s)\n* [Totale handel in producten](adres:6 6a)\n\nHet product kan worden geïmporteerd vanuit de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)", "help_itemname_cheapjewelry_content": "**Goedkope Sieraden** is een producttype dat voornamelijk via [Juwelierszaken](businesstypes-jewelrystore) wordt verkocht.\n\nBovendien kan het via [Kledingwinkels](businesstypes-clothingstore) worden verkocht.\n\nHet product kan in het volgende meubel geplaatst worden:\n* [Si<PERSON>denvitrine] (furniture-jewelryfloorshowcase)\n\nHet product kan op de volgende locatie worden gekocht:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [JetCargo Imports] (address: 1 pier)\n* [United Ocean Import](address: 3 pier)", "help_itemname_expensivejewelry_content": "**Dure Sieraden** is een producttype dat voornamelijk via [Juwelierszaken](businesstypes-jewelrystore) wordt verkocht.\n\nBovendien kan het via [Kledingwinkels](businesstypes-clothingstore) worden verkocht.\n\nHet product kan in het volgende meubel geplaatst worden:\n* [Si<PERSON>denvitrine] (furniture-jewelryfloorshowcase)\n\nHet product kan op de volgende locatie worden gekocht:\n* [NY Distro Inc] (address:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locatie:\n* [United Ocean Import](address: 3 pier)", "help_itemname_pizza_content": "**Pizza** is een type product dat wordt verkocht door [Fastfoodrestaurant](businesstypes-fastfoodrestaurant).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Pizza Oven](furniture-pizzaoven)\n\nHet product kan worden gekocht bij de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan geïmporteerd worden vanuit de volgende locaties:\n* [SeaSide Internationals](address: 2 pier)\n", "help_itemname_frenchfries_content": "**Franse friet** is een type product dat wordt verkocht door [Fastfoodrestaurant](businesstypes-fastfoodrestaurant).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Industriële Friteuse Machine](furniture-industrialfryermachine)\n\nHet product kan worden gekocht bij de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan geïmporteerd worden vanuit de volgende locaties:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_burger_content": "**Burger** is een type product dat voornamelijk wordt verkocht door [Fastfoodrestaurants](bedrijfstypes-fastfoodrestaurant).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Industriële grill](meubel-industriële grill)\n\nHet product kan op de volgende locaties worden gekocht:\n* [Metro Groothandel](adres:2 5a)\n* [NY Distro Inc](adres:37 1s)\n\nHet product kan worden geïmporteerd vanuit de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)", "help_itemname_sodacan_content": "**Frisdrankblikje** is een type product dat voornamelijk wordt verkocht door [Fastfoodrestaurants](bedrijfstypes-fastfoodrestaurant) en [Supermarkten](bedrijfstypes-supermarkt).\n\nBovendien kan het worden verkocht via [Coffeeshops](businesstypes-coffeeshop) en [Elektronicawinkels](businesstypes-elektronicawinkel) en [Bloemisten](businesstypes-bloemist) en [Cadeauwinkels](businesstypes-cadeauwinkel) en [Slijterijen] (bedrijfstypes-slijterij) en [Nachtclubs](bedrijfstypes-nachtclub).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Drankjeskoelkast](meubel-drankkoelkast)\n* [Grote drankenkoelkast](meubel-grotedrankkoelkast)\n\nHet product kan op de volgende locaties worden gekocht:\n* [Metrogroothandel](adres:2 5a)\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [JetCargo Imports] (adres: 1 pier)\n* [SeaSide Internationals](adres: 2 pier)", "help_itemname_salad_content": "**Salade** is een type product dat voornamelijk wordt verkocht door [Fastfoodrestaurants](bedrijfstypes-fastfoodrestaurant).\n\nBovendien kan het worden verkocht via [Coffeeshops](bedrijfstypes-coffeeshop) en [Fruit- en groentewinkels](bedrijfstypes-fruit-en-groentewinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Saladebar](meubel-saladebar)\n\nHet product kan op de volgende locaties worden gekocht:\n* [Metrogroothandel](adres:2 5a)\n* [NY Distro Inc](adres:37 1s)\n* [Totale handel in producten](adres:6 6a)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)", "help_itemname_croissant_content": "**Croissant** is een type product dat voornamelijk wordt verkocht via [Coffeeshops](businesstypes-coffeeshop).\n\nBovendien kan het worden verkocht via [Supermarkten](bedrijfstypes-supermarkt).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Bakkerijvitrine](meubel-bakkerijvitrine)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)", "help_itemname_cupofcoffee_content": "**Kop koffie** is een type product dat wordt verkocht via [Coffee Shop](businesstypes-coffeeshop).\n\nHet product kan worden toegewezen aan de volgende meubels:\n* [Industriële koffiemachine](furniture-industrialcoffeemachine)\n\nHet product kan worden gekocht bij de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan geïmporteerd worden vanuit de volgende locaties:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_cigar_content": "**Sigaar** is een type product dat wordt verkocht via [Liquor Store](businesstypes-liquorstore).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Sigarenschap] (furniture-cigarshelf)\n\nHet product kan worden gekocht bij de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan geïmporteerd worden vanuit de volgende locaties:\n* [JetCargo Imports](address: 1 pier)", "help_itemname_bottleofwine_content": "**Fles wijn** is een type product dat voornamelijk wordt verkocht via [Slijterijen](businesstypes-slijterij).\n\nBovendien kan het worden verkocht via [Supermarkten](bedrijfstypes-supermarkt).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Wijnplank] (meubels-wijnplank)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc](adres:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [United Ocean Import](adres: 3 pier)", "help_itemname_paperbag_content": "**Paper Bag** is een type product dat wordt gebruikt door [Points of Sales](furniture-itemgrouppointofsale).\n\nDe papieren zak is noodzakelijk om bijna alle producten in elk bedrijf te kunnen verkopen.\n\nDit product kan op de volgende locaties worden gekocht:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nDit product kan worden geïmporteerd vanaf de volgende locaties:\n* [JetCargo Imports](address: 1 pier)\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_hotdog_content": "**Hotdog** is een type product dat wordt verkocht door [Fastfoodrestaurant](businesstypes-fastfoodrestaurant).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Hotdog Grill](furniture-hotdoggrill)\n\nHet product kan worden gekocht bij de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan geïmporteerd worden vanuit de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)", "help_itemname_industrialfryermachine_content": "**Industriële friteusemachine** kan worden gebruikt voor de verkoop van:\n\n* [frietjes](producten-frietjes)\n\n**Productcapaciteit:** 200\n**Klantcapaciteit:** 30\n\nHet meubilair is te koop op de volgende locaties:\n* [Vierkante Apparaten](adres:16 4a)", "help_itemname_hotdoggrill_content": "**Hotdog Grill** kan worden gebruikt om te verkopen:\n\n* [Hotdog](products-hotdog)\n\n**Productcapaciteit:** 100\n**Klantcapaciteit:** 30\n\n**Vereist:** [Kast](furniture-cabinet)\n\nDe meubels zijn te koop bij de volgende vestigingen:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_drinksfridge_content": "**Drankkoelkast** kan worden gebruikt voor de verkoop van:\n\n* [Frisdrankblikje](products-sodacan)\n\n**Productcapaciteit:** 120\n**Klantcapaciteit:** 20\n\nHet meubilair is te koop op de volgende locatie:\n* [Square Appliances](address:16 4a)", "help_itemname_industrialfridge_content": "**Industriële Koelkast** kan worden gebruikt voor de verkoop van:\n\n* [Vers Voedsel](products-freshfood)\n\n**Productcapaciteit:** 50\n**Klant Capaciteit:** 20\n\nDe meubels zijn te koop op de volgende locaties:\n* [Square Appliances](address:16 4a)\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_jewelryfloorshowcase_content": "**Sieradenvloershowcase** kan worden gebruikt voor de verkoop van:\n\n* [<PERSON><PERSON><PERSON> (goedkoop)](products-cheapjewelry)\n* [<PERSON><PERSON><PERSON> (duur)](products-expensivejewelry)\n\n**Productcapaciteit:** 50\n**Klantcapaciteit:** 15\n\nHet meubilair is te koop op de volgende locaties:\n* [AJ Pederson & Zoon](address:13 5a)", "help_itemname_industrialcoffeemachine_content": "**Industriële Koffiemachine** kan worden gebruikt voor de verkoop van:\n\n* [<PERSON><PERSON><PERSON>](products-cupofcoffee)\n\n**Productcapaciteit:** 300\n**Klant Capaciteit:** 30\n\n**Vereist:** [Kast](furniture-cabinet)\n\nMeubels zijn te koop bij de volgende vestigingen:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_bakeryshowcase_content": "**<PERSON><PERSON><PERSON><PERSON>** kan worden gebruikt om het volgende te verkopen:\n\n* [Croissant](products-croissant)\n* [Cupcake](products-cupcake)\n\nDe meubels zijn te koop op de volgende locaties:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_saladbar_content": "**Salade bar** kan worden gebruikt om te verkopen:\n\n* [Salade](producten-salade)\n\nDe meubels zijn te koop bij de volgende vestigingen:\n* [De Appliance Store] (adres: 16 4a)\n* [AJ Pederson & zoon](adres:13 5a)", "help_itemname_clothingrack_content": "**Kledingrek** kan worden gebruikt om te verkopen:\n\n* [Klassieke goedkope herenkleding](products-moderncheapmaleclothing)\n* [Klassieke goedkope dameskleding](products-moderncheapfemaleclothing)\n* [Moderne goedkope herenkleding](products-moderncheapmaleclothing)\n* [Moderne goedkope dameskleding](products-moderncheapfemaleclothing)\n* [Klassieke dure mannenkleding](products-modernexpensivemaleclothing)\n* [Klassieke dure dameskleding]products-modernexpensivefemaleclothing)\n* [Moderne dure mannenkleding](products-modernexpensivemaleclothing)\n* [Moderne dure dameskleding](products-modernexpensivefemaleclothing)\n\nDe meubels zijn te koop bij de volgende vestigingen:\n* [<PERSON> Apparaatwinkel] (address: 16 4a)", "uncle_fred_tutorial_1": "<PERSON><PERSON>, ik hoop dat je je beter voelt. <PERSON><PERSON> dan ook, ik spra<PERSON> met een vrien<PERSON> van <PERSON>, <PERSON>, hij is nogal een grote speler. Het komt erop neer: hij heeft een goedkoop appartement dat jij je waarschijnlijk kunt veroorloven. Het is niet veel, maar het zal wel lukken.", "uncle_fred_tutorial_2": "En ik heb ook wat geld op je bankrekening gestort, want ik wil zeker weten je wat te eten haalt. Ok<PERSON>. Beloof het me.", "uncle_fred_tutorial_3": "<PERSON>. <PERSON>k heb je eerste maand huur <PERSON>, maar dat is het dan ook, oke?, je moet naar buiten gaan en een baan zoeken. al is het maar iets. Je hebt nu gewoon geld nodig.", "uncle_fred_tutorial_4": "Goed gedaan! Je lijkt sprekend op je vader, volgens mij was hij jouw leeftijd toen hij zijn eerste onderneming startte. Als je wat extra geld nodig om iets te beginnen, dan heb ik een vriend bij Jensen Capital. <PERSON><PERSON><PERSON> naam is <PERSON>, dat is de persoon waarnaar je moet vragen. Vergeet hem niet de groeten te doen van je Oom <PERSON>, Ok?", "uncle_fred_tutorial_5": "Mooi! Je hebt de auto gevonden. Pas op voor parkeerboetes. <PERSON><PERSON><PERSON> ik al eerder zei, is het tijd om wat spullen op te halen voor je nieuwe winkel. Je kunt je auto gebruiken of blij<PERSON> rond<PERSON> met de steekwagens. Het is he<PERSON><PERSON> aan jou.", "uncle_fred_tutorial_6": "<PERSON><PERSON><PERSON><PERSON>, het begint nu op een echte winkel te lijken. Vervolgens moeten we wat cadeau's kopen om te verkopen. Voor nu moet je producten kopen bij de kleine groothandel, maar in de toekomst kun je betere deals krijgen door rechtstreeks vanuit de haven te importeren.", "uncle_fred_tutorial_7": "Goed werk, kiddo. Nu is het tijd om je bedrijf te openen en wat geld te gaan verdienen. Ik duim voor je.", "uncle_fred_tutorial_8": "<PERSON>u, we zijn al aan het hosselen en geld aan het stapelen. <PERSON><PERSON><PERSON> me, je vader zou zo trots zijn geweest. Ik denk dat het tijd is om je eerste werknemer in dienst te nemen. En vergeet niet die cursus te volgen bij de Business Administration School zodat je weet waar je mee bezig bent. Oké.", "uncle_fred_tutorial_9": "<PERSON>jk eens wie de leiding neemt. Het was een goede zet om die werknemer aan te nemen. Ik weet dat werknemers een groot deel van je inkomsten uitmaken, maar het geeft je ook extra tijd. Het wordt tijd om de vloeren schoon te maken. Oh, man, ze zijn behoorlijk smerig.", "uncle_fred_tutorial_free_car": "Je bent nu eigenaar van een cadeauwinkel, eh? <PERSON><PERSON>, heel goed. <PERSON><PERSON> goed. Nu moeten we boodschappen doen. We zullen wat meubels voor je halen en een paar mooie producten om daar te verkopen. En ik wilde ook vermelden, ik heb onlangs geïnvesteerd in een autodealer en we vonden een soort wrak van een auto in de werkplaats achterin, maar hij rijdt nog steeds. Het is niet veel, maar het is van jou als je het wilt. De sleutel ligt in het dashboardkastje.", "uncle_fred_tutorial_11": "<PERSON><PERSON><PERSON><PERSON>, zien er goed uit! Zorg ervoor dat je ze schoon houdt. Je wilt niet dat de klanten in een vuile winkel rondlopen, oké. Oké. Nu terug naar de zaken. Misschien is het tijd om op zoek te gaan naar andere mogelijkheden. Heb je toevallig gemerkt dat mensen, je klanten, op zoek zijn naar iets te drinken? Wat dacht je van een koelkast of zoiets met blikje<PERSON> frisdrank? Daar kun je wat extra geld mee verdienen.", "uncle_fred_tutorial_12": "<PERSON>ar ga je, jochie. <PERSON>lij<PERSON> naar je klanten luisteren en blijf verbeteren. Blijf verbeteren. <PERSON> trouwen<PERSON>, weet je, je bouwt geen heel imperium op met alleen een cadeauwinkel, toch? Ik denk dat je moet overwegen om een nieuw bedrijf te Starten. Dat gezegd te hebben, je wilt niet zomaar ergens tegenaan rennen. Je moet weten wat mensen willen. Daar is een app voor. MarketInsider-app. Ki<PERSON> waar mensen naar op zoek zijn.", "uncle_fred_tutorial_13": "<PERSON>u, je weet: in zaken doen we altijd onderzoek naar de markt voordat we erin gaan, oké? Ten slotte, voordat u uw volgende bedrijf begint, zorg ervoor dat u een gebouw vindt met een hoge verkeersindex. Het serveren van veelgevraagde producten in combinatie met een adres met veel voetgangers betekent een hoge omzet.", "uncle_fred_tutorial_14": "Je hebt nu twee bedrij<PERSON>, eh <PERSON><PERSON>. Ik denk dat het wel is wat de media een seriële ondernemer noemt. Heel mooi werk. Ik laat je voor nu gaan en ik zie je later wel, jochie.", "uncle_fred_tutorial_15": "Heb je een telefoontje van de bank gehad? Ze zullen wel geschokt zijn als ze zien hoe je rekening groeit. Je moet een mooier appartement zoeken en mooie meubels kopen. Je verdient het.", "uncle_fred_tutorial_16": "<PERSON><PERSON>, jongen. <PERSON><PERSON> heb gister<PERSON> met een goede vriend gesproken. <PERSON><PERSON><PERSON> is <PERSON>. Hij heeft een drive-in groothandel in het industriegebied. Ze verkopen spullen in grote stukken, maar veel goedkoper. Je moet een auto meenemen. Het is te ver om te lopen.", "uncle_fred_tutorial_17": "Mooi! Zorg ervoor dat de populariteit van al je bedrijven hoog blijft. Soms moet je je marketingbudget verhogen om dezelfde resultaten te behalen. De markt verandert elke dag.<br><br> Ik ga mijn vakantieverblijf in Costa Del Sol bezoeken. Als ik terugkom, hoop ik te zien dat je je dagelijkse inkomen hebt verhoogd. Het is tijd om je rijkdom naar een hoger niveau te tillen, maar we hebben eerst meer geld nodig!", "uncle_fred_tutorial_18": "<PERSON><PERSON>, kn<PERSON>, je moet mijn nieuwe villa in Marbella eens komen bekijken. We zullen ooit samen wat sangria drinken. Het is geweldig hier. Hoe dan ook, ik ben blij te zien dat je hebt ontdekt hoe je je winst kunt vergroten. Onze volgende stap is alles om de tuss<PERSON>, de groot<PERSON>elaar, uit te schakelen. We moeten onze producten zelf gaan importeren.", "uncle_fred_tutorial_19": "Gefeliciteerd met je nieuw Ho<PERSON>dkantoor! Hier zitten je topmedewerkers van je organisatie. <PERSON><PERSON>t moeten we beginnen met het <PERSON><PERSON><PERSON> van een Inkoopagent die ons helpt met het sluiten van Importcontracten.", "uncle_fred_tutorial_20": "Ahh, vooruitgang! Je eerste operationele werknemer werkt vanuit je gloednieuwe hoofdkantoor. Begint behoorlijk chic te klinken, huh? Nu moeten we je inkoper aan het werk zetten. Maar eerst hebben we een plek nodig om al je geïmporteerde producten op te slaan.", "uncle_fred_tutorial_21": "Dit is uitstekend. We gebruiken dit magazijn om je producten op te slaan tot ze in je winkels liggen. Daar komen we later op terug. Maar laten we nu beginnen met het bevo<PERSON><PERSON> van deze mooie nieuwe schappen.", "uncle_fred_tutorial_22": "Dus je magazijn zit gewoon vol met producten. We zijn bij het laatste deel, om dat spul uit je magazijn en in je winkels te krijgen. Dus laten we aan de slag gaan.", "uncle_fred_tutorial_23": "Je doet het geweldig, j<PERSON><PERSON>. <PERSON><PERSON> ben erg onder de indruk. <PERSON><PERSON><PERSON> nog, ik denk dat ik me een beetje moet terugtrekken, je meer ruimte moet geven, zodat je in je eentje verder kunt gaan met het opbouwen van je retailimperium.", "tutorial_1_objective_1": "<PERSON><PERSON> naar en huur <b>3rd Street 45</b>", "tutorial_1_objective_2": "<PERSON><PERSON>ap tot je energie vol is", "tutorial_2_objective_1": "<PERSON><PERSON> een <b>standaar<PERSON> koe<PERSON></b> bij de plaatselijke elektronicawinkel", "tutorial_2_objective_2": "Plaats de koelkast in je appartement", "tutorial_2_objective_3": "Ga naar de plaatselijke supermarkt en koop minimaal 3 eenheden vers voedsel", "tutorial_2_objective_4": "Bevoorraad je koelk<PERSON> met vers eten", "tutorial_2_objective_5": "Eet wat eten", "tutorial_3_objective_1": "Zoe<PERSON> een baan bij de plaatselijke supermarkt", "tutorial_3_objective_2": "<PERSON><PERSON><PERSON><PERSON> werken totdat je <b>$ 300</b> hebt verdi<PERSON> met je nieuwe baan", "tutorial_4_objective_1": "Sluit een lening van $ 15.000 af", "tutorial_4_objective_3": "Start een cadeauwinkel", "help_itemname_wineshelf_content": "**W<PERSON><PERSON>** kan worden gebruikt voor de verkoop van:\n\n* [<PERSON><PERSON> Wijn](products-bottleofwine)\n\n**Productcapaciteit:** 72\n**Klant Capaciteit:** 10\n\nMeubels zijn te koop bij de volgende locaties:\n* [AJ Pederson & Son](address:13 5a)", "tutorial_5_objective_1": "<PERSON>op een <u>toon<PERSON></u>, een <u>ka<PERSON></u>, een <u>stapel <PERSON></u> en een <u>ronde plank</u> bij de plaatselijke huishoudelijke apparatenwinkel. Tip: je kunt de steekwagen lenen", "tutorial_5_objective_2": "Plaats de toonbank met <PERSON><PERSON><PERSON> <PERSON> in je winkel", "tutorial_5_objective_3": "Plaats de stapel winkelmand<PERSON>s in uw winkel", "tutorial_5_objective_4": "Plaats de ronde plank in je winkel", "tutorial_6_objective_1": "Koop een <u>doos met go<PERSON><PERSON><PERSON> g<PERSON></u> en een <u>doos met papieren tassen</u> bij <b>Metro Wholesale</b> .", "tutorial_6_objective_2": "Vul de ronde plank met <b>go<PERSON><PERSON><PERSON> cadeaus</b>", "tutorial_6_objective_3": "<PERSON><PERSON> de kassa met <b>papieren zakken</b>", "tutorial_7_objective_1": "Open de winkel met <b>BizMan</b>", "tutorial_7_objective_2": "<PERSON><PERSON> met werken aan de <PERSON>ssa", "tutorial_7_objective_3": "Dr<PERSON><PERSON> het bedrijf voor 2 dagen", "tutorial_8_objective_1": "Volg de cursus \"Basismanagement\"", "tutorial_8_objective_2": "Start een wervingscampagne voor medewerkers van de <b>klantenservice</b> van <b>Anderson Recruitment Corp.</b>", "tutorial_8_objective_3": "Gebruik het <b>BizMan-schema</b> om de werknemer voor elke gewenste dag aan de kassa toe te wijzen.", "tutorial_9_objective_1": "<PERSON><PERSON> een schoonmaakstation bij Square Appliances", "tutorial_9_objective_2": "Plaats het schoonmaakstation in je cadeauwinkel", "tutorial_free_car_objective_1": "<PERSON>al de Honza Mimic op bij Uncle Fred's Car Dealership", "tutorial_11_objective_1": "Zorg voor $ 8.000 op je bankrekening (door te sparen of opnieuw met de bank te praten)", "tutorial_11_objective_2": "<PERSON><PERSON> een <b><PERSON><PERSON><PERSON><PERSON><PERSON></b> bij de plaatselijke apparaatwinkel", "tutorial_11_objective_3": "<PERSON><PERSON> een <b>do<PERSON> fris<PERSON></b> bij <b>Metro Wholesale</b>", "tutorial_11_objective_4": "Plaats de <b><PERSON><PERSON><PERSON><PERSON><PERSON></b> in je winkel", "tutorial_11_objective_5": "Vul de <b><PERSON><PERSON><PERSON><PERSON><PERSON></b> met <b>f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b>", "tutorial_13_objective_1": "<PERSON><PERSON> en huur een wink<PERSON>pand met een verkeersindex van minimaal {trafficIndex} en stel het bedrijfstype in op 'Fastfoodrestaurant'", "tutorial_14_objective_1": "<PERSON><PERSON> een dageli<PERSON><PERSON> winst van {moneyAmount} per bedrijf", "tutorial_15_objective_1": "Bezoek de drive-in winkel", "tutorial_16_objective_1": "Voer een marketingcampagne uit voor een van uw bedrijven", "tutorial_16_objective_2": "Bereik een marketingniveau van 70% voor een van uw bedrijven", "tutorial_17_objective_1": "<PERSON><PERSON> in één dag een totale winst van {moneyAmount}", "tutorial_18_objective_1": "<PERSON><PERSON> een klein kantoorgebouw", "tutorial_18_objective_2": "Open een hoofd<PERSON><PERSON><PERSON> in je nieuwe kantoorgebouw", "tutorial_19_objective_1": "<PERSON><PERSON> een inkoper in", "tutorial_19_objective_2": "<PERSON>op een <u>computer</u> , een <u>bureau</u> en een <u>stoel</u> bij <PERSON><PERSON>&#39;s <PERSON><PERSON><PERSON>", "tutorial_19_objective_3": "Plaats het bureau in uw hoofdkantoor en bevestig de computer en de stoel aan het bureau", "tutorial_19_objective_4": "<PERSON>i<PERSON><PERSON> de <PERSON> toe aan het werkstation", "tutorial_20_objective_1": "<PERSON><PERSON> een klein magazijn", "tutorial_20_objective_2": "Koop minimaal één palletplank", "tutorial_20_objective_3": "Plaats palletplank in uw magazijn", "tutorial_21_objective_1": "Zet een importpartnerschap op met JetCargo Imports", "tutorial_21_objective_2": "Bestel een lading van minimaal 1000 goedkope geschenken en 600 blikjes frisdrank via het scherm Inkoopagent van uw hoofdkantoor (BizMan)", "tutorial_21_objective_3": "Controleer na ontvangst van de geïmporteerde goederen of er 1000 goedkope geschenken en 600 frisdrankblikjes in uw magazijn zijn", "tutorial_22_objective_1": "<PERSON><PERSON> een UMC Desert busje bij de Truckdealer", "tutorial_22_objective_2": "Rijd het busje naar je magazijn en wijs het toe aan een plek", "tutorial_22_objective_3": "<PERSON><PERSON> een logistiek manager in", "tutorial_22_objective_4": "<PERSON>ur een bezorger in", "tutorial_22_objective_5": "<PERSON>i<PERSON><PERSON> de Logistiek Manager toe aan een leeg werkstation in uw hoofdkantoor (kan worden gekocht bij Mr<PERSON> Scott's Office Supplies)", "tutorial_22_objective_6": "<PERSON>i<PERSON><PERSON> de bezorger toe aan de bestelbus in uw magazijn", "tutorial_22_objective_7": "<PERSON>el uw cadeauwinkel in als bestemming in het Logistiek Manager-scher<PERSON> van uw hoofdkantoor (BizMan)", "tutorial_22_objective_8": "<PERSON><PERSON> de minimale voor<PERSON><PERSON> van uw cadeauwinkel in op minimaal 200 goedkope cadeaus en 100 blikjes frisdrank", "tutorial_23_objective_1": "Wacht op nieuwe doelstellingen in de volgende bèta.", "uncle_freds_car": "De auto van oom Fred", "streetname_pier": "Pier", "streetname_thirdandahalfavenue": "Third and a half Avenue ", "help_businesstype_webdevelopmentagency_content": "**Webontwikkelingsbureaus** zijn kantoorbedrijven.\n\nKlanten worden digitaal afgehandeld en zijn niet fysiek aanwezig in de gebouwen.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [Computerwerkstation] (meubel-computerwerkstation)\n\nBedrijven van dit type kunnen verkopen:\n\n* [Uurtarief voor programmeur](kosten-programmeertarief per uur)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Programmeur] (vaardigheid-programmeur)\n* [Reiniging] (vaardigheidsreiniging)", "help_itemname_computer_content": "**Computer** kan worden gebruikt om een [Computer Workstation] (furniture-computerworkstation) in te richten.\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_requirement_desk_content": "**Bureaus** kunnen worden gebruikt om een werkplek in te richten voor kantoormedewerkers.\nHet vereist een [<PERSON><PERSON><PERSON>](meubelstoel) en een [Computer](meubelcomputer).\n\nEr zijn verschillende soorten bureaus:\n* Standaard bureau\n* Directiebureau\n* Standaard tafel\n\nDe meubels zijn te koop op de volgende locaties:\n* [Dhr. Scott's Office Supplies](adres:11 1a)\n* [Ika Bohag](adres:50 4s)\n* [Lux Concept](adres:68 5a)", "help_requirement_chair_content": "**St<PERSON>len** kunnen worden gebruikt om een werkplek in te richten voor kantoormedewerkers.\nHet vereist een [Bureau](meubel-bureau) en een [Computer](meubel-computer).\n\nEr zijn verschillende soorten stoelen:\n* Bureaustoel\n* Stump Mesh-bureaustoel\n* Gewone stoel\n* Multifunctionele stoel\n* Sommerhus fauteuil\n* Gammel Armstoel\n* Gaming-stoel\n\nDe meubels zijn te koop op de volgende locaties:\n* [Dhr. <PERSON>'s Office Supplies](adres:11 1a)\n* [Lux Concept](adres:68 5a)", "help_itemname_cleaningstation_content": "**Schoonmaakstation** is een speciaal *werkstation* waarvoor medewerkers met **Schoonmaak**-v<PERSON><PERSON><PERSON><PERSON><PERSON> nodig zijn.\n\nDe meubels zijn te koop bij de volgende vestigingen:\n* [Apparaat Winkel] (address: 16 4a)", "common_day": "<PERSON><PERSON>", "common_back": "Terug", "menu_continue": "Spel <PERSON>", "menu_start_new_game": "Nieuw spel starten", "menu_forum_login": "Inloggen op het forum", "menu_forum_signup": "Registreren voor het forum", "menu_report_bug": "<PERSON>ug melden", "menu_exit_to_desktop": "Afsluiten naar bureaublad", "menu_news_updates": "Nieuws & Updates", "menu_until_next_release": "tot de volgende release", "menu_days_next_release": "Dagen", "menu_discover_roadmap": "Ontdek het stappenplan", "menu_more_hg_headline": "<PERSON><PERSON>", "menu_more_hg_description": "Startup Company is een bedrijfssimulatiespel. Bouw je eigen website en neem het op tegen grote techreuzen!", "menu_more_hg_check_out_on_steam": "Bekijk het op steam", "menu_more_hg_follow_us_sm": "Volg ons op sociale media", "menu_load_game": "Laad Spel", "menu_load_game_show_recover_saves": "Toon herstelde opgeslagbestanden", "menu_load_game_delete_character": "Verwij<PERSON> karak<PERSON>", "menu_load_game_save_files": "Opgeslagen bestanden", "menu_load_game_recover_saves": "Herstel opgeslagen spel", "menu_load_game_delete_save": "Verwijder opgeslagen spel", "common_day_format": "{day} {dayNumber}", "menu_options": "Opties", "menu_options_graphics": "Grafische Instellingen", "menu_options_audio": "Audio", "menu_options_controls": "Bediening", "menu_options_others": "<PERSON><PERSON>", "menu_options_graphics_fullscreen": "Volledig scherm", "menu_options_graphics_resolution": "Resolutie", "menu_options_graphics_fps_limit": "FPS-limiet", "menu_options_graphics_gfx_quality": "Gfx-kwaliteit", "menu_options_graphics_aa": "Anti-Aliasing", "menu_options_audio_global_volume": "Algemeen volume", "menu_options_controls_invert_rotation": "Rotatie omkeren", "menu_options_others_language": "Taal", "menu_options_audio_radio_volume": "Radiovolume", "menu_options_audio_menumusic_volume": "Menu muziek volume", "citymap_map_filters": "kaartfilters", "citymap_category_special_buildings": "<PERSON>e geb<PERSON>wen", "citymap_category_rentable": "Geb<PERSON>wen te huur", "citymap_category_active_businesses": "<PERSON><PERSON><PERSON>", "tutorial_objectives": "<PERSON><PERSON><PERSON>", "tutorial_upcoming_tasks": "Aankomende taken", "tutorial_todotask_lowstock": "<u>{businessname}</u>'s {producer_itemname} heeft binnenkort geen <u>{itemname}</u> meer!", "tutorial_todotask_emptystock": "<u>{businessname}</u>'s {producer_itemname} heeft geen <u>{itemname}</u> meer!", "tutorial_todotask_missingemployee": "<u>{businessname}</u> is open, maar er is geen medewerker bij <u>{itemname}</u>.", "tutorial_todotask_missingrequireditem": "<u>{businessname}</u> vereist ten minste één <u>{itemname}</u>.", "tutorial_todotask_missingschedule": "<u>{businessname}</u> heeft geen ingestelde openingstijden.", "tutorial_todotask_noproducers": "<u>{businessname}</u> heeft geen producten om te verkopen.", "tutorial_todotask_dirtyfloors": "<u>{businessname}</u> is vuil en moet worden schoongemaakt.", "tutorial_todotask_employeeunassigned": "<u>{employeename}</u> is momenteel niet toegewezen aan een bedrijf.", "tutorial_todotask_employeeidle": "<u>{employeename}</u> heeft momenteel geen taken toegewezen.", "tutorial_message_from_uncle_fred": "be<PERSON><PERSON> van oom fred", "common_monday": "<PERSON><PERSON><PERSON>", "common_tuesday": "Dinsdag", "common_wednesday": "Woensdag", "common_thursday": "Donderdag", "common_friday": "Vrijdag", "common_saturday": "Zaterdag", "common_sunday": "Zondag", "topbar_date_format": "{DayOfWeek} (Dag {CurrentNumberDay})", "common_cancel": "<PERSON><PERSON><PERSON>", "common_today": "Vandaag", "common_tomorrow": "<PERSON><PERSON>", "common_setdestination": "Bestemming instellen", "playerhud_currentjob_quit_job": "<PERSON><PERSON>", "playerhud_currentjob_finished": "<PERSON><PERSON><PERSON>", "playerhud_currentjob_day_off": "<PERSON><PERSON><PERSON> dag", "jobname_undefined": "<PERSON><PERSON>", "jobname_cashier": "<PERSON><PERSON><PERSON>", "itempanelui_sell": "Verkopen", "itempanelui_maintenance_condition": "Staa<PERSON>", "itempanelui_vehicle_fuel": "<PERSON><PERSON><PERSON>", "itempanelui_vehicle_parkingzone": "Parkeer zone", "itempanelui_parkingzone_legal": "TOEGESTAAN (GRATIS)", "itempanelui_parkingzone_illegal": "VERBODEN", "itempanelui_parkingzone_notavailable": "-", "itempanelui_unpaid_total": "Totaal Onbetaald", "itempanelui_buttons_drop_pallet": "<PERSON><PERSON><PERSON>", "itempanelui_buttons_sleep": "<PERSON><PERSON><PERSON>", "itempanelui_buttons_auto_park": "Auto Park", "itempanelui_buttons_park_vehicle": "Parkeer voert<PERSON>g", "itempanelui_buttons_grab": "<PERSON><PERSON><PERSON><PERSON>", "itempanelui_buttons_place": "Plaats", "itempanelui_buttons_discard": "<PERSON><PERSON><PERSON><PERSON>", "itempanelui_buttons_leave": "<PERSON><PERSON><PERSON>", "itempanelui_buttons_pack": "Inpakken", "sleeppanel_headline": "<PERSON>a slapen", "sleeppanel_start_sleeping": "<PERSON><PERSON> met slapen", "sleeppanel_waking_up_at": "<PERSON><PERSON><PERSON> worden om {time}", "joboffer_wage_per_hour": "Loon per uur", "studypanel_start_studying": "<PERSON><PERSON> met <PERSON><PERSON><PERSON>", "studypanel_study_for": "<PERSON><PERSON><PERSON> voor {time} uur", "studypanel_ends_at": "<PERSON><PERSON><PERSON> om {time}", "diplomaname_basicmanagement": "Basis Management", "diplomaname_fundamentalbusinessadministration": "<PERSON>sis <PERSON>lijke Administratie", "managecargo_unloadforklift": "Heftruck lossen", "itemname_undefined": "<PERSON><PERSON>", "itemname_pizzaoven": "Pizza oven", "itemname_hotdog": "Hotdog", "itemname_sodacan": "<PERSON><PERSON> Fr<PERSON>drank", "itemname_closedcardboardbox": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> doos", "itemname_pizza": "Pizza", "itemname_industrialfryermachine": "Industriële friteuse", "itemname_roundedshelf": "Afgeronde plank", "itemname_standardfridge": "Standaard koelkast", "itemname_bed1": "Standaard bed", "itemname_smallcabinet": "<PERSON><PERSON> kast", "itemname_hotdoggrill": "Hotdoggrill", "itemname_paperbag": "<PERSON><PERSON><PERSON> zak", "itemname_floorlamp": "Vloerlamp", "itemname_recyclebin": "Prullenbak", "itemname_cleaningstation": "Reinigingsstation", "itemname_kingsizebed": "Kingsize bed", "itemname_industrialgrill": "Industriële Grill", "itemname_burger": "<PERSON><PERSON>", "itemname_expensivegift": "<PERSON><PERSON> cadeau", "itemname_drinksfridge": "Dranken <PERSON>kast", "itemname_frenchfries": "Franse frie<PERSON>s", "itemname_regularchair": "gewone stoel", "itemname_coffeetable1": "Moderne koffie tafel", "itemname_storageshelf": "Opbergplank", "itemname_freshfood": "Vers voedsel", "itemname_coffeetable2": "<PERSON>e koffie tafel", "itemname_industrialfridge": "Industriële k<PERSON>lkast", "itemname_frozenfood": "Diepvries voedsel", "itemname_coffeetable3": "Dubbelzijdige koffie tafel", "itemname_multipurposechair": "Multifunctionele stoel", "itemname_computer": "Computer", "itemname_officedesk1": "Standaard bureau", "itemname_table1": "<PERSON>aar<PERSON> tafel", "itemname_cashregister": "<PERSON><PERSON>", "itemname_stackofshoppingbaskets": "<PERSON><PERSON><PERSON>", "itemname_shoppingbasket": "Winkelmandje", "itemname_hourlyprogrammerfee": "Programmeur uurloon", "itemname_sofa1": "Bank 1", "itemname_jewelryfloorshowcase": "Sieraden Etalage", "itemname_cheapjewelry": "Goedkope si<PERSON>", "itemname_expensivejewelry": "<PERSON>re si<PERSON>den", "itemname_croissant": "Croissant", "itemname_cupofcoffee": "<PERSON><PERSON> koffie", "itemname_pottedplant1": "Hoge potplant", "itemname_cheapgift": "Goedkoop cadeau", "itemname_industrialcoffeemachine": "Industriële koffiemachine", "itemname_classiccheapmaleclothing": "<PERSON><PERSON><PERSON><PERSON> goedkope herenkleding", "itemname_bakeryshowcase": "<PERSON><PERSON><PERSON><PERSON>", "itemname_mop": "Dweil", "itemname_saladbar": "Salade bar", "itemname_salad": "Salade", "itemname_counter1": "<PERSON><PERSON> met lades", "itemname_smallindustrialfreezer": "Kleine industriële vriezer", "itemname_officechair": "Bureaustoel", "itemname_palletshelf": "Palletplank", "itemname_pallet": "<PERSON><PERSON><PERSON>", "itemname_clothingrack": "<PERSON><PERSON><PERSON><PERSON>", "itemname_classiccheapfemaleclothing": "Klassieke goedkope dameskleding", "itemname_cigar": "<PERSON><PERSON><PERSON>", "itemname_moderncheapmaleclothing": "Moderne goedkope herenkleding", "itemname_bottleofwine": "<PERSON><PERSON> wijn", "itemname_moderncheapfemaleclothing": "Moderne goedkope dameskleding", "itemname_wineshelf": "<PERSON><PERSON><PERSON><PERSON>", "itemname_classicexpensivemaleclothing": "Klassieke dure mannen<PERSON>ing", "itemname_classicexpensivefemaleclothing": "Klassieke dure dameskleding", "itemname_modernexpensivemaleclothing": "Moderne dure mannen<PERSON>ing", "itemname_itemgrouppointofsale": "<PERSON><PERSON> of toog", "itemname_modernexpensivefemaleclothing": "Moderne dure dameskleding", "itemname_handtruck": "Steekwagen", "itemname_itemgroupdesktopworkstation": "Desktop-werkstation", "itemname_hourlylawyerfee": "Advocaat <PERSON>", "itemname_trashbin": "Prullenbak", "itemname_wallmountedtv": "ELGE 8467NW-43283833 43\" TV", "itemname_painting40x30": "Schilderij 40x30", "itemname_painting30x30": "Schilderij 30x30", "itemname_painting50x70": "Schilderij 50x70", "itemname_pictureframe": "Fotolijst", "itemname_wallclock": "Wandklok", "itemname_productpanel": "Productpaneel", "itemname_ceilinglampround": "Plafond<PERSON>", "itemname_ceilinglampsquare": "Plafondlamp Vierkant", "itemname_ceilinglamptube": "Plafond<PERSON>", "itemname_counter2": "<PERSON><PERSON> met <PERSON><PERSON>", "itemname_countercorner": "Hoek kast", "itemname_counterleftend": "<PERSON><PERSON><PERSON><PERSON><PERSON> (links)", "itemname_counterrightend": "Kastuiteinde (rechts)", "itemname_modularsofa1l": "Modulaire bank 1 L", "itemname_modularsofa1r": "Modulaire bank 1 R", "itemname_modularsofa1m": "Modulaire bank 1 M", "itemname_armchair1": "Sommerhus fauteuil", "itemname_armchair2": "<PERSON><PERSON><PERSON>", "itemname_changingroom": "Kleedkamer", "itemoverlay_packed": " (Ingepakt)", "vehicletypename_umcdesert": "UMC Desert", "vehicletypename_handtruck": "Steekwagen", "vehicletypename_honzamimic": "Honza Mimic", "vehicletypename_forklift": "<PERSON><PERSON>ru<PERSON>", "vehicletypename_missamvillian": "Luisteren L6", "vehicletypename_mersaidis500": "Mersaidi S500", "vehicletypename_freighttruckt1": "Vrachtwagen T1", "vehicletypename_ferdinand112": "<PERSON> 112", "common_cargo_capacity": "Laadcapaciteit:", "common_purchase": "Aankoop", "common_open": "Open", "common_closed": "Gesloten", "open_in_bizman": "Openen in BizMan", "interior_designer": "Interieur ontwerper", "purchaseui_totalprice": "Totale prijs", "purchaseui_place_order": "Plaats bestelling", "sign_appearance": "<PERSON><PERSON>", "sign_appearance_sign_type": "Bordtype", "sign_appearance_sign_light_color": "<PERSON><PERSON> licht kleur", "sign_appearance_lamp_color": "<PERSON><PERSON><PERSON><PERSON>", "common_save_changes": "Wijzigingen Opslaan", "common_placeholder": "{data}", "menu_save": "Opsla<PERSON>", "menu_main_menu": "Hoofdmenu", "subwaystation_price_per_ride": "<b>{price}</b> per rit", "buildingpreview_cancel_preview": "Voorbeeld annuleren", "smartphone_persona": "<PERSON>a", "smartphone_myemployees": "<PERSON>jn me<PERSON>wer<PERSON>", "smartphone_bizman": "BizMan", "smartphone_econoview": "EconoView", "smartphone_vooglemaps": "Voogle Maps", "smartphone_marketinsider": "MarketInsider", "buildingresume_right_click": "Rechtermuisklik voor opties", "buildingresume_left_click": "Linkermuisklik om naar binnen te gaan", "buildingresume_change_sign": "W<PERSON><PERSON>zig bord uiterlijk", "buildingresume_open_in_bizman": "Openen in BizMan", "buildingresume_neighborhood": "Buurt: {neighbourhood}", "buildingresume_traffic_index": "Verkeersindex: {index}", "buildingresume_closed_opening_in_hours": "<br>Gaat open in {hours} uur ({time})", "buildingresume_closed_opening_in_hour": "<br>Gaat open in {hours} uur ({time})", "buildingresume_open_closing_in_hours": "<br>sluit over {hours} uur ({time})", "buildingresume_open_closing_in_hour": "<br>sluit over {hours} uur ({time})", "buildingresume_occupied": "ver<PERSON><PERSON><PERSON>", "buildingresume_rented_by_you": "Gehuurd door jou", "common_available_for_rent": "Te huur", "ownerdescription_government_building": "Overheidsgebouw", "ownerdescription_privately_owned": "Privebezit", "ownerdescription_owned_by": "Eigendom van {corporation}", "personalgoal_achieved": "Persoonlijk doel bereikt", "personalgoal_minemployees": "Neem minimaal {minimumEmployees} mensen in dienst", "personalgoal_reach_valuation": "Be<PERSON><PERSON> een waardering van {valuation}", "personalgoal_rent_apartment": "Huur een {size} appartement", "personalgoal_run_businesses": "Bezit {amount} succesvolle {type} bed<PERSON><PERSON><PERSON>", "personalgoal_own_vehicle_worth": "Bezit een voertuig ter waarde van {price}", "daily_summary": "Dag {day} <PERSON><PERSON><PERSON><PERSON>", "total_profit": "Totale winst", "daily_summary_businesses": "<PERSON><PERSON><PERSON><PERSON>", "common_confirm": "Bevestigen", "hud_confirm_are_you_sure": "Weet je het zeker?", "bizman_presentation_hud_confirm_terminate_contract": "U ontvangt de volledige aanbetaling en meubels worden automatisch verkocht en aan uw rekening toegevoegd", "colorlist_hud_confirm_remove_color": "Weet je zeker dat je deze kleur wilt verwijderen?", "hud_confirm_discard_item": "Weet je zeker dat je dit item permanent wilt vernietigen?", "itempanelui_hud_confirm_sellvehicle": "Weet u zeker dat u {type} wilt verkopen voor {price}?", "itempanelui_hud_confirm_sellitem": "Weet u zeker dat u {type} wilt verkopen voor {price}?", "mini_menu_hud_confirm_save_game_exists": "<PERSON>r bestaat al een savegame met de naam <b>\"{name}\"</b>. Wil je het overschrijven?", "mini_menu_hud_confirm_unsaved_changes_menu": "Weet je zeker dat je naar het hoofdmenu wilt gaan? <b>Alle niet-opgeslagen voortgang gaat verloren!</b>", "mini_menu_hud_confirm_unsaved_changes_desktop": "Weet u zeker dat u wilt stoppen en naar het bureaublad gaan? <b>Alle niet-opgeslagen voortgang gaat verloren!</b>", "bizman_marketing_hud_confirm_cancel_campain": "Wil je deze campagne echt annuleren?", "interior_designer_wall": "<PERSON><PERSON>", "interior_designer_floor": "V<PERSON>er", "character_customization_notification_name_required": "<PERSON><PERSON> ve<PERSON>t", "carcontroller_notification_warehouse_vehicle_assigned_slot": "{type} is toe<PERSON><PERSON><PERSON> aan plek {index}", "personal_goal_notification_personal_goal_required_to_enter_building": "Persoon<PERSON>jk doel '{goal}' vereist om toegang te krijgen tot dit gebouw", "bed_notification_cant_use_with_vehicle": "Je kunt een bed niet gebruiken terwijl je een {vehicle} gebruikt!", "businessemployeecontroller_notification_cant_use_with_vehicle": "Je kunt niet zitten tijdens het gebruik van een {vehicle}", "cashregister_notification_forklift_buy": "U kunt alleen dozen kopen die in uw handen, op een steekwagen of in de kofferbak van uw auto liggen.", "exitzonedespawner_notification_cant_leave_without_paying": "U dient uw artikelen voor vertrek te betalen.", "exitzonedespawner_notification_cant_leave_with_while_in_repairmode": "Je kunt het gebouw niet verlaten terwijl je een item van dit type vasthoudt.", "exitzonedespawner_notification_cant_leave_with_forklift": "Met dit type voertuig kunt u het gebouw niet verlaten.", "fridge_notification_empty_hands_to_empty": "Je hebt lege handen nodig om de {itemname} te legen", "itemcontroller_notification_empty_hands_to_empty": "Je handen moeten vrij zijn om de inhoud te verwijderen", "itemcontroller_notification_not_in_placementmode_to_empty": "Je kunt de inhoud niet verwijderen terwijl je een item verpla<PERSON>t", "pallet_notification_pallet_full": "Deze pallet is vol!", "pallet_notification_only_quantity_items": "U kunt alleen producten in bulk plaatsen op pallets", "palletshelf_notification_shelf_full": "Palletplank is vol!", "playeritempurchaser_notification_require_shoppingbasket": "Je hebt een winkelmandje nodig om dit product te pakken", "playeritempurchaser_notification_hands_full": "Je handen zijn al vol", "storageshelf_notification_shelf_full": "Opbergrek is vol!", "importmanagerdialog_notification_select_agent": "Selecteer een inkoper", "common_notification_select_business": "Selecteer een bed<PERSON><PERSON>", "marketingagencydialog_notification_select_website": "Selecteer een marketingwebsite", "recruitmentagencydialog_notification_select_skill": "Selecteer e<PERSON>t een vaardigh<PERSON>d", "wholesalestoremanagerdialog_notification_require_shelf_in_business": "Je hebt minimaal één {shelf} nodig in het gebouw", "import_partnership_notification_shipment_arrived": "<PERSON><PERSON> z<PERSON> van {name} is aangekomen", "businesshelper_notification_shipment_from_to_has_arrived": "Je bestelling van {fromname} naar {toname} is aangekomen", "businesshelper_notification_no_employee_assigned": "<b>{name} is open, maar heeft geen medewerkers toegewezen.</b>", "employeehelper_notification_employee_called_in_sick": "{name} ({businessName}) heeft zich ziek gemeld", "employeehelper_notification_employee_finished_training_male": "Medewerker {name} heeft zijn training in {skill} afgerond", "employeehelper_notification_employee_finished_training_female": "Medewerker {name} heeft haar training in {skill} afgerond", "itemhelper_notification_you_are_to_stuffed_to_eat_this": "Je buik zit al vol, dit kan er niet meer bij.", "forklift_notification_failed_to_load": "{amount} goederen kunnen niet worden geladen.", "producer_notification_already_holding": "Dit product is al in bezit van {name}.", "producer_notification_already_full": "{name} is al vol!", "producer_notification_resource_not_fitting": "Dit product past niet op dit type plank.", "buildingmanager_notification_no_free_spot": "<PERSON>r zijn geen vrije voertuigplaatsen in dit magazijn.", "buildingmanager_notification_business_closed": "<PERSON><PERSON> bedri<PERSON><PERSON> is momenteel ges<PERSON>en. Tip: Je kunt rusten op de bankjes.", "gamemanager_notification_save_successfull": "Spel succesvol opgeslagen in: {name}", "handtruck_notification_cant_use_while_using_paperbag": "U kunt een Steekwagen niet gebruiken terwijl u een Papieren Z<PERSON> draagt", "pointandclickobject_notification_cant_pick_occupied_objects": "U kunt een object dat bezet is niet oppakken.", "sleepingbench_notification_cant_use_with_handtruck": "Je kunt geen bank gebruiken terwijl je een steekwagen gebruikt!", "joboffer_notification_already_got_job": "Je hebt al een baan.", "managecargo_notification_vehicle_full": "{type} is vol.", "itemoverlay_notification_no_storage_available": "<PERSON><PERSON> opslagruimte beschik<PERSON>ar", "interiordesigner_cant_use_while_carrying_item": "Je kunt Interieur Ontwerper niet gebruiken terwijl je een voorwerp draagt", "itempanelui_notification_sleeping_not_in_this_building": "Je mag niet in dit gebouw slapen", "itempanelui_notification_cant_grab_with_attachments": "Je kunt een voorwerp met een bijgevoegd voorwerp niet oppakken", "itempanelui_notification_cant_grab_with_cargo_or_stock": "Je kunt een voorwerp met vracht of voorraad niet oppakken", "itempanelui_notification_cant_discard_with_attachments": "Je kunt een voorwerp met een bijgevoegd voorwerp niet weggooien", "itempanelui_notification_cant_pack_with_cargo_or_stock": "U kunt een artikellading of plankvoorraad niet inpakken", "itempanelui_notification_cant_pack_with_attchments": "U kunt een artikel niet inpakken met een bijgevoegd artikel", "itempanelui_notification_cant_sell_with_cargo": "U kunt een voertuig met lading niet verkopen", "itempanelui_notification_cant_add_to_temp_container": "Kan voorwerp niet toevoegen aan een tijdelijke container", "itempanelui_notification_cant_put_non_quanitity_items_into_temp_container": "Kan een artikel zonder hoeveelheid niet in een winkelcontainer plaatsen", "itempanelui_notification_cant_load_handtruck_into_handtruck": "Je kunt geen steekwagen in een andere steekwagen laden.", "notificationui_notification_insufficient_energy": "Onvoldoende energie", "notificationui_notification_insufficient_money": "niet genoeg geld", "purchasevehicleui_notification_recently_purchase_vehicle_is_blocking": "<PERSON><PERSON><PERSON> uw recent aangekochte voertuig op te halen, alvorens een ander te kopen.", "purchasevehicleui_notification_purchase_successfull": "Voertuig succesvol gekocht! Het wacht buiten op je.", "buildingpreview_notification_you_are_already_in_the_same_building": "Je zit al op zo'n gebouw!", "bizman_purchasingagents_contact_notification_minimum_amount_not_reached": "Minimum bestelbedrag van {price} niet gehaald.", "bizman_purchasingagents_contact_notification_no_warehouse_assigned": "<PERSON><PERSON> of meer producten hebben geen toegewezen magazijn.", "bizman_schedule_notification_employee_missing_skill": "Deze medewerker heeft geen vaardigheden die geschikt zijn voor {itemname}", "bizman_schedule_notification_cant_assign_on_closed_days": "Werknemers kunnen niet worden toegewezen aan dagen die als gesloten zijn ingepland.", "bizman_warehouse_driverstation_notification_needs_skill": "Werknemer vereist {skill} vaardigheid om te worden toegewezen", "bizman_warehouse_driverstation_notification_driver_already_assigned": "Bestuurder al toegewezen", "bizman_contractsettings_notification_max_boxes": "Maximaal aantal dozen bereikt", "bizman_presentation_notification_cant_terminate_when_inside": "U kunt een contract niet beëindigen terwijl u zich in hetzelfde gebouw bevindt", "common_notification_invalid_amount": "Vul een geldige hoeve<PERSON>heid in", "bizman_presentation_notification_offer_rejected": "De bedrijfseigenaar heeft uw bod van {price} afgewezen", "bizman_presentation_notification_offer_accepted": "De bedrijfseigenaar heeft uw aanbod geaccepteerd. Je bent nu de eigenaar van {name}. Gefeliciteerd!", "bizman_presentation_notification_fundamentalbusinessadministration_course_required": "Basiscursus bedrijfskunde vereist om meer dan <PERSON>én bedrijf te openen", "bizman_presentation_notification_no_business_name_entered": "<PERSON><PERSON> ingevo<PERSON>", "bizman_presentation_notification_no_business_type_entered": "<PERSON><PERSON> ingevoerd", "bizman_schedule_notification_headquaters_cant_change_opening_hours": "<PERSON> openingstij<PERSON> van het hoofdkantoor kunnen niet worden gewijzigd.", "bizman_settings_notification_name_empty": "Bedrijfsnaam mag niet leeg zijn", "bizman_settings_notification_type_empty": "<PERSON><PERSON><PERSON><PERSON> moet een type hebben", "bizman_settings_notification_name_duplicated": "Bedrijfsnaam al in gebruik", "bizman_settings_notification_save_successfull": "Bedrijfsinformatie succesvol opgeslagen.", "feedback_notification_success": "We hebben uw feedback ontvangen. Heel erg bedankt!", "feedback_notification_failure": "<PERSON><PERSON><PERSON><PERSON>, er is iets misgegaan! Laat het ons weten over deze fout. Zorg ervoor dat u uw feedback kopieert/plakt voordat u dit venster sluit.", "myemployees_unassign_for_training": "De werknemer moet ontheven zijn van alle bedrijven.", "buildingmanager_notification_warehouse_gate_is_bocked": "<PERSON> is vanuit de andere kant geb<PERSON>.", "common_close": "Gesloten", "deliveryplans_hud_confirm_discard_destination": "Weet je zeker dat je deze bestemming wilt verwijderen?", "contact_hud_confirm_end_parnetship": "Weet u zeker dat u deze samenwerking wilt beëindigen?", "econoviewloans_hud_confirm_payback_loan": "Weet u zeker dat u het totale bedrag van {loan} wilt terugbetalen?", "myemployees_hud_confirm_start_training": "Weet u zeker dat u wilt beginnen met het trainen <b>van {skill}</b> naar <b>{value}%</b> ? Het kost <b>{price}</b> en duurt <b>1 dag</b> . Het trainen van werknemers verhoogt ook hun uurloon.", "myemployees_hud_confirm_fire_employee": "Weet u zeker dat u deze medewerker wilt ontslaan?", "common_help": "Help", "buildingmanager_notification_private_property": "<PERSON>t gebouw is privé-eigendom.", "persona_characterinfo_age": "Leeftijd: <b>{age}</b>", "persona_characterinfo_networth": "Totaal vermogen: <b>{amount}</b>", "persona_characterinfo_businesses": "Totaal aantal bedrijven: <b>{amount}</b>", "persona_characterinfo_weeklyincome": "Wekelijks inkomen: <b>{amount}</b>", "common_energy": "Energie", "common_hunger": "<PERSON><PERSON>", "persona_personal_goals_header": "Persoonlijke doelen ({progress})", "persona_personal_goals_reward": "Beloning {reward}", "persona_personal_goals_rewards": "<PERSON><PERSON><PERSON> {reward}", "persona_personal_goals_no_rewards": "<PERSON><PERSON> be<PERSON>", "persona_personal_goals_reward_building_unlocked": "{name} ontgrendeld", "timestamp_full": "Dag {day}, {time}", "contacts_new_message": "Je hebt een nieuw bericht ontvangen van <b>{sender}</b>", "neighborhood_garmentdistrict": "Garment District", "neighborhood_hellskitchen": "Hell's Kitchen", "neighborhood_murrayhill": "<PERSON> Hill", "neighborhood_midtown": "Midtown", "neighborhood_global": "globaal", "dialog_bank_amount_to_loan": "Bedrag om te lenen", "tutorial_8_objective_wait_for_candiate": "Wacht tot het uitzendbureau je de eerste kandidaat stuurt via de <b>MyEmployees app</b>", "dialog_bank_larry": "<PERSON>", "dialog_bank_npc_name": "Bankier", "dialog_bank_loan_amount_header": "<PERSON><PERSON><PERSON><PERSON>", "dialog_import_npc_name": "Import Manager", "dialog_import_partnership_header": "Import Partnerschap", "dialog_marketing_agency_npc_name": "Marketing agent", "dialog_marketing_agency_campaign_settings_header": "Campagne-instellingen", "dialog_recruitment_agency_npc_name": "Wervingsagent", "dialog_recruitment_agency_candidate_properties_header": "Kandidaat eigenschappen", "dialog_wholesale_store_npc_name": "<PERSON><PERSON> manager", "dialog_wholesale_store_delivery_contract_header": "Leveringscontract", "dialog_bank_start": "Hallo daar! <PERSON><PERSON><PERSON> geld wil je lenen?", "dialog_bank_loan_accepted": "Daar kunnen we je zeker bij helpen. Ik laat het volledige bedrag meteen naar je bankrekening overmaken.<br> Bedankt dat je zaken met ons doet!", "dialog_bank_loan_denied_1": "<PERSON><PERSON> ben bang dat dat meer is dan we je kunnen bieden. Met je huidige economie kunnen we je niet meer dan in totaal {amount} lenen", "dialog_bank_loan_maximum_exceeded": "<PERSON><PERSON><PERSON> kunnen we geen gecombineerd leenbedrag van meer dan {amount} per klant aan. Door uw bestaande activiteiten bij onze bank te combineren met het gevraagde bedrag, overschrijden we deze drempel.", "dialog_bank_loan_fraud_physical": "Een negatief bedrag? <PERSON><PERSON><PERSON>, probeer je onze IT-systemen te misbruiken? Wil je dat ik de politie bel? GA WEG!!!", "dialog_bank_loan_fraud_phone": "Een negatief bedrag? <PERSON><PERSON><PERSON>, probeer je onze IT-systemen te misbruiken? Wil je dat ik de politie bel? <i>*Hang woedend op*</i>", "dialog_bank_loan_zero_input": "Het spijt me als onze bankvoorwaarden een beetje moeilijk te begrijpen zijn, maar we willen echt dat u ons vertelt hoeveel u wilt lenen.", "dialog_bank_loan_too_low": "Het spijt me, maar we kunnen je geen lening geven voor minder dan {amount}.", "dialog_bank_loan_denied_2": "Het spijt me, maar we kunnen u geen lening geven met uw huidige economie.", "dialog_bank_loan_request_player": "Ik wil graag een lening aanvragen van ${amount}", "dialog_try_again_button": "<PERSON><PERSON><PERSON> het opnieuw", "dialog_leave_button": "Verlaat", "dialog_close_button": "Afsluiten", "dialog_recruitment_agency_start": "Hallo en welkom bij {businessName}. Welk type werknemer wilt u inhuren?", "dialog_recruitment_agency_required_diploma_not_found": "Het spijt me, maar voordat wij u kunnen helpen bij het in dienst nemen van uw eerste medewerker, bent u wettelijk verplicht de cursus <b>Basismanagement</b> te volgen.<br> Wij raden Manhattan Business School aan.", "dialog_recruitment_agency_already_has_campaign_active": "We zijn nog op zoek naar de kandidaten die we de vorige keer hebben afgesproken. Weet je zeker dat je de werving wilt annuleren?", "dialog_recruitment_agency_on_cancel_campaign_recruiter": "<PERSON><PERSON>, we stoppen meteen met zoeken naar die campagne.", "dialog_recruitment_agency_on_cancel_campaign_player": "<PERSON><PERSON>, annuleer het alstublieft", "dialog_recruitment_agency_on_recruitment_settings_set_recruiter": "Heel erg bedankt. We sturen je het cv van de kandidaten zodra we ze hebben gevonden.", "dialog_recruitment_agency_on_recruitment_settings_set_player": "Ik wil {amountOfCandidates} kandidaten voor {businessName} met goed<lowercase> {skillKey}</lowercase> capaciteiten.<br> Kandidaten moeten over {days} dag(en) gevonden worden en een beschikbaarheid hebben van: {text}", "dialog_hang_up_button": "Ophang<PERSON>", "skillname_customerservice": "Klantenservice", "skillname_cleaning": "scho<PERSON><PERSON><PERSON>", "skillname_lawyer": "Advocaat", "skillname_purchasingagent": "Inkoper", "skillname_logisticsmanager": "Manager logistiek", "skillname_deliverydriver": "Bezorger", "skillname_programmer": "Programmeur", "dialog_recruitment_agency_cancel_current_campaign": "<PERSON>rving annuleren", "dialog_import_start": "Hallo en welkom! Hoe kan ik u helpen?", "dialog_import_no_warehouses": "Het spijt me, maar zonder een magazijn kunnen we de goederen niet bij u afleveren.", "dialog_import_no_purchasing_agents": "Het spijt me, maar we kunnen alleen zaken met u doen als u een vaste inkoopagent heeft die een eigen bureau heeft en beschikbaar is om een samenwerking af te handelen.", "dialog_import_on_partnership_settings_set_manager": "Prachtig! We kijken ernaar uit om zaken met u te doen. Ik zet de onderhandeling voort met {selectedEmployee}. Fijne dag!", "dialog_import_on_partnership_settings_set_player": "Ik wil graag een partnerschap aangaan dat wordt beheerd door onze inkoopagent {selectedEmployee}", "dialog_import_create_new_partnership": "Nieuw partnerschap", "dialog_accept_button": "Accept<PERSON><PERSON>", "dialog_decline_button": "Afwijzen", "workpanel_start_working": "<PERSON><PERSON> met werken", "workpanel_shift_ends_at": "<PERSON>nst eindigt om {time}", "workpanel_employee_is_currently_assigned": "{name} is <PERSON><PERSON> toe<PERSON>", "workpanel_business_is_currently_closed": "<PERSON><PERSON><PERSON><PERSON> is <PERSON><PERSON> g<PERSON>en", "workpanel_business_shift_not_yet_started": "<PERSON> dienst is nog niet begonnen.", "workpanel_fast_forward": "Tijd vooruit spoelen", "workpanel_unassign_self": "<PERSON> van u<PERSON> ongedaan maken", "dialog_marketing_agency_start": "<PERSON>o en welkom bij {businessName}. In welk type advertentie bent u geïnteresseerd?", "dialog_marketing_agency_no_businesses": "Het spijt me, maar zonder een bedrijf te hebben, kunnen we u niet helpen met het uit<PERSON>ere<PERSON> van marketingcampagnes voor een bedrijf.", "dialog_marketing_agency_on_campaign_settings_set_manager": "Heel erg bedankt. We gaan je campagne meteen instellen!", "dialog_marketing_agency_on_campaign_settings_set_player": "Ik wil een {marketingType} voor {businessName}.", "dialog_wholesale_store_start": "Hallo! Hoe kan ik u helpen?", "dialog_wholesale_store_no_businesses": "Het spijt me, maar zonder een bedrijf te hebben, kunnen we u niet helpen met leveringscontracten.", "dialog_wholesale_store_on_contract_settings_set_manager": "Akkoord! We hebben een deal.", "dialog_wholesale_store_on_contract_settings_set_player": "Ik wil graag een contract starten voor het bedrijf {businessName}", "dialog_wholesale_start_contract": "Start contract", "contacts_message_calling_outside_working_hours": "U heeft ons buiten onze kantooruren bereikt. <PERSON>beer het later opnieuw.", "contacts_message_occupied": "We zijn momenteel bezet. Bel alsjeblieft later nog een keer. Dank je!", "contacts_message_not_implemented": "<PERSON><PERSON> met {businessType} is nog niet geïmplementeerd.", "contacts_message_player_cancel_call": "<PERSON><PERSON><PERSON>t het, ik heb geen intresse meer", "phone_import_partnership_delivery_not_enough_funds": "We hebben het vereiste bedrag van {amount}, om door te gaan met de levering, niet in rekening kunnen brengen.<br> Het contract is opgezegd.", "phone_import_partnership_delivery_no_available_space": "We kunnen niet alle {itemName} dozen in {businessName} opslaan. Zorg er alstublieft voor dat we palletplanken beschikbaar hebben voor de volgende levering.", "phone_wholesale_store_delivery_not_enough_funds": "We hebben het {amount} dat nodig is om door te gaan met de levering van {businessName} niet in rekening kunnen brengen.<br> De levering is gean<PERSON><PERSON><PERSON>.", "phone_wholesale_store_delivery_no_available_space": "We kunnen niet alle {itemName}-dozen in {businessName} opslaan. Zorg ervoor dat we opslagplanken hebben voor de volgende levering.", "phone_recruitment_agency_new_candidate": "Hallo daar! Go<PERSON> nieu<PERSON>, we hebben een nieuwe kandidaat voor je gevonden:", "phone_government_parking_ticket": "<b><PERSON><PERSON><PERSON><PERSON> van parkeerovertreding</b><br><br> <PERSON><PERSON><PERSON> meneer of mevrouw:<br><br> We schrijven u om u te informeren dat uw motorvoertuig van het type <b>{vehicleTypeName}</b> illegaal geparkeerd is aangetroffen om {hour}:{minute}, Day {day}.<br><br> Er is automatisch een parkeerboete van <b>$ 125,00</b> in rekening gebracht van uw primaire bankrekening.<br><br> ho<PERSON><PERSON><PERSON>,<br> Het ministerie van Financiën van New York City", "phone_boss_fire_message": "Dit is uw derde wa<PERSON>chuwing. Ik heb geno<PERSON> van je, je bent ontslagen! Kom niet terug!", "phone_boss_warning_onpremise": "H<PERSON>, waar ben je verdomme? Je werktijden zijn {startingHour}:00 - {endingHour}:00. <PERSON><PERSON><PERSON><PERSON> dit als een waarschuwing!", "phone_welcome_message_friendly_1": "Hallo!", "phone_welcome_message_friendly_2": "Hoi!", "phone_welcome_message_friendly_3": "Heeeey!", "phone_welcome_message_business_1": "Bedankt voor uw komst naar {businessName}!", "phone_welcome_message_business_2": "Het team van {businessName} wil u bedanken voor uw komst naar ons bedrijf.", "phone_welcome_message_business_3": "Hallo! Bedankt voor uw komst naar {businessName}. Als we je ooit kunnen helpen, laat het ons dan weten!", "phone_welcome_message_business_4": "Bedankt voor het gebruik van {businessName}-services. U kunt ons tijdens onze openingstijden ook telefonisch bereiken.", "dialog_recruitment_agency_primary_skills": "Primaire vaardigheid", "dialog_recruitment_agency_amount_of_candidates": "Aantal kandidaten", "dialog_recruitment_agency_days_to_deliver": "Dagen om te bezorgen", "common_total_price": "Totale prijs", "common_business": "<PERSON><PERSON><PERSON><PERSON>", "common_type": "Type", "common_price_per_day": "Prijs per dag", "dialog_marketing_agency_impressions_per_week": "Impressies per week", "dialog_import_purchasing_agent": "Inkoper", "dialog_delivery_fee": "Bezorgkosten", "dialog_delivery_max_boxes": "Max dozen", "common_hourly_wage": "Uurloon: <b>{hourlyWage}</b>", "phone_call_button": "<PERSON><PERSON>", "phone_view_on_map_button": "Bekijk op kaart", "contacts_today_opening_hours": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contacts_today_opening_hours_closed": "Gesloten", "contacts_today_opening_hours_open": "{startingHour} - {endingHour}", "uncle_fred": "<PERSON><PERSON>", "the_city_of_new_york": "De stad New York", "friends_and_family": "Vrienden & Familie", "government": "<PERSON><PERSON><PERSON>", "common_address": "<PERSON><PERSON>", "job_board": "Vacaturenbord", "sleeping_bench": "Bank", "sleeping_bench_click_to_sleep": "Klik om te slapen", "common_storage_capacity_status": "Opslag: {amount}", "common_cargo_capacity_status": "Lading: {amount}", "fridgecontroller_empty_fridge": "leeg", "itemcontroller_remove_content": "Inhoud verwijderen", "common_manage_storage": "<PERSON><PERSON><PERSON>", "educationdoorcontroller_learn": "<PERSON><PERSON><PERSON>", "myemployees_employee_name": "<PERSON><PERSON>", "myemployees_age": "Leeftijd", "myemployees_task": "<PERSON><PERSON>", "myemployees_primary_skill": "Primaire vaardigheid", "myemployees_current_business": "<PERSON><PERSON><PERSON>", "myemployees_hours_per_week": "Uren per week", "myemployees_satisfaction": "Tevredenhe<PERSON>", "myemployees_hourly_wage": "Uurloon", "workpanel_assign_self": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "common_product": "Product", "marketinsider_demand": "Vraag", "marketinsider_import_price_index": "Importprijsindex", "marketinsider_providers": "<PERSON><PERSON><PERSON><PERSON>", "common_number_of_businesses": "{amount} <PERSON><PERSON><PERSON><PERSON>", "common_years_amount": "{years} jaar", "common_hours_per_week": "{weeklyHours} uur per week", "tooltip_right_click_to_manage": "<PERSON><PERSON> met de rechtermuisknop om te beheren", "requirement_on_counter_top": "Geplaatst op een kastje of een display", "requirement_no_duplicates": "<PERSON><PERSON>", "common_unassigned": "<PERSON><PERSON>", "myemployees_no_task": "<PERSON><PERSON> taak", "requirement_chair": "<PERSON><PERSON><PERSON>", "requirement_desk": "Bureau", "feedback_title": "Bugrapport indienen", "feedback_description": "Bedankt dat je de tijd hebt genomen om ons te helpen van Big Ambitions de game te maken waar we allemaal van gedroomd hebben.<br><br> We gebruiken uw feedback actief om prioriteiten te stellen waar we de volgende keer aan moeten werken. Alle feedback is 100% anoniem, tenzij u uw gegevens in het tekstvak schrijft. <b>Laat alle feedback alleen in het Engels achter</b><br><br> Dank je!<br> Hovgaard Games", "feedback_input_field_placeholder": "<PERSON><PERSON> hier feedback in. Als je een vraag hebt, gebruik dan de forums of Discord, anders wordt deze niet beantwoord.", "feedback_submit_button": "Bugrapport indienen", "feedback_system_data_toggle": "<PERSON><PERSON>g systeeminformatie, savegame, spelerlogboeken en screenshot toe om ons te helpen de bug te verhelpen.", "myemployees_title": "Medewerkers", "common_age": "Leeftijd: <b>{age}</b>", "common_gender": "Geslacht: <b>{gender}</b>", "myemployees_days_hired": "<PERSON><PERSON> ingehuurd: <b>{daysHired}</b>", "myemployees_called_in_sick": "{employee<PERSON>ame} heeft zich vandaag ziek gemeld", "myemployees_skills": "Vaardigheden", "myemployees_wants_and_demands": "Wensen & eisen", "myemployees_manage_schedule": "<PERSON><PERSON><PERSON> beheren", "myemployees_fire": "Werknemer ontslaan", "myemployees_satisfaction_description": "Tevredenheid is direct a<PERSON><PERSON><PERSON><PERSON><PERSON> van de gestelde eisen. Hoe minder, hoe minder kwaliteit ze leveren. Volledig ontevreden medewerkers zullen ontslag nemen.", "myemployees_currently_training_text": "Werknemer traint moment<PERSON>:", "help_button": "HULP", "jobdemand_freeweekends": "Geen weekends", "common_events": "Evenementen", "myemployees_train_skill_button": "Train vaardigheid", "common_businesses": "<PERSON><PERSON><PERSON><PERSON>", "bizman_menu_insight": "Inzicht", "bizman_menu_inventory_pricing": "Inventaris & Prijzen", "bizman_menu_schedule": "Rooster", "bizman_menu_deliveries": "Leveringen", "bizman_menu_settings": "Instellingen", "bizman_menu_marketing": "Marketing", "bizman_menu_logistics_managers": "Logistiek Managers", "bizman_menu_purchasing_agents": "Inkopers", "bizman_menu_hr_managers": "HR Managers", "bizman_menu_drivers": "chauffeurs", "bizman_menu_inventory": "Inventaris", "common_day_number": "Dag {number}", "bizman_insight_customer_satisfaction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bizman_insight_promotion": "Promotie", "bizman_insight_promotion_description": "Promotie geeft aan hoeveel klanten uw bedrijf trekt.", "bizman_traffic_index": "Verkeersindex", "bizman_insight_marketing": "Marketing", "bizman_insight_satisfaction": "Tevredenhe<PERSON>", "bizman_insight_satisfaction_description": "<PERSON><PERSON><PERSON><PERSON><PERSON> bepaalt hoeveel geld een klant in uw bedrijf uitgeeft.", "bizman_insight_customer_service": "Klantenservice", "bizman_insight_pricing": "Prijzen", "bizman_insight_cleanliness": "<PERSON><PERSON><PERSON>", "marketeventtype_businessopened_description": "{rivalName} opende <b>{businessName}</b> op {address}.", "marketeventtype_businessclosed_description": "<b>{businessName}</b> op {address}, eigendom van {rivalName}, is gesloten.", "marketeventtype_hype_description": "Burgers tonen de laatste tijd een sterke vraag naar <b>{itemName}</b> .", "marketeventtype_productshortage_description": "Importeurs melden een tijdelijk tekort aan <b>{itemName}</b> . De verwachte duur is {durationInDays} dagen.", "marketeventtype_largeplayerpurchase_description": "Importeurs melden een tijdelijk tekort aan <b>{itemName}</b> . De verwachte duur is {durationInDays} dagen.", "marketeventtype_productbackorder_description": "Er is een huidige globale nabestelling van <b>{itemName}</b> . De verwachte duur is {durationInDays} dagen.", "bizman_insight_customers": "Klanten", "bizman_customers_capacity": "Klantcapaciteit:", "bizman_insight_customers_capacity_description": "U kunt slechts zoveel klanten per uur krijgen als uw capaciteit toelaat. Het vergroten en balanceren van het aantal beschikbare verkooppunten, productschappen, etc. zal direct resulteren in meer potentiële klanten per uur.", "bizman_insight_current_capacity_per_hour": "Huidige capaciteit per uur", "bizman_insight_building_limit": "Gebouw limiet", "bizman_insight_customers_over_time": "Klanten in de loop van de tijd", "bizman_insight_customers_chart_yesterday": "Gisteren", "marketeventtype_businessopened": "<PERSON><PERSON><PERSON><PERSON> geo<PERSON>d", "marketeventtype_businessclosed": "Gesloten", "marketeventtype_hype": "Hype", "marketeventtype_productshortage": "Producttekort", "marketeventtype_largeplayerpurchase": "Producttekort", "marketeventtype_productbackorder": "Product nabestelling", "capacity_type_shopping_baskets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "capacity_type_point_of_sales": "Verkooppunt", "capacity_type_desktop_workstations": "Desktop-werkstations", "bizman_insight_shelf_type_capacity": "{shelfAmount}x {shelfLabel} ({customersPerHour})", "businesstype_webdevelopmentagency": "webontwikkelings kantoor", "bizman_insight_customers_chart_last_week": "7 dagen", "bizman_schedule_cleaning_employees_toggle": "Verberg schoonmaakmedewerkers", "bizman_schedule_shared_schedule": "Gedeeld rooster voor alle dagen", "bizman_schedule_opening_hours": "Openingstij<PERSON>", "bizman_schedule_business_closed": "Bedrijf is vandaag gesloten. Klik om te openen.", "bizman_schedule_employee": "{employeeName} ({employeeWeeklyHours} U/WEEK)", "subwaystation_hellskitchennorthstation": "Hells Kitchen Station Noord", "subwaystation_hellskitchensouthstation": "Hells Kitchen Station Zuid", "subwaystation_garmentdistricteaststation": "Garment District Station Oost", "subwaystation_garmentdistrictharborstation": "Garment District Haven Station", "subwaystation_midtownstpatrickstation": "St. Patrick Station", "subwaystation_midtowntimessquarestation": "Times Square Station", "subwaystation_midtowngrandcentralstation": "Grand Central Station", "bizman_schedule_opening_hour_slot": "{startingHour} - {endingHour} ({hours} uur)", "bizman_delivery_contracts": "Leveringscontracten", "bizman_delivery_contract_enabled": "Levering {day}", "bizman_delivery_contract_disabled": "Uitgeschakeld", "bizman_delivery_contract_settings": "Contractinstellingen", "bizman_delivery_contract_cancel_contract": "Contract opzeggen", "bizman_delivery_contract_delivery_day": "Bezorgdag", "bizman_delivery_contract_delivery_fee": "Bezorgkosten: {fee}", "bizman_delivery_contract_boxes_limit": "Limiet: {totalBoxes}/{maxBoxes} dozen", "bizman_delivery_contract_item_boxes": "{boxes} dozen ({boxesPrice})", "bizman_delivery_contract_add_entry": "Invoer toevoegen", "bizman_delivery_contract_total_price_per_delivery": "Totale prijs per levering: {totalPricePerDelivery}", "bizman_inventory_title": "Maga<PERSON><PERSON>inventaris", "bizman_inventory_in_stock": "<PERSON> voorra<PERSON>", "bizman_inventory_shipped": "verzonden", "bizman_inventory_import": "Importeren", "bizman_inventory_balance": "Balans", "bizman_inventory_days_until_empty": "Dagen tot leeg", "bizman_inventory_product_days_until_empty": "{days} dagen", "bizman_inventory_run_out": "op", "bizman_inventory_never_runs_out": "Nooit", "bizman_drivers": "Chauffeurs", "bizman_drivers_slot_number": "Plek #{number}", "bizman_drivers_vehicle_info": "Rijd een voertuig door de garagedeur van het magazijn om het aan deze plek toe te wijzen", "bizman_drivers_driver_info": "Sleep werknemer hierheen om toe te wijzen", "bizman_drivers_driver": "<PERSON><PERSON><PERSON><PERSON>", "bizman_drivers_vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bizman_products_inventory": "Producten & Inventaris", "bizman_sold_last_7_days": "Afgelopen 7 dagen verkocht", "bizman_compared_last_period": "Vergel<PERSON><PERSON> met {amount} vorige periode", "bizman_based_on_7_days": "Gebaseerd op de afgelopen 7 dagen", "common_gross_profit_margin": "Brutowinstmarge", "common_best_selling_products": "Best verkopende producten", "bizman_settings_manage_business_label": "<PERSON><PERSON><PERSON><PERSON>", "bizman_business_information": "Bedrijfsinformatie", "bizman_business_information_description": "<PERSON>es een nieuwe naam, type of logo voor dit bedrijf.", "bizman_customize_logo": "<PERSON><PERSON> a<PERSON>", "bizman_logo_shape": "Logovorm", "bizman_select_from_disk": "<PERSON><PERSON>", "common_font": "Lettertype", "bizman_logo_color": "Logokleur", "bizman_font_color": "Letterkleur", "bizman_background_color": "Achtergrond kleur", "bizman_sign_explanation": "Bordinstelling<PERSON> zijn van b<PERSON> door met de rechtermuisknop op het gebouw te klikken", "bizman_shutdown_business": "<PERSON><PERSON><PERSON>", "bizman_shutdown_business_description": "Je kan dit bedrijf permanent sluiten. Deze actie kan niet ongedaan gemaakt worden!", "businesstype_warehouse": "<PERSON><PERSON><PERSON><PERSON>", "bizman_select_business_type": "Selecteer bedrijfstype", "common_open_in_econoview": "Openen in EconoView", "common_campaigns": "Campagnes", "bizman_marketing_no_campaigns": "U heeft geen actieve marketingcampagnes. Bezoek een marketingbureau om er een op te zetten.", "common_impressions_per_day": "Vertoningen per dag", "common_daily_expense": "Dagelijkse uitgaven", "common_days_amount_left": "{amount} dagen resterend", "common_expired": "Afgelopen", "bizman_marketing_cancel_campaign": "<PERSON><PERSON><PERSON> annu<PERSON>en", "bizman_marketing_auto_renew": "Automatisch vernieuwen", "bizman_marketing_restart_campaign": "Campagne opnieuw starten", "marketinggroups_internet_marketing": "Internet marketing", "common_show_warehouse": "<PERSON><PERSON> ma<PERSON>", "bizman_logisticsmanagers_deliver_to_destinations_amount": "<PERSON>ert tot <b>{amount}</b> bestem<PERSON>en", "bizman_logisticsmanagers_hint_seated_only": "Alleen zittende Logistiek Managers zijn toegankelijk", "bizman_logisticsmanagers_adddestination": "Nieuwe bestemming toe<PERSON>egen", "bizman_logisticsmanagers_delivery_plan_for_businessname": "Leveringsplan voor <b>{businessName}</b>", "bizman_logisticsmanagers_warehouse_stock": "Magazijnvoorraad", "bizman_logisticsmanagers_min_stock_amount": "Minimale voorraad", "bizman_logisticsmanagers_runs_out_in": "<PERSON><PERSON><PERSON> op in", "bizman_logisticsmanagers_destination_number": "Bestemming #{number}", "bizman_logisticsmanagers_no_vehicle_assigned": "<PERSON><PERSON> voe<PERSON><PERSON>g toe<PERSON>", "bizman_logisticsmanagers_no_driver": "<PERSON><PERSON> chauffeur", "transactiontype_undefined": "<PERSON><PERSON>", "transactiontype_cheat": "GEEF MIJ GELD!!!!", "transactiontype_subwayride": "Metrorit", "transactiontype_recruitmentcampaign": "Wervingscampagne", "transactiontype_itemsold": "Verkocht {itemSoldInfo}", "transactiontype_deliverycontractrefund": "{itemQuantityFormat} voor {businessName}", "transactiontype_importdeliveryrefund": "Terugbetaling {itemQuantityFormat} voor {warehouseName} van {businessName}", "transactiontype_marketing": "Marketingcampagnes voor {businessName}", "transactiontype_tuitionfee": "{diplomaName} ({uren} uur, {minuten} minuten)", "transactiontype_deliverycontract": "{businessName} levering van {warehouseName}", "transactiontype_unassignedwage": "{employee} ({businessName} Dagloon", "transactiontype_wage": "{employee} ({businessName} Dagloon", "transactiontype_employeetraining": "{skillName} training voor {employee}", "transactiontype_playerjobsalary": "<PERSON><PERSON> {businessName}", "transactiontype_loanpayment": "{businessName} Leningbetaling", "transactiontype_loanpayoff": "{businessName} <PERSON><PERSON> afbet<PERSON>", "transactiontype_revenue": "{businessName} Opbrengst", "transactiontype_importdelivery": "Levering van {businessName}", "transactiontype_loanpayout": "{businessName} <PERSON><PERSON>", "transactiontype_depositreturn": "Geretourneerde aanbetaling voor {address}", "transactiontype_rent": "{address} <PERSON><PERSON>", "transactiontype_deposit": "{address} Storting", "transactiontype_parkingticket": "Boete voor illegaal parkeren van {vehicleName}", "transactiontype_vehiclebought": "{vehicleName}", "bizman_purchasingagents_hint": "Alleen zittende inkoop agenten zijn <PERSON>", "bizman_purchasingagents_no_contract": "Geen contract", "bizman_purchasingagents_minimum_order": "Minimale bestelling:", "bizman_purchasingagents_end_partnership": "Beëindig import partnerschap", "bizman_purchasingagents_next_delivery": "Volgende levering", "bizman_purchasingagents_next_delivery_day": "Dag {nextDeliveryDay} om {time}", "bizman_purchasingagents_total_price": "Totaal {totalPrice}", "bizman_purchasingagents_order": "<PERSON><PERSON><PERSON>", "bizman_purchasingagents_cancel_order": "<PERSON><PERSON><PERSON> bestelling", "bizman_purchasingagents_amount_to_buy": "# kopen", "bizman_purchasingagents_price": "<PERSON><PERSON><PERSON><PERSON>", "bizman_purchasingagents_designated_warehouse": "Aangewez<PERSON> magazijn", "bizman_purchasingagents_product_price": "{totalPrice} ({pricePerPiece} per stuk)", "bizman_purchasingagents_product": "Product", "smartphone_contacts": "<PERSON><PERSON>", "bizman_purchasingagents_deliver_one_time": "<PERSON><PERSON> keer", "bizman_purchasingagents_deliver_every_x_days": "Automatisch bevoorraden elke {x} dag(en)", "itempanelui_cargo": "Lading:", "common_unknown": "Onbekend", "common_select_type": "Selecteer type", "common_select_color": "Selecteer kleur", "sign_type": "Type {number}", "colors_red": "Rood", "colors_green": "<PERSON><PERSON><PERSON>", "colors_yellow": "<PERSON><PERSON>", "colors_darkgrey": "<PERSON><PERSON> grijs", "colors_black": "<PERSON><PERSON>", "colors_blue": "<PERSON><PERSON><PERSON>", "colors_white": "Wit", "colors_lime": "Limoen", "colors_lightgrey": "Lichtgrijs", "colors_darkgreen": "Donkergro<PERSON>", "specialbuilding_bank": "Bank", "specialbuilding_school": "School", "specialbuilding_cardealership": "Autohandelaar", "specialbuilding_appliancestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "specialbuilding_wholesalestore": "Groothandel", "specialbuilding_recruitmentagency": "<PERSON><PERSON>gsbureau", "specialbuilding_furniturestore": "<PERSON><PERSON><PERSON> winkel", "specialbuilding_marketingagency": "Marketingbureau", "specialbuilding_officesupplystore": "Winkel voor kantoorbenodigdheden", "specialbuilding_importexport": "Importeren/Exporteren", "menu_options_select_resolution": "Selecteer resolutie", "menu_options_select_fps_limit": "Selecteer FPS-limiet", "menu_options_none": "geen", "menu_options_fps_vsync": "VSync", "menu_options_fps_x_fps": "{x} FPS", "menu_options_quality_low": "Laag", "menu_options_quality_medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu_options_quality_high": "<PERSON><PERSON>", "menu_options_aa_fxaa": "FXAA", "menu_options_aa_taalow": "TAA Laag", "menu_options_aa_taamedium": "TAA Gemiddeld", "menu_options_aa_taahigh": "TAA Hoog", "menu_options_aa_smaalow": "SMAA Laag", "menu_options_aa_smaamedium": "SMAA Gemiddeld", "menu_options_aa_smaahigh": "SMAA Hoog", "menu_wishlist_description": "Zorg ervoor dat je op de hoogte wordt gehouden wanneer Big Ambitions wordt uitgebracht. Plaats het in je Verlanglijst!", "common_wishlist_now": "Voeg toe aan verlang<PERSON>st!", "close_button": "Sluiten [ESC]", "character_customization_step_one": "Karakteraanpassing - Stap 1", "character_customization_step_one_title": "Stap 1 - Basisinformatie", "character_customization_step_two": "Karakteraanpassing - Stap 2", "character_customization_step_two_title": "Stap 2 - Lichaamskenmerken", "character_customization_gener": "Geslacht", "character_customization_skin_color": "Huidskleur", "common_next": "Volgende", "common_previous": "Vorige", "common_finish": "<PERSON><PERSON><PERSON>", "character_customization_hair": "<PERSON><PERSON>", "character_customization_head": "Hoofd", "character_customization_torso": "<PERSON><PERSON>", "character_customization_legs": "<PERSON><PERSON>", "character_customization_shoes": "<PERSON><PERSON><PERSON>", "intro_paragraph_one": "Het is 3 maanden geleden dat oma is overleden. Ik weet dat ik volwassen ben nu ik 18 jaar oud ben, maar toch...", "intro_paragraph_two": "Het voelt zo eng dat er niemand is om voor dingen te zorgen.", "intro_paragraph_three": "Er is echter <PERSON><PERSON> goed ding. Op de begrafenis vroeg mijn oom Fred om mijn telefoonnummer.", "intro_paragraph_four": "Hij zei dat hij me wilde helpen overeind te komen. Ik ken hem niet echt, maar ik denk dat hij toch familie is.", "character_customization_hair_01": "Haar 1", "character_customization_hair_02": "Haar 2", "character_customization_torso_01": "<PERSON><PERSON>", "character_customization_torso_02": "<PERSON>", "character_customization_torso_03": "T-shirt", "character_customization_legs_01": "<PERSON><PERSON> broek", "character_customization_legs_02": "<PERSON><PERSON>", "character_customization_legs_03": "Pantalon", "character_customization_head_01": "Hoofd 1", "character_customization_head_02": "Hoofd 2", "character_customization_shoes_01": "<PERSON><PERSON><PERSON>", "character_customization_shoes_02": "<PERSON><PERSON> s<PERSON>", "character_customization_shoes_03": "Sneakers", "character_customization_shoes_04": "Hakken", "new_character_save_game": "<PERSON><PERSON><PERSON> {character} Save Game", "common_distance_meters": "{distance}m", "help_title": "Help-systeem", "common_neighborhoods": "<PERSON><PERSON><PERSON><PERSON>", "bizman_building_area": "Bouwgebied", "bizman_building_area_value": "{squaremeters}m2 ({buildingSize}{buildingVersion})", "bizman_market_value": "Marktwaarde", "common_neighborhood": "<PERSON><PERSON><PERSON>", "bizman_building_buildingtype": "{type} gebouw", "buildingtype_residential": "<PERSON><PERSON><PERSON>", "buildingtype_retail": "<PERSON><PERSON>", "buildingtype_office": "<PERSON><PERSON><PERSON>", "buildingtype_warehouse": "<PERSON><PERSON><PERSON><PERSON>", "buildingtype_special": "Speciaal", "bizman_buy_building": "Gebouw kopen", "bizman_not_for_sale": "Dit gebouw is niet te koop", "bizman_preview_button": "Voorvertoning", "condition_average": "Gemiddelde", "bizman_estimated_valuation": "Geschatte waardering: <b>{valuation}</b>", "bizman_send_overtake_offer_button": "Overkoop aanbod versturen", "business_description_appliance_store_1": "Elektronicawinkel in familiebezit met apparaten op industrieel niveau", "business_description_appliance_store_2": "Wij hebben alle apparaten voor uw huis of bedrij<PERSON>!", "business_description_bank_1": "Prive bankieren sinds 1902", "business_description_bank_2": "Mensen en bedrijven helpen groeien als eerlijke en transparante bank", "business_description_car_dealership_1": "Uw lokale autodealer", "business_description_car_dealership_2": "Erkende dealer van industriële semi-vrachtwagens sinds 2011", "business_description_furniture_store": "Kwaliteit en eenvoudig te monteren Scandinavische meubels", "business_description_import_1": "Internationale import en export van verschillende soorten retail goederen", "business_description_import_2": "Import van groothandelproducten voor de voedingsindustrie", "business_description_marketing_1": "Met meer dan 50 jaar ervaring is CityAds uw beste keuze voor echte wereld billboard-advertenties.", "business_description_marketing_2": "Modern marketingbureau gespecialiseerd in online adverteren", "business_description_office_supplies": "Haal het beste uit uw medewerkers met onze state-of-the-art kantoormeubelen!", "business_description_recruitment_agency_1": "Wij zijn het nummer 1 wervingsbureau in New York", "business_description_recruitment_agency_2": "Wervingsbureau uit Manhattan gespecialiseerd in operationeel personeel", "business_description_school": "Breng uw zakelijke vaardigheden naar een hoger niveau", "business_description_wholesalestore_1": "Wij bieden lokale B2B groothandelsproducten aan", "business_description_wholesalestore_2": "NY Distro, opgericht in 2001, is de nummer 1 groothandel in Manhattan", "common_amount": "Bedrag", "bizman_send_offer_button": "<PERSON><PERSON><PERSON><PERSON> verzenden", "common_name": "<PERSON><PERSON>", "bizman_start_new_business": "Start een nieuw <PERSON>", "bizman_choose_business_name": "<PERSON><PERSON><PERSON> de na<PERSON> van uw bedrij<PERSON> in en bevestig:", "bizman_start_business_button": "<PERSON><PERSON><PERSON><PERSON> starten", "bizman_residential_description": "Dit gebouw heeft alleen een woonbestemming.", "bizman_start_new_business_button": "Start een nieuw <PERSON>", "bizman_building_price_label": "<#{color}>{deposit}\\n{defaultLayoutCost}</color>\\n{calculatedDailyRent}", "bizman_rent_building_button": "<PERSON><PERSON>", "bizman_terminate_contract_button": "Contract beëindigen", "bizman_building_price_label_description": "<#ABABAB>Aanbetaling\nElektrische apparaten</color>\nDagelijkse huur", "contacts_your_contacts": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "econoview_last_transactions": "Laatste transacties", "econoview_loans": "Leningen", "econoview_loan_cost_per_day": "{costPerDay} per dag", "econoview_loan_pay_off_button": "Betaal {remainingAmount} af", "econoview_dashboard": "Dashboard", "econoview_income_statement": "Inkomingsverklaring", "econoview_yesterday_date": "Gisteren (Dag {dayNumber})", "econoview_row_undefined": "<PERSON><PERSON>", "econoview_row_businesses": "<PERSON><PERSON><PERSON><PERSON>", "econoview_row_ongoing_expenses": "Lopende kosten", "econoview_row_private_residences": "<PERSON><PERSON><PERSON><PERSON> woningen", "econoview_row_loans": "Leningen", "econoview_row_unassigned_staff_wages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> person<PERSON>lonen", "econoview_row_total": "Totaal", "econoview_row_profit": "Winst", "econoview_row_salaries": "<PERSON><PERSON><PERSON>", "econoview_row_rent": "<PERSON><PERSON>", "econoview_row_marketing": "Marketing", "econoview_row_sales": "verkoop", "econoview_row_resources": "Middelen", "common_requirements": "Eisen", "bizman_market_price": "Laagste marktprijs", "bizman_retail_price": "<PERSON><PERSON><PERSON> prij<PERSON>", "bizman_stock_count": "<PERSON><PERSON><PERSON><PERSON>", "transactiontype_itempurchase": "<PERSON><PERSON> bij {businessName}", "common_loading": "Bezig met laden...", "common_full_address": "{number} {street}", "menu_options_reset_to_defaults": "<PERSON><PERSON><PERSON>aardinstellingen", "job_cashier_description": "Verantwoordelijk voor het bedienen van klanten aan de kassa", "common_loaded": "Geladen", "purchaseui_descriptions_coming_later": "Beschrijvingen komen later...", "purchaseui_waiting_in_queue": "<PERSON>achten in de rij...", "joboffer_accept_job": "<PERSON><PERSON>n", "itemoverlay_contents_headline": "<PERSON><PERSON><PERSON>", "itemoverlay_missing_requirements_headline": "Ontbrekende vereisten", "poi_custom_location": "Aangepaste locatie", "dialog_import_select_purchasing_agent": "Selecteer <PERSON><PERSON><PERSON>", "dialog_select_business": "Selecteer bed<PERSON><PERSON><PERSON>", "dialog_select_skill": "<PERSON><PERSON>", "pallet_shelf_price_label": "{totalPrice} ({quantity} stuks)", "itempanelui_buttons_skip": "Wijzig station", "itempanelui_buttons_toggle_radio": "Radio Aan/Uit", "businesstype_importexport": "Import/Export", "common_days": "{value} dagen", "school_closing_soon_warning": "Het is nu te laat om een sessie te starten. Kom morgen weer.", "common_value": "{value}", "common_weeks": "{value} weken", "buildingresume_taxi_travel": "<PERSON><PERSON> hi<PERSON> ({price})", "click_to_use_taxi": "Klik om taxi te gebruiken", "common_taxi": "Taxi", "filebrowser_search": "Zoeken...", "filebrowser_filename": "Bestandsnaam", "filebrowser_show_hidden_files": "Toon verborgen bestanden", "filebrowser_delete_file_warning": "Weet u zeker dat u deze bestanden wilt verwijderen? Deze bewerking kan niet ongedaan worden gemaakt.", "filebrowser_select_all": "Selecteer alles", "filebrowser_deselect_all": "Deselecteer alles", "filebrowser_new_folder": "Nieuwe map", "filebrowser_delete": "Verwijderen", "filebrowser_rename": "<PERSON><PERSON><PERSON><PERSON>", "filebrowser_all_files": "<PERSON>e bestanden (.*)", "filebrowser_logo_shape_title": "Selecteer logovorm", "filebrowser_images": "Afbeeldingen", "npc_expression_default": "Ik heb g<PERSON>elens en emoties en ik druk ze uit via een bubbel", "npc_expression_bad_customer_service": "Wat een vreselijke klantenservice. Ik kom niet terug.", "npc_expression_good_customer_service": "Wat een fijne klantervaring. Ik kom hier zeker terug!", "npc_expression_no_cashiers": "Waar zijn de kassiers?", "npc_expression_no_shopping_baskets": "Hoe verwachten ze dat ik boodschappen doe zonder winkelmanden? Ik ben weg.", "npc_expression_item_too_expensive": "<PERSON>k zal niet zoveel betalen voor <b>{itemname}</b>", "npc_expression_no_item_in_stock": "<PERSON><PERSON><PERSON>, <b>{itemname}</b> is niet meer op voorraad", "npc_expression_no_cash_registers": "<PERSON>k kan geen <b>kassa</b> vinden.", "npc_expression_no_paper_bags": "Ze hebben geen papieren zakken meer!", "notification_no_energy_to_run": "Je bent te uitgeput om te rennen", "notification_no_energy_to_walk": "Je bent te uitgeput om op volle snelheid te bewegen", "npc_expression_no_item": "<PERSON>k kan geen <b>{itemname}</b> vinden.", "common_furniture": "<PERSON><PERSON><PERSON>", "nearest_building": "{number} {street}", "marketingtypename_smallbillboard": "<PERSON> reclameb<PERSON>", "marketingtypename_mediumbillboard": "Middelgroot reclamebord", "marketingtypename_largebillboard": "G<PERSON> reclamebord", "marketingtypename_voogleads": "Voogle-advertenties", "marketingtypename_latergramads": "Latergram Advertenties", "marketingtypename_friendbookads": "Friendbook Advertenties", "marketingtypename_jitterads": "Jitter Advertenties", "itemname_patterncarpet": "<PERSON><PERSON><PERSON><PERSON>", "itemname_persiancarpet": "<PERSON><PERSON><PERSON>", "itemname_colorfulcarpet": "Golvend tapijt", "itemname_roundcarpet": "<PERSON><PERSON>", "jobdemand_parttime": "Part-time", "jobdemand_fulltime": "Full-time", "jobdemand_nomornings": "<PERSON><PERSON>", "jobdemand_noevenings": "<PERSON><PERSON>", "jobdemand_nonights": "<PERSON>n nachtdiensten", "jobdemand_nocleaning": "<PERSON><PERSON> scho<PERSON>die<PERSON>", "jobdemand_fourdaysweek": "Vier dagen week", "jobdemand_fivedaysweek": "Vi<PERSON><PERSON> dagen week", "businesstype_empty": "leeg", "myemployees_task_training": "Training", "tutorial_20_objective_4": "Koop minimaal <PERSON><PERSON>", "tutorial_20_objective_5": "Plaats een op<PERSON>plank in je cadeauwinkel", "employeehelper_notification_emloyee_resigned": "{employeeName} heeft ontslag genomen ({businessName})", "businesshelper_notification_delivery_no_storage_shelves": "Levering aan {businessName} was niet mogelijk vanwege gebrek aan beschikbare opslag", "transactiontype_taxiride": "Tax<PERSON>t", "notification_need_empty_hands_to_interact": "Je moet lege handen hebben om met dit voorwerp te werken", "menu_options_open_localizor": "Localizor openen", "menu_options_others_localizor_tip": "Onze vertalingen zijn bewerkt en goedgekeurd door de community.", "workpanel_timemachine": "Tijdmachine tot einde dienst", "menu_options_select_language": "<PERSON><PERSON> een taal", "notification_delivery_contract_arrived": "Uw zending van {fromname} naar {toname} is aangekomen", "bizman_schedule_cant_assign_on_closed_days": "Werknemers kunnen niet worden toegewezen aan dagen die als gesloten zijn ingepland.", "business_description_supermarket": "Jouw pitstop voor snacks, drankjes, eten en alles daar tussenin!", "playerhud_currentjob_notification_cant_quit_working": "Kan baan niet opzeggen terwijl je aan het werk bent", "menu_copyright_disclaimer": "Hey streamer!", "menu_copyright_disclaimer_description": "Wij bezitten de rechten voor alle muziek in het spel.\nU hoeft zich geen zorgen te maken over auteursrechtclaims.", "bizman_contractsettings_product_label": "{itemName} ({boxSize}x) - {boxPrice}/doos", "carcontroller_notification_warehouse_vehicle_unassigned": "{type} is niet meer toegewezen aan {warehouseName}", "playeritempurchaser_notification_require_interaction_with_cashier": "U moet communiceren met de kassier om dit artikel te kopen", "jobdemand_fulltime_description": "Tussen 30 en 50 uur per week", "jobdemand_freeweekends_description": "<PERSON>n zaterdag-/zondagdiensten", "jobdemand_parttime_description": "Tussen 10 en 30 uur per week", "jobdemand_nomornings_description": "<PERSON><PERSON> diensten toegewezen tussen 06:00 en 10:00", "jobdemand_noevenings_description": "<PERSON><PERSON> diensten toegewezen tussen 18.00 uur en 22.00 uur", "jobdemand_nonights_description": "<PERSON><PERSON> diensten toegewezen tussen 22.00 uur en 04.00 uur", "jobdemand_nocleaning_description": "<PERSON><PERSON> schoonmaakploegen toegewezen", "jobdemand_fourdaysweek_description": "Toegewezen om 4 dagen per week te werken", "jobdemand_fivedaysweek_description": "Toegewezen om 5 dagen per week te werken", "common_condition": "Staa<PERSON>", "common_enter_building": "Betreed gebouw", "tutorial_4_objective_4": "Zeg je baan op bij de plaatselijke supermarkt", "voogle_maps_close": "Voogle Maps sluiten", "help_common_hunger_content": "**Honger** is <PERSON><PERSON> van de belangrijkste eigenschappen van je personage.\n\nO<PERSON> de hongerstatus te verbeteren, moet je eten of drinken. Voeding kan worden gekocht in winkels in de stad, of door een koelkast in uw appartement of bedrij<PERSON> te voorzien van genoeg voedsel.\n\n<PERSON><PERSON> de honger 0% bereikt, heeft dit de volgende impact:\n* Verhoogd energieverbruik met 200%", "help_common_energy_content": "**Energie** is een van de belangrijkste eigenschappen van je personage.\n\nOm de energiestatus te verbeteren, moet je slapen of cafeïnehoudende dranken drinken.\n\nWanneer de energie 0% bereikt, heeft dit de volgende gevolgen:\n* Lang<PERSON><PERSON> lopen\n* Risico op flauwvallen door uitputting\n* Snellere afname van de hongerstatus\n\nJe kunt op het volgende slapen:\n* [Standaard bed](furniture-bed1)\n* [Kingsize bed] (furniture-kingsizebed)\n* Stadsbanken\n* Voertuigen\n* Boten", "help_common_personal_goals_content": "**Persoonlijke doelen** zijn de neven<PERSON> van je personage. Ze zijn niet gere<PERSON>erd aan de doelstellingen van oom Fred.\n\nHet bereiken van een persoonlijk doel geeft een tijdelijke **geluksboost**.\n\nPersoonlijke doelen zijn direct gekoppeld aan Steam-prestaties.", "common_personal_goals": "Persoonlijke doelen", "help_general": "<PERSON><PERSON><PERSON><PERSON>", "common_business_type": "Bedrijfstype", "common_building_type": "Type gebouw", "common_size_sq": "{size} m2", "common_select": "Selecteer", "common_market_demands": "<PERSON><PERSON><PERSON><PERSON>", "common_real_estate": "<PERSON><PERSON><PERSON><PERSON> goed", "common_subtotal": "Subtotaal", "common_price": "<PERSON><PERSON><PERSON><PERSON>", "common_buildings_for_sale": "Gebouwen te koop", "common_happiness": "Geluk", "common_days_left": "{days} dagen over", "common_hours_left": "{hours} uur resterend", "common_vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "real_estate_type_and_size": "Type en maat", "real_estate_estimated_value": "<PERSON><PERSON><PERSON><PERSON> wa<PERSON>e", "real_estate_building_sold_notification": "Je hebt {address} verkocht voor {price}", "bizman_businesses_and_real_estate": "Bedrijven & onroerend goed", "bizman_warehouses": "Magazijnen", "bizman_headquarters": "Hoofdkwartier", "bizman_private_residences": "<PERSON><PERSON><PERSON><PERSON> woningen", "bizman_avg_daily_income": "Gem. dagelijks inkomen", "bizman_alerts": "Waarschuwingen", "bizman_status": "Status", "bizman_number_of_alerts": "{amount} waarschuwing(en)", "bizman_hover_destination_button": "<PERSON>lik om als bestemming in te stellen", "bizman_hover_manage_button": "Klik om uw bedrijf te beheren", "bizman_empty_building": "Leeg ({buildingType})", "bizman_vehicle_slots": "Voertuigslots", "bizman_manage_drivers": "<PERSON><PERSON><PERSON><PERSON> beheren", "bizman_show_full_inventory": "Toon volledige inventaris", "bizman_linked_businesses": "Gelinkte bedrijven", "bizman_manage_delivery_plan": "Leveringsplan beheren", "bizman_warehouse_driverstation_not_enough_skill": "Bestu<PERSON><PERSON> heeft niet genoeg vaardigheden om dit voertuig te besturen", "bizman_choose_business_type": "Selecteer het type bedrijf dat u wilt starten:", "bizman_primary_products": "Primaire producten", "bizman_competitors_in_neighborhood": "<b>{amount} concurrenten</b> in de buurt", "bizman_purchasingagents_warehouse_stock": "Magazijnvoorraad", "bizman_building_total_size": "Totale grootte", "bizman_send_purchase_offer": "Bod voor de aankoop van het gebouw verzenden", "bizman_building_description_retail": "Een gebouw dat wordt verhuurd aan bedrijven zoals winkels en restaurants", "bizman_building_description_office": "<PERSON>en gebouw dat wordt verhuurd aan bedrijven zoals advocatenkantoren en webontwikkelingsbureaus", "bizman_building_description_residential": "<PERSON>en gebouw dat wordt verhuurd aan individuen of gezinnen om er te wonen", "bizman_building_description_warehouse": "<PERSON>en gebouw dat wordt verhuurd aan bedrijven die hun logistiek willen uitbreiden", "bizman_presentation_notification_building_offer_accepted": "De gebouweigenaar heeft uw bod geaccepteerd. U bent nu de eigenaar van {address}. Gefeliciteerd!", "bizman_presentation_notification_building_offer_rejected": "De eigenaar van het gebouw heeft uw bod van {price} afgewezen", "bizman_menu_realestate": "Vastgoedinstellingen", "bizman_real_estate_rent_management": "<PERSON><PERSON> beheer", "bizman_real_estate_self_occupied_unit": "Zelfstandige eenheid", "bizman_real_estate_set_for_sale": "Zet te koop", "bizman_real_estate_current_rent": "Huidige huur: <b>{rent}/{unit}</b>", "bizman_real_estate_average_market_price": "Gem. marktprijs: <b>{price}/{unit}</b>", "bizman_real_estate_time_left_for_new_rent": "Resterende tijd tot nieuwe huur van toepassing is: <b>{days} dag(en)</b>", "bizman_real_estate_rent_applied": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reeds toegepast", "bizman_real_estate_apply_new_rent": "Nieu<PERSON> huur a<PERSON>vragen", "bizman_real_estate_occupancy": "Bezetting", "bizman_real_estate_total_daily_rent": "Totale dagelijkse huur ({occupancy}% van {totalSqm})", "bizman_real_estate_self_occupied_unit_description": "<PERSON><PERSON> eigenaar van dit gebouw mag je er <b>1</b> huren<lowercase> <b>{buildingType}</b></lowercase> <b>businessunit gratis</b> .", "bizman_real_estate_takeover_unit": "Overname eenheid", "bizman_real_estate_unit_generating_rent": "<PERSON><PERSON><PERSON><PERSON> is <PERSON>eel bezet en genereert huur.", "bizman_real_estate_unit_owned_by_player": "<PERSON><PERSON><PERSON><PERSON> is momenteel eigendom van u en genereert geen huur.", "bizman_real_estate_price_at_purchase": "<PERSON><PERSON> prijs bij aan<PERSON> (dag {day})", "bizman_real_estate_estimated_market_price": "<PERSON><PERSON><PERSON><PERSON>", "bizman_real_estate_enter_sales_price": "<PERSON><PERSON><PERSON>oopp<PERSON> in", "bizman_real_estate_your_potential_gain": "<PERSON><PERSON> winst", "bizman_real_estate_mark_for_sale": "Zet te koop", "bizman_real_estate_no_sales_price_notification": "<PERSON>oer een geldige verkoopprijs in", "bizman_real_estate_for_sale_info": "Gebouw staat te koop voor {price}", "bizman_real_estate_cancel_sale": "Verkoop annuleren", "bizman_real_estate_taxes_info": "{daysInterval}-daagse onroerendgoedbelasting:", "carcontroller_notification_warehouse_employee_unassigned_slot": "{employeeName} is verwi<PERSON><PERSON>d uit slot {slotNumber} wegens te laag vaardigheidsniveau", "carcontroller_notification_fuel_empty": "Je voertuig heeft geen brands<PERSON><PERSON> meer. Tip: Jerrycans zijn verkrijgbaar bij benzinestations", "gasstationoverlay_fillup": "Tanken ({price})", "gasstationoverlay_jerrycan": "Jerrycan ({price})", "gasstationoverlay_repair": "Repareer ({price})", "businesstype_florist": "Bloemist", "help_itemname_cheapflower_content": "**Flower (Cheap)** is een type product dat verkocht wordt door [Florists](businesstypes-florist).\n\nDaarnaast kan het verkocht worden door:\n\n* [Gift Shops](businesstypes-giftshop)\n* [Bookstore](businesstypes-bookstore)\n\nHet product kan in de volgende meubels geplaatst worden:\n* [Rounded Shelf](furniture-roundedshelf)\n* [Display Stand (Tiered Product)](furniture-productdisplaystandtiered)\n\nHet product kan gekocht worden op de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n* [Total Produce Trading](address:6 6a)\n\nHet product kan geïmporteerd worden vanaf de volgende locaties:\n* [JetCargo Imports](address: 1 pier)\n* [United Ocean Import](address: 3 pier)", "help_itemname_expensiveflower_content": "**Flower (Expensive)** is een type product dat verkocht wordt door [Florists](businesstypes-florist).\n\nDaarnaast kan het verkocht worden door [Gift Shops](businesstypes-giftshop).\n\nHet product kan in de volgende meubels geplaatst worden:\n* [Rounded Shelf](furniture-roundedshelf)\n* [Display Stand (Tiered Product)](furniture-productdisplaystandtiered)\n\nHet product kan gekocht worden op de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n* [Total Produce Trading](address:6 6a)\n\nHet product kan geïmporteerd worden vanaf de volgende locaties:\n* [United Ocean Import](address: 3 pier)", "itemname_jerrycan": "<PERSON><PERSON> (10L)", "itemname_cheapflower": "Goedkope bloem", "itemname_expensiveflower": "<PERSON><PERSON> bloem", "itemname_deskplant01": "Bureauplant 01", "itemname_deskplant02": "Bureauplant 02", "itemname_deskplant03": "Vierkante bureauplant", "itemname_largemeetingtable": "<PERSON><PERSON> ve<PERSON>l", "itemname_largewallclock": "<PERSON>rote Wandklok", "itemname_lcdrestaurantscreen": "LCD Restaurant-scherm", "itemname_wallradiator": "<PERSON><PERSON><PERSON><PERSON>", "itemname_officechair2": "Stump Mesh bureaustoel", "itemname_valenciachair": "Valencia Stoel", "itemname_valenciaottoman": "Valencia Ottomaans", "contacts_taxes_message_heading": "<b><PERSON><PERSON><PERSON> van openstaande belastingen</b><br><br><PERSON><PERSON><PERSON>eer of mevrouw:<br><br>De aanslag voor de periode Dag {startingDay}-{endingDay} is nu besch<PERSON><PERSON><PERSON>.", "contacts_taxes_message_ending": "G<PERSON>eve alle openstaande belastingen te betalen voor Dag {lastPayingDay} aan uw lokale IRS-kantoor:<br>{address}<br><br><b>Hoogachtend,<br>Internal Revenue Service<b>", "contacts_taxes_registered_businesses": "Geregistreerde bedrijven:", "contacts_taxes_tax_deductible_expenses": "Fiscaal aftrekbare kosten:", "contacts_taxes_real_estate": "Vastgoedbelasting:", "contacts_corporate_tax_percentage": "Percentage vennootschapsbelasting", "contacts_taxes_to_be_paid": "Te betalen belastingen", "internal_revenue_service": "Belastingdienst", "tutorial_todotask_paytaxes": "Belasting te betalen in {remainingDays} dagen", "contacts_taxes_message_warning": "<b>Waar<PERSON><PERSON>wing voor openstaande belastingbetaling</b><br><br> <PERSON><PERSON><PERSON> meneer of mevrouw:<br><br> We hebben de betaling van uw openstaande belastingen nog niet ontvangen.<br><br> Als de betaling niet binnen <b>dag {day}</b> is voltooid, zijn we genoodzaakt om zonder verdere waarschuwing waardevolle spullen van uw bedrijf in beslag te nemen.<br><br> G<PERSON>eve het verschuldigde bedrag te betalen bij uw lokale belastingkantoor:<br> {address}<br><br> <b><PERSON><PERSON> hoogacht<PERSON>,<br> de Belastingdienst</b>", "taxes_pay_item": "Belastingen", "market_insider_show_only_for_sale": "Enkel te koop tonen", "market_insider_citizen_data": "Burgergegevens", "market_insider_real_estate_data": "Vastgoedgegevens", "market_insider_total_buildings": "To<PERSON><PERSON> geb<PERSON>wen", "market_insider_average_building_price": "Gemiddelde <lowercase>{buildingType}</lowercase> prijs/{unit}", "socialclass_working": "Arbeidersklasse", "socialclass_middle": "Middenklasse", "socialclass_upper": "Hogere klasse", "transactiontype_hospitalbill": "Ziekenhuisrekening", "transactiontype_taxpayment": "Belasting betaling", "hospital_respawn_notification": "Je had geen energie meer en viel flauw. De lokale autoriteiten hebben u naar het ziekenhuis vervoerd om te herstellen.", "businesstype_government": "Regering", "transactiontype_buildingbought": "{address} acquisitie", "transactiontype_rentrevenue": "{address} huuropbrengst", "transactiontype_autotowservice": "NY AutoTow-service", "econoview_row_rent_revenue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "irs_tax_payment_station": "Belastingbetalingsstation", "happinessmodifiertype_startedaheadquarters": "Hoofdkantoor geopend", "happinessmodifiertype_sleptinthecar": "In de auto geslapen", "happinessmodifiertype_walkedinthepark": "In het park gelopen", "happinessmodifiertype_wenttohospital": "<PERSON><PERSON> naar het ziek<PERSON>huis", "jobdemand_peacefulworkenvironment": "Rustige werkomgeving", "jobdemand_peacefulworkenvironment_description": "<PERSON><PERSON><PERSON><PERSON> heeft een geluk van 50% of meer", "towdestination_gasstation": "Vervoer naar het dichtstbijzijnde tankstation", "towdestination_autorepairshop": "Transport naar dichtstbijzijnde autoreparatiewerkplaats", "dialog_auto_tow_start": "Welkom bij NY AutoTow, hoe kunnen we u helpen?", "dialog_auto_tow_service_no_vehicle": "Bedankt voor het bellen naar NY AutoTow. Ga in het voertuig zitten waarvoor u diensten wilt en bel terug.", "dialog_auto_tow_service_settings_set": "<PERSON><PERSON> en<PERSON>, dat komt op {amount}. We zijn steeds om de hoek! Tot ziens.", "dialog_auto_tow_service_settings_set_player": "Ik wil graag de '{autoTowServiceOption}' optie", "autotowservicedialog_notification_vehicle_towed": "Uw voertuig is weggesleept naar {towAddress}.", "auto_tow_service_ny": "NY AutoTow-service", "contact_description_special": "Speciaal", "sadperiodtype_stayathome": "<PERSON><PERSON><PERSON>", "sadperiodtype_noeat": "<PERSON><PERSON> eten", "sad_period_cant_leave_vehicle_notification": "Je bent te droevig om het voertuig te verlaten. Je moet eerst slapen", "sad_period_cant_leave_home_notification": "Je bent te droevig om het huis te verlaten. Je moet eerst slapen", "sad_period_cant_enter_building_notification": "Je bent te verdrietig om dit gebouw binnen te gaan. Je moet even slapen", "sad_period_cant_eat": "Je bent te verdrietig om te eten. Je moet even wachten tot je weer kunt eten", "sadperiodtype_stayathome_started_notification": "Je gaat een trieste periode in waarin je het voertuig/huis niet verlaat", "sadperiodtype_noeat_started_notification": "Je gaat een trieste periode in van niet eten", "sadperiod_finished_notification": "Je zit niet meer in een droevige periode", "sad_period_persona_app_notification": "In een droevige periode: {sadPeriod} <color=#515A60>({hours} uur resterend)</color>", "bizman_real_estate_rent_not_applied": "<PERSON><PERSON><PERSON> huurpri<PERSON><PERSON> niet toe<PERSON>t", "help_common_happiness_content": "**Geluk** is een van de belangrijkste eigenschappen van je personage. De hoeveelheid geluk hangt af van de huidige boosts die je personage heeft.\n\nPositieve boosts kunnen worden verkregen door:\n* Positieve inkomsten te hebben\n* Persoonlijke doelen te behalen\n* Belangrijke doelen te bereiken (zoals een hoofdkantoor openen, je eerste werknemer aannemen...)\n* Wandelen in het park\n* Naar het skatepark te gaan\n* [Televisie kijken](common_watchtv)\n* [Computerspellen spelen](common_playcomputer)\n* [Boeken lezen](common_readbooks)\n* [Sporten](common_exercise)\n* [Gokken (alleen op vrijdagavond)](adres: 4 pier)\n* Ontspannen op je boot\n* Een [DJ Booth](furniture-djbooth) in je huis te gebruiken\n\nNegatieve boosts kunnen worden verkregen door:\n* In de auto te slapen\n* Geen appartement te hebben\n* Naar het ziekenhuis te gaan\n\nWanneer het geluk 0% bereikt, heeft dit de volgende impact:\n* Risico op periodes van verdriet\n* Verminderd geluk van werknemers\n* Snellere afname van de hongerstatus\n* Snellere afname van de energiestatus\n\nEr zijn twee soorten periodes van verdriet:\n* Periode van niet eten: u kunt niets eten\n* Periode van niet verlaten van huis/voertuig: u kunt uw huidige huis/voertuig niet verlaten of een ander gebouw dan uw huis betreden", "bizman_list_real_estate_type": "Vastgoed ({buildingType})", "happinessmodifiertype_nohome": "<PERSON><PERSON> thuis", "happinessmodifiertype_firstdayonny": "Eerste dag in New York", "happinessmodifiertype_firstapartment": "Eerste appartement", "happinessmodifiertype_firstjob": "Eerste baan", "happinessmodifiertype_firstemployee": "Eerste werknemer", "happinessmodifiertype_completedpersonalgoal": "<PERSON><PERSON><PERSON><PERSON><PERSON> doel behaald", "happinessmodifiertype_positiverevenue": "Positieve o<PERSON>t", "contacts_taxes_repossession_message_heading": "<b><PERSON><PERSON><PERSON><PERSON> van he<PERSON></b><br><br> <PERSON><PERSON><PERSON> of mevrouw:<br><br><PERSON><PERSON><PERSON> de achterstallige betaling van belastingen zijn we overgegaan tot beslaglegging op een deel van de activa van uw bedrijf.", "contacts_taxes_repossession_message_ending": "<b><PERSON><PERSON> g<PERSON>,<br>Belastingdienst</b>", "contacts_taxes_repossessed_valuables": "In beslag genomen waardevolle spullen:", "vehicletypename_bima320": "VERZEKERING 320", "sleepingbench_notification_cant_use_with_item_in_hand": "Je kunt de slaapbank niet gebruiken terwijl je een voorwerp in je hand hebt.", "help_itemname_pizzaoven_content": "**Pizza Oven** kan worden gebruikt om te verkopen:\n\n* [Pizza](producten-pizza)\n\n**Productcapaciteit:** 60\n**Klantcapaciteit:** 20\n\nHet meubilair kan worden gekocht op de volgende locaties:\n* [Square Appliances](adres:16 4a)", "notification_destination_set": "{address} is ingesteld als bestemming", "transactiontype_buildingsold": "{address} verkocht", "myemployees_no_business_assigned_notification": "Werknemer is niet toegewezen aan een bedrijf", "itempanelui_parkingzone_legal_payed": "WETTELIJK ({price})", "transactiontype_publicparking": "Openbare parkeerplaats voor {vehicleName}", "bizman_browse_store_inventory": "Blader door winkelvoorraad", "bizman_store_inventory_title": "{businessName} Inventaris", "furniture_delivery_dialog_order": "<PERSON><PERSON><PERSON>", "furniture_delivery_dialog_add_item": "Voeg voorwerp toe", "furniture_delivery_item": "{itemName} - {itemPrice}", "dialog_furniture_store_npc_name": "<PERSON>kelmana<PERSON>", "dialog_furniture_store_start": "<PERSON>o, wa<PERSON>ee kunnen we u helpen?", "dialog_furniture_store_no_addresses": "U moet ten minste één beschikbaar adres hebben om een levering te starten.", "dialog_furniture_delivery_header": "Meubelbezorging", "dialog_furniture_store_player_start": "Ik wil graag de volgende artikelen bestellen", "dialog_select_address": "Selecteer adres", "dialog_select_delivery_time": "Selecteer levertijd", "dialog_delivery_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialog_furniture_delivery_time_slot": "{day} (Dag {number}) {hour}", "dialog_furniture_delivery_item_already_in_list_notification": "Dit artikel staat al in de lijst", "common_notification_select_address": "Selecteer een adres", "common_notification_select_delivery_time": "Selecteer het bezor<PERSON><PERSON><PERSON>", "dialog_furniture_delivery_select_items_notification": "U moet ten minste één item selecteren om te laten bezorgen", "dialog_furniture_store_on_contract_settings_set_player": "Ik wil dat die {amount} items worden bezorgd op {address} dag {day} om {hour}:<br> {text}", "dialog_furniture_store_on_contract_settings_set_manager": "Akkoord. We doen de levering volgens uw specificaties. Dank je!", "dialog_furniture_store_already_has_delivery": "Hallo! We hebben al een lopend leveringscontract. Wil je deze annuleren?", "dialog_furniture_store_cancel_delivery_button": "Annuleer levering", "dialog_furniture_store_cancel_delivery": "<PERSON><PERSON>, annuleer het alstublieft.", "menu_options_ui_zooming": "UI zoomen", "smartphone_title": "BizPhone 1.0", "jobdemand_seatedatofficechair2": "Stump mesh bureaustoel", "jobdemand_seatedatmultipurposechair": "Multifunctionele stoel", "jobdemand_seatedatofficedesk2": "Bureau 2", "jobdemand_standardfridge": "Standaard koelkast", "jobdemand_largemeetingtable": "<PERSON><PERSON> ve<PERSON>l", "jobdemand_sofa": "Elke sofa", "jobdemand_coffeemachine": "Koffiezetapparaat", "jobdemand_seatedatofficechair2_description": "Werknemer eist plaats te nemen op een Stump Mesh Office Chair", "jobdemand_seatedatmultipurposechair_description": "Werknemer eist plaats te nemen op een multifunctionele stoel", "jobdemand_seatedatofficedesk2_description": "Werknemer eist plaats te nemen aan een Executive Office Desk", "jobdemand_standardfridge_description": "Werknemer eist een standaard koelkast in het gebouw", "jobdemand_largemeetingtable_description": "Werknemer eist een grote vergadertafel in het gebouw", "jobdemand_sofa_description": "Werknemer eist enige vorm van een bank in het gebouw", "jobdemand_coffeemachine_description": "Werknemer eist een goedkope koffiemachine", "employeehelper_notification_employee_amount_called_in_sick": "{amount} medewerkers hebben zich ziek gemeld", "econoview_loans_daily_interest_rate": "Dagelijkse rente", "econoview_loans_daily_payment": "Dagelijkse betaling", "econoview_loans_time_left": "<PERSON>i<PERSON><PERSON> over", "main_menu_browse_savegame_folder": "Blader door de savegame-map...", "myemployees_task_absent": "<PERSON><PERSON>", "loading_hint_handtrucks": "Gebruik de gratis steekwagens (in alle winkels) om eenvoudig meerdere dozen te verplaatsen.", "loading_hint_prefix": "<b>Tip:</b>", "loading_hint_cityworkforce": "Er is meer dan <PERSON><PERSON> wervingsbureau en elk bureau is gespecialiseerd in verschillende soorten werknemers.", "dialog_slot_machine_start": "Klik op de knop om de wielen te laten draaien!", "dialog_slot_machine_spin_the_wheel_button": "<PERSON>aai aan het rad ({price})", "transactiontype_casino": "Casino ({element})", "dialog_casino_no_prize_dialog": "<PERSON>n geluk deze keer!", "dialog_slot_machine_jackpot_prize": "Gefeliciteerd! Je hebt de grote jackpot van {prize} gewonnen", "dialog_casino_regular_prize_dialog": "Gefeliciteerd! Je hebt {prize} gewonnen", "common_add": "Toevoegen", "dialog_roulette_start": "Welkom bij de roulette!", "dialog_bet_button": "Inzetten", "dialog_bet_set_dealer": "<PERSON>eel succes allemaal!", "dialog_roulette_no_prize": "<PERSON><PERSON> winst deze keer. Volgende keer beter!", "dialog_bet_amount_input": "Inzet", "dialog_bet_question_dealer": "<PERSON><PERSON><PERSON> wil je inzetten?", "dialog_roulette_bet_color": "<PERSON><PERSON><PERSON>", "dialog_roulette_bet_number": "Getal", "dialog_roulette_bet_color_select": "Selecteer kleur", "dialog_roulette_bet_number_select": "Selecteer getal", "common_any": "<PERSON><PERSON><PERSON>", "common_notification_select_color": "<PERSON><PERSON> een kleur", "common_notification_select_number": "Selecteer een nummer", "help_common_clothing_content": "Er zijn verschillende soorten kleding:\n* [Klassieke goedkope herenkleding](itemname_classiccheapmaleclothing)\n* [Klassieke goedkope dameskleding](itemname_classiccheapfemaleclothing)\n* [Moderne goedkope herenkleding](itemname_moderncheapmaleclothing)\n* [Moderne goedkope dameskleding](itemname_moderncheapfemaleclothing)\n* [Klassieke dure herenkleding](itemname_classicexpensivemaleclothing)\n* [Klassieke dure dameskleding](itemname_classicexpensivefemaleclothing)\n* [Moderne dure herenkleding](itemname_modernexpensivemaleclothing)\n* [Moderne dure dameskleding](itemname_modernexpensivefemaleclothing)", "help_itemname_classiccheapmaleclothing_content": "**Klassieke goedkope herenkleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](bedrijfstypes-kledingwinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Kledingrek](meubel-kledingrek)\n* [Kleding<PERSON> schuin](meubel-kledingrek schuin)\n\nHet product kan op de volgende locaties worden gekocht:\n* [Metrogroothandel](adres:2 5a)\n* [NY Distro Inc](adres:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone import](adres: 4 pier)", "help_itemname_moderncheapmaleclothing_content": "**Moderne goedkope herenkleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](bedrijfstypes-kledingwinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Kledingrek](meubel-kledingrek)\n* [Kledingrek schuin](meubel-kledingrek schuin)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc](adres:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone import](adres: 4 pier)", "help_itemname_moderncheapfemaleclothing_content": "**Moderne goedkope dameskleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](bedrijfstypes-kledingwinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Kledingrek](meubel-kledingrek)\n* [Kleding<PERSON> schuin](meubel-kledingrek schuin)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone Imports](adres: 4 pier)", "help_itemname_classicexpensivemaleclothing_content": "**Klassieke dure herenkleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](businesstypes-clothingstore).\n\nHet product kan in de volgende meubels geplaatst worden:\n* [<PERSON><PERSON><PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON><PERSON><PERSON> schuin](furniture-clothingrackangled)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_classicexpensivefemaleclothing_content": "**Klassieke dure dameskleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](bedrijfstypes-kledingwinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Kledingrek](meubel-kledingrek)\n* [Kleding<PERSON> schuin](meubel-kledingrek schuin)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone Imports](adres: 4 pier)", "help_itemname_modernexpensivemaleclothing_content": "**Moderne dure herenkleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](businesstypes-clothingstore).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [<PERSON><PERSON><PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON><PERSON>](furniture-clothingrackangled)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc](address: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_modernexpensivefemaleclothing_content": "**Moderne dure dameskleding** is een type product dat voornamelijk wordt verkocht via [Kledingwinkels](bedrijfstypes-kledingwinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Kledingrek](meubel-kledingrek)\n* [Kledingrek schuin](meubel-kledingrek schuin)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [BlueStone Imports](adres: 4 pier)", "common_clothing": "<PERSON><PERSON><PERSON>", "help_skillname_customerservice_content": "Medewerkers met de vaardigheid **Klantenservice** worden ingezet voor detailhandelsbedrijven.\n\nMedewerkers van de klantenservice werken allemaal op het verkooppunt van een winkel om klanten te bedienen.\nZe vullen ook de voorraad aan via de opslagplanken achterin\n\nHet vaardigheidsniveau (%) bepaalt hoe goed de klanten worden behandeld.\n\nZe kunnen worden toegewezen aan:\n* [Verkooppunten](furniture-itemgrouppointofsale)\n* [Coat Check (Links)](furniture-coatcheckleft)\n* [Coat Check (Rechts)](furniture-coatcheckright)\n\nZe kunnen worden ingehuurd bij:\n* [Anderson Recruitment Corp.](adres: 16 5a)", "help_skillname_cleaning_content": "Werknemers met de vaardigheid **Schoonmaken** worden ingezet voor detailhandelsbedrijven.\n\nSchoonmaakmedewerkers maken uw winkel of kantoor schoon tijdens de uren dat ze ingepland staan.\n\nHet vaardigheidsniveau (%) bepaalt hoeveel ze per uur kunnen schoonmaken.\n\nZe kunnen worden toegewezen aan:\n* [Schoonmaakstations](meubel-schoonmaakstation)\n\nZe kunnen worden ingehuurd bij:\n* [Anderson Recruitment Corp.](adres: 16 5a)", "help_skillname_lawyer_content": "Werknemers met de vaardigheid **Advocaat** worden ingezet voor advocatenkantoren.\n\nAdvocaten werken allemaal met digitale klanten om hun [Advocaatvergoeding (per uur)](vergoeding-uurvergoedingadvocaat) te verdienen.\n\nHet vaardigheidsniveau (%) bepaalt hoeveel klanten bereid zijn te betalen voor de diensten.\n\nZe kunnen worden toegewezen aan:\n* [Computerwerkstation](furniture-computerworkstation)\n\nZe kunnen worden ingehuurd bij:\n* [City Workforce Inc.](adres: 41 4a)", "help_skillname_purchasingagent_content": "Medewerkers met de vaardigheid **Inkoopagent** worden gebruikt voor het hoofdkantoor.\n\nInkoopagenten beheren elk 1 [importcontract](importeurs-contract) en controleren [importvoorraad](importeurs-overzicht) naar uw [warehouses](businesstypes-warehouse) of [fabriek](businesstypes-fabriek).\n\nHet vaardigheidsniveau (%) bepaalt hoeveel korting ze kunnen krijgen op de importcontracten.\n\nZe kunnen worden toegewezen aan:\n* [Computerwerkstation](furniture-computerworkstation)\n\nZe kunnen worden ingehuurd bij:\n* [City Workforce Inc.](adres: 41 4a)", "help_skillname_logisticsmanager_content": "Werknemers met de vaardigh<PERSON>d **Logistiek Manager** worden gebruikt voor het hoofdkantoor.\n\nLogistiek Managers beheren elk 1 [warehouse](businesstypes-warehouse) of [factory](businesstypes-factory) en controleren [delivering inventory](logistics-overview) naar uw winkels.\n\nHet vaardigheidsniveau (%) bepaalt naar hoeveel locaties ze kunnen leveren.\n\nZe kunnen worden toegewezen aan:\n* [Computer Workstation](furniture-computerworkstation)\n\nZe kunnen worden ingehuurd bij:\n* [City Workforce Inc.](address: 41 4a)", "help_skillname_programmer_content": "Werknemers met de vaardigheid **Programmeur** worden ingezet voor webontwikkelingsbureaus.\n\nProgrammeurs werken allemaal met digitale klanten om hun [Programmeurvergoeding (per uur)](vergoedingen-uurloonprogrammeurvergoeding) te verdienen.\n\nHet vaardigheidsniveau (%) bepaalt hoeveel klanten bereid zijn te betalen voor de diensten.\n\nZe kunnen worden toegewezen aan:\n* [Computerwerkstation](meubilair-computerwerkstation)\n\nZe kunnen worden ingehuurd bij:\n* [City Workforce Inc.](adres: 41 4a)", "bank_dialog_total_daily_payment": "Totale dagelijkse uitbetaling", "bank_dialog_interest_daily_payment": "Dagelijkse rente ({rate}%)", "menu_options_reset_windows": "Windows-posities resetten", "menu_options_reset_windows_tip": "Zet de verplaatsbare vensters terug naar de standaardposities", "menu_options_reset_windows_button": "Vensters resetten", "menu_options_reset_windows_notification": "Windows-posities zijn succesvol gereset", "loading_hint_walkinpark": "Laag geluk? Maak een wandeling in het park.", "loading_hint_purchasingagent": "<PERSON><PERSON> met een hoger niveau zorgt voor betere importprijzen.", "loading_hint_coffee": "<PERSON><PERSON><PERSON> drinken geeft je een kleine energieboost!", "loading_hint_neighborhoodprices": "<PERSON><PERSON><PERSON><PERSON> met rijkere burgers maken het mogelijk om producten tegen hogere prijzen te verkopen.", "loading_hint_marketing": "Is de verkeersindex van uw bedrijf laag? Gebruik marketing om uw promotiescore te verbeteren.", "loading_hint_towservice": "He<PERSON>t je auto geen benzine meer of is deze volledig beschadigd? Bel NY sleepdienst via de contact app.", "loading_hint_speedrun": "He<PERSON>t u haast terwi<PERSON>l u binnen bent? Houd SHIFT ingedrukt om te rennen.", "loading_hint_subway": "Gebruik de metrostations om snel en gemakkelijk door de stad te reizen.", "loading_hint_customerdialogs": "Weet u niet zeker hoe je jouw bedrijf kunt verbeteren? Pro<PERSON>r te observeren wat jouw klanten zeggen tijdens een bezoek aan uw winkel.", "loading_hint_employeedemands": "Zorg ervoor dat u aan de eisen van uw medewerkers voldoet. Ongelukkige medewerkers presteren onder hun niveau.", "loading_hint_storageshelf": "Opslagplanken zijn een geweldige manier om de inventaris van uw winkel beter te organiseren.", "loading_hint_cleaningstation": "Om ervoor te zorgen dat uw medewerkers een winkel kunnen schoonmaken, hebben zij een schoonmaakstation nodig.", "loading_hint_sleepingincar": "Slapen in je auto is handig, maar het heeft een negatieve invloed op je geluk.", "loading_hint_benches": "Banken in de stad kunnen worden gebruikt om snel tijd over te slaan en wat energie op te doen.", "loading_hint_realestate": "Ben je het betalen van huur beu? Spaar en koop in plaats daarvan het gebouw!", "loading_hint_contacts": "Nadat je speciale plaatsen hebt bezocht, zoals winkels voor apparaten, wervingsbureaus, enz., kun je ze rechtstreeks bellen via de Contacten-app.", "loading_hint_handtruck_in_trunk": "U kunt een steekwagen opbergen in de kofferbak van uw voertuig.", "loading_hint_opening_hours": "Gebruik BizMan's \"Customers Over Time\" om erachter te komen op welke uren uw bedrijf het meest winstgevend zal zijn.\n", "loading_hint_help": "U kunt op ieder gewenst moment op F1 drukken om het hulp menu te openen.", "dialog_roulette_ball_land_position": "De bal landde op nummer {number}, kleur {color}", "casino_message_welcome": "Welkom in internationale wateren. V<PERSON> succes!", "casino_message_trip_over": "De nacht is voorbij en de boot keert terug naar de haven", "vehicletypename_vordv150": "Vord V150", "vehicletypename_vordpony": "Vord Pony", "employeehelper_notification_employee_retired": "{name} is met pensioen gegaan ({businessName})", "ticket_house": "Casino Tickethuis", "casinoboatmanager_leaveboat": "Weet je zeker dat je vooruit wilt spoelen naar het einde van de to<PERSON>?", "casinoboat_ticket_pay_item": "Casino Boot Ticket", "dialog_blackjack_start": "Welkom bij blackjack!", "dialog_blackjack_dealer": "Dealer", "dialog_blackjack_score": "Score: {score}", "dialog_blackjack_player": "<PERSON><PERSON><PERSON>", "dialog_blackjack_hit": "Hit", "dialog_blackjack_stand": "Stand", "dialog_blackjack_insurance": "Verzekering", "dialog_blackjack_surrender": "Opgeven", "dialog_blackjack_both_blackjack": "Zowel de dealer als de speler hebben een blackjack. <PERSON>n <PERSON>!", "dialog_blackjack_player_blackjack": "Blackjack voor de speler! <PERSON><PERSON><PERSON> wint {prize}", "dialog_blackjack_surrender_result": "Je gaf op. De helft van je inzet is terugbetaald.", "dialog_blackjack_dealer_blackjack_insurance": "Dealer blackjack! S<PERSON>er krijgt het geld terug van de verzekering.", "dialog_blackjack_dealer_blackjack_no_insurance": "Dealer blackjack! Volgende keer beter!", "dialog_blackjack_tie": "Dezelfde resultaten voor zowel de speler als de dealer! De inzet wordt teruggegeven.", "dialog_blackjack_player_wins": "<PERSON><PERSON><PERSON> wint {prize}!", "dialog_recruitment_no_skills_available_notification": "Dit wervingsbureau heeft geen beschikbare vaardigheden voor het geselecteerde bedrijf. Probeer het eens bij een ander wervingsbureau", "bizman_store_furniture_delivery_title": "{businessName}-v<PERSON><PERSON><PERSON> ({currentAmount}/{maxAmount} items geselecteerd)", "happinessmodifiertype_gambled": "Gegokt in het casino", "bizman_store_furniture_delivery_add_to_cart": "Voeg toe aan wink<PERSON>kar", "businessemployeecontroller_notification_cant_use_with_item_in_hand": "Je kunt niet gaan zitten terwijl je een voorwerp vasthoudt", "carcontroller_no_exitpositon_found": "Er is geen besch<PERSON>e plek gevonden voor het voertuig. Verplaats het voertuig alstublieft naar een andere positie", "transactiontype_casinoboatticket": "Casino Boot Ticket", "itemoverlay_items_attached_headline": "Bijgevoegde artikelen", "common_distance_feet": "{distance} meter", "menu_options_others_time_format": "Gebruik 12-<PERSON><PERSON> tijdnotatie", "menu_options_others_unit_system": "Gebruik het imperiale systeem", "main_menu_story_mode": "Ver<PERSON>al modus", "main_menu_custom_game": "Aangepast spel", "main_menu_new_game_start": "Start het spel", "main_menu_custom_game_configure": "Spel configureren", "main_menu_story_mode_new_players_recommendation": "Aanbevolen voor nieuwe spelers", "main_menu_story_mode_description": "Begin je reis als ondernemer met hulp en begeleiding van je oom <PERSON>.", "main_menu_story_mode_recommended_for_first_playthrough": "Aanbevolen voor je eerste keer spelen", "main_menu_story_mode_guidance_and_objectives": "Begeleiding en doelstellingen", "main_menu_story_mode_unlocks_achievements": "Ontgrendelt prestaties", "main_menu_custom_game_description": "Pas zelf de variabelen aan voor de perfecte spelervaring.", "main_menu_custom_game_not_recommended_for_new_players": "<PERSON>et aanbevolen voor nieuwe spelers", "main_menu_custom_game_no_guidance_or_objectives": "<PERSON>r zijn geen <PERSON> of doelstellingen om te volgen", "main_menu_custom_game_sandbox_experience": "Volledige sandbox-ervaring zonder voortgangsbeperkingen", "main_menu_custom_game_achievements_disabled": "Prestaties uitgeschakeld", "main_menu_custom_game_starting_age": "Beginleeftijd", "main_menu_custom_game_disable_aging": "<PERSON><PERSON><PERSON> het ouder worden uit", "main_menu_custom_game_all_courses_unlocked": "Alle cursussen ont<PERSON>deld", "main_menu_custom_game_days_per_year": "Dagen per jaar", "main_menu_custom_game_starting_money": "<PERSON><PERSON> kapitaal", "main_menu_custom_game_tax_percentage": "Belastingpercentage", "main_menu_custom_game_market_price_multiplier": "Multiplier voor publieke prijzen", "main_menu_custom_game_employee_hourly_salary_multiplier": "Vermenigvu<PERSON><PERSON> van het uur<PERSON><PERSON> van de werknemer", "main_menu_custom_game_bank_interest_multiplier": "Multiplier voor bankrente", "itemname_loudspeaker1": "JayBeeel-luidspreker 1", "itemname_loudspeaker2": "JayBeeel-luidspreker 2", "dialog_furniture_delivery_minimum_price_not_reached": "De totale prijs moet minimaal {minimumCost} zijn", "city_map_cant_use_while_in_placement_mode_notification": "<PERSON><PERSON>gle Maps niet openen tijdens het verplaatsen van een item", "itempanelui_notification_cant_sell_vehicle_at_current_position": "Kan op deze positie geen voertuig verkopen", "bizman_schedule_auto_fill": "Alles automatisch bijvullen", "tickethouse_handtruck": "Je handen moeten leeg zijn om de casinoboot te betreden", "difficulty_easy": "Makkelijk", "difficulty_normal": "Normaal", "difficulty_hard": "<PERSON><PERSON><PERSON>", "bizman_schedule_auto_fill_disabled_tooltip": "U heeft u een HR-manager nodig om deze functie te kunnen gebruiken", "bizman_hrmanagers_hint_seated_only": "Alleen HR-managers die zijn toegewezen aan een bureau kunnen worden bereikt", "bizman_menu_hr_manager_settings": "Instellingen HR-manager", "common_salary": "<PERSON><PERSON>", "bizman_hrmanager_employee_primary_skill": "{skill} ({value}%)", "bizman_hrmanager_salary": "{wage}/uur", "bizman_hrmanager_assign_employees": "Medewerkers toewijzen", "bizman_hrmanager_assigned_employees_amount": "{amount} van {max} toegewezen werknemers", "bizman_hrmanager_settings_replace_absent_employees": "Vervang afwezige medewerkers automatisch door tijdelijke medewerkers", "bizman_hrmanager_settings_replace_absent_employees_hint": "Let op: tijdelijke medewerkers kosten 10% meer dan de vervangen medewerker", "bizman_hrmanager_settings_train_employees": "Train medewerkers automatisch tot:", "bizman_hrmanager_average_salary": "<PERSON><PERSON><PERSON><PERSON><PERSON> salaris", "bizman_hrmanager_average_satisfaction": "Gemiddel<PERSON> tevredenheid", "bizman_hrmanager_average_primary_skill": "Gemiddeld percentage primaire v<PERSON><PERSON><PERSON><PERSON><PERSON>", "bizman_hrmanager_assign_employees_title": "Werknemers toewijzen aan {manager} ({amount}/{max})", "common_assigned": "Toegewezen", "broken_savegames": "Corrupte save-games gedetecteerd!", "menu_brokensavegames_description": "<PERSON>r zijn een of meer beschadigde savegames gedetecteerd.", "menu_brokensaves_delete": "Verwijder corrupte savegames (aanbevolen)", "menu_brokensaves_move": "Verplaats corrupte savegames (aanbevolen)", "menu_brokensaves_ignore": "<PERSON><PERSON><PERSON> corrupte savegames", "common_area_feet": "{area} ft²", "common_area_meters": "{area} m²", "vehicletypename_umcnunavut": "UMC Nunavut", "skillname_hrmanager": "HR Manager", "bizman_hrmanager_max_employees_reached": "<PERSON><PERSON> niet meer werknemers toewijzen aan deze HR-manager", "bizman_hrmanager_fill": "<PERSON><PERSON><PERSON>", "main_menu_custom_game_disable_energy": "Schakel energie uit", "main_menu_custom_game_disable_happiness": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "happinessmodifiertype_cheat": "<PERSON><PERSON>", "transactiontype_hrtraining": "{employee} trainingskosten", "itemname_eameschair": "<PERSON>-stoel", "itemname_eamesottoman": "James Lawns Ottomaans", "transactiontype_replacementwage": "Vervanging voor {employee} ({businessName} loon)", "vehicletypename_flatbed": "Oplegger", "bizman_settings_terminate_contract": "<PERSON><PERSON> hieronder om je huurcontract voor dit gebouw te beëindigen. De volledige aanbetaling wordt terugbetaald.", "help_itemname_itemgrouppointofsale_content": "**Kassasysteem** is verplicht in retail bedrijven voor het afhandelen van verkopen aan consumenten.\n\nDe volgende meubels vallen onder kassasysteem:\n* [<PERSON><PERSON>](furniture-cashregister)\n* [<PERSON><PERSON><PERSON><PERSON> (Links)](furniture-checkoutcounterleft)\n* [<PERSON><PERSON><PERSON><PERSON> (Rechts)](furniture-checkoutcounterright)", "uncle_fred_tutorial_nicer_apartment": "Heb je een telefoontje gekregen van de bank? Ze zullen vast geschokt zijn als ze zien wat er met je account gebeurt, haha... de manier waarop je balans groeit! <br><br> Je zou een mooier appartement moeten zoeken. En koop een paar hele mooie meubels voor jezelf. Je verdient het!", "tutorial_nicer_apartment_objective_1": "Huur een appartement (residentieel) van minimaal 90 m²", "tutorial_nicer_apartment_objective_2": "<PERSON><PERSON> een <b>kingsize bed</b> van I<PERSON>lag", "dialog_recruitment_agency_cancel_campaign": "<PERSON><PERSON><PERSON> annu<PERSON>en", "dialog_recruitment_agency_start_new_campaign": "Start nieuwe campagne", "dialog_recruitment_agency_already_has_campaign_continue": "Kunnen we je nog ergens mee helpen?", "dialog_recruitment_agency_already_has_campaign_new": "<PERSON>o, hoe kunnen we u vandaag helpen?", "dialog_recruitment_agency_manage_campaigns": "<PERSON><PERSON><PERSON> beheren", "dialog_recruitment_agency_no_campaign_selected": "Sorry over welke campagne heb je het ook alweer?", "dialog_recruitment_campaigns_list_info": "{businessName}, {skillKey}, {scheduleTypes}. {daysLeft} dagen resterend, nog {amountOfCandidatesLeft} kandidaten van {amountOfCandidates} over", "vehicletypename_petrollsfanton": "<PERSON><PERSON>", "purchasecar_specs": "Specificaties", "purchasecar_colors": "<PERSON><PERSON><PERSON>", "vehicle_tank_capacity": "<PERSON><PERSON><PERSON><PERSON>", "vehicle_auto_park_support": "Ondersteuning voor automatisch parkeren", "vehicle_max_speed": "Maximale snelheid", "common_yes": "<PERSON>a", "common_no": "<PERSON><PERSON>", "tutorial_nicer_apartment_objective_3": "Plaats het kingsize bed in je nieuwe appartement", "common_area_meter_unit": "m²", "common_area_feet_unit": "ft²", "common_distance_meter_unit": "m", "common_distance_feet_unit": "ft", "bizman_hrmanager_clear": "<PERSON><PERSON>", "help_skillname_hrmanager_content": "Medewerkers met **HR Manager** vaardigheid worden gebruik voor het hoofdkantoor.\n\nVoor elke medewerker die aan de HR Manager is toege<PERSON><PERSON>, zal HR:\n* de vaardigheden van de medewerker verhogen terwijl hij werkt\n* een uitzendkracht regelen die de dienst van de werknemer kan overnemen als deze ziek wordt\n* een [Zorgverzekeringsplan](employee_demands_benefits_healthinsurance) van het [Ziekenhuis](address: 0 7s) beheren\n\nHet vaardigheidsniveau (%) bepaalt hoeveel medewerkers ze aankunnen en hoeveel het automatisch trainen van medewerkers kost. \n\nZe kunnen worden toegewezen aan:\n* [Computerwerkstation](furniture-computerworkstation)\n\nZe kunnen aangenomen worden bij:\n* [City Workforce Inc.](address: 41 4a)", "bizman_hrmanager_settings_train_employees_hint": "Let op: training is lang<PERSON>er maar goedkoper en medewerkers blijven aan hun taken werken", "uncle_fred_tutorial_marketing": "Laten we weer teruggaan naar je zaken, want je hebt nog veel te leren, jongen. Het gaat geweldig. O ja. Maar er zijn veel potentiële klanten die nog nooit van uw winkels hebben gehoord. Het is dus tijd om te beginnen met marketing.", "uncle_fred_tutorial_totalprofit": "Mo<PERSON>. Hey, zorg ervoor dat je de populariteit van je bedrijven erg hoog houdt. Soms zal je wat meer geld moeten uitgeven aan marketing om tot hetzelfde resultaat te komen als voorheen, dit omdat de markt elke dag verandert. <PERSON> trouwen<PERSON>, ik ben van plan om mijn huis te bezoeken, mijn v<PERSON>, in Costa del Sol. Maar wanneer ik terug ben, hoop ik dit te zien: dat je de dagelijkse inkomsten hebt verhoogd. Het is tijd om je rijk naar een hoger niveau te tillen, maar we hebben wel eerst meer geld nodig.", "hospital_respawn_timemachine_info": "Je wordt naar het ziekenhuis gebracht...", "casino_timemachine_info_start": "Reizen naar internationale wateren...", "casino_timemachine_info_end": "Reizen naar de stad...", "itemname_donut": "Donut", "help_itemname_donut_content": "**Donut** is een type product dat voornamelijk wordt verkocht in [Coffee Shops](businesstypes-coffeeshop).\n\nDaarnaast kan het worden verkocht in [Supermarkets](businesstypes-supermarket).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Bakery Showcase](furniture-bakeryshowcase)\n\nHet product kan worden gekocht op de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [SeaSide Internationals](address: 2 pier)\n\nHet product kan worden vervaardigd met behulp van de volgende recepten:\n* [Donut Recipe](recipes-donutrecipe)", "common_time": "{hours} uur {minutes} minuten", "playpanel_headline": "Speel videospellen", "playpanel_start_playing": "<PERSON><PERSON> met spelen", "happinessmodifiertype_playedvideogames": "Video games gespeeld", "itemhelper_notification_no_more_energy_generation": "Je voelt geen effect meer van de cafeïne.", "businessrequirement_atleastoneproduct": "Minimaal 1 primair product", "businessrequirement_pointofsales": "Kassa of kassabalie", "businessrequirement_computer": "Computerwerkstation", "businessrequirement_cashregister": "<PERSON><PERSON>", "businessrequirement_stackofshoppingbaskets": "<PERSON><PERSON><PERSON>", "tutorial_nicer_apartment_objective_4": "Zeg het contract van je oude appartement op", "tutorial_13_objective_3": "Koop een <b>industriële grill</b> en een <b>kast</b>", "tutorial_13_objective_4": "Plaats de kast in uw winkel en plaats de industriële grill er bovenop.", "tutorial_13_objective_5": "Vul de industriële grill aan met <b>Burgers</b>", "itemcustomizer_set_custom_queue": "Aangepaste wachtrij instellen", "itemcustomizer_reset_queue": "<PERSON><PERSON><PERSON><PERSON> resetten", "intro_start_game": "Start game", "intro_name_placeholder": "<PERSON><PERSON><PERSON> naam...", "intro_character_name": "Je kan me ... noemen", "intro_body": "<PERSON><PERSON><PERSON>", "intro_head": "Hoofd", "intro_clothes": "<PERSON><PERSON><PERSON>", "dialog_bank_selector": "Hallo! Hoe kan ik je vandaag helpen?", "econoview_investments_changeperiod": "Wijziging laatste 14 dagen", "econoview_investments_changetotal": "Totale verandering", "econoview_investments_order_payout": "Uitbetaling bestelling", "econoview_investments_initial_investment": "Initiële investering", "econoview_investments_current_value": "<PERSON><PERSON><PERSON> waarde", "econoview_investments_notenoughdata": "On<PERSON><PERSON><PERSON>e gegevens om de grafiek weer te geven", "econoview_investments_noinvestments": "U heeft geen beleggingen. Neem contact op met uw bank voor meer informatie.", "econoview_investments_confirmpayout": "Weet u zeker dat u het volledige saldo uit dit beleggingsfonds wilt opnemen?", "investmentfundname_euroenergyhigh": "Euro Energie Hoog", "investmentfundname_franklinus": "Franklin VS", "investmentfundname_alliancestechnologya": "Allianties Technologie A", "investmentfundname_laceglobala": "Lace Globaal A", "investmentfundname_asiadynamicindustries": "Aziatische Dynamische industrieën", "investmentfundname_hgchinabonds": "HG China-obligaties", "dialog_select_investment_fund": "Kies een beleggingsfonds", "common_investment_fund": "Beleggingsfonds", "common_risk": "Risico", "dialog_bank_investment_accepted": "Heel erg bedankt. Het volledige bedrag is belegd en u kunt het nu in uw portefeuille zien met de EconoView-app.", "dialog_bank_new_investment": "Nieuwe investering", "dialog_bank_new_loan": "<PERSON><PERSON><PERSON>", "itemname_gamingcomputer": "Basis Gaming PC Setup", "itemname_loudspeaker3": "P&S-Peolab 90", "itemname_loudspeaker4": "<PERSON><PERSON><PERSON><PERSON> (Klein)", "menu_options_controls_control_mode": "Besturingsmodus", "options_control_mode_mouse": "Aanwijzen en klikken (muis)", "options_control_mode_controller": "To<PERSON><PERSON>bord of controller", "npc_expression_no_place_to_sitdown": "Ik kan geen plek vinden om te gaan zitten.", "npc_expression_no_missing_trashbin": "Ik kan geen plek vinden om mijn afval neer te zetten.", "itemname_ceilinglamppinecone": "<PERSON><PERSON><PERSON>", "bizman_enter_price_placeholder": "<PERSON><PERSON><PERSON> prijs in", "tutorial_objective_reach_profit": "Bereik een dageli<PERSON><PERSON> winst van {moneyAmount}", "uncle_fred_tutorial_25": "Je begint het echt onder de knie te krijgen. Dus ik denk dat het tijd is om je te begeven op een nieuw terrein: kantoorbedrijven. Het is verge<PERSON><PERSON><PERSON><PERSON> met detailhandelsbedrijven, maar het is belangrijk om te overwegen wat er in jouw specifieke buurt in trek is. Je weet wat je moet doen. Dus doe het!", "uncle_fred_tutorial_26": "<PERSON>u, wil je hiernaar kijken? Al deze mensen zijn allemaal verkleed, zitten aan een bureau en verdienen geld voor jou. En dat gezegde: nog niet zo lang geleden was je nog maar net begonnen. Ik zeg je wat, ik denk dat het tijd is voor een klein feestje. Nu kun je niet in de stad gaan gokken, maar je springt over de Hudson en maakt een cruise buiten de internationale wateren. Het kost je misschien een paar tientjes. <PERSON><PERSON> luister, je bent niet meer alleen maar een blut kind.", "uncle_fred_tutorial_27": "En heb je iets gewonnen? <PERSON><PERSON>, het maakt niet uit. So<PERSON> is het goed om gewoon wat geld als je er gelukkig van wordt. Maar nu is het tijd om weer aan het werk te gaan. Begin je een beetje overweldigd te voelen door al die mensen van je die zich steeds ziek melden? Nou, je moet een HR-manager inhuren. Zij regelen al die dingen voor je.", "uncle_fred_tutorial_28": "Kijk eens aan. HR-managers zijn net alsof je elke dag meer uren voor jezelf koopt. Hé, ik moet gaan. Ik heb een belangrijk golfles. Haha. Ga je miljoenen verdienen en ik kom later bij je terug.", "tutorial_objective_rent_office_building": "<PERSON><PERSON> een kantoorgebouw", "tutorial_objective_start_lawfirm": "<PERSON>gin een <PERSON><PERSON>", "tutorial_objective_hire_lawyer": "<PERSON>rf een advocaat aan.", "tutorial_objective_hire_hr_manager": "<PERSON><PERSON> <PERSON>-manager in", "tutorial_objective_assign_lawyer": "Wijs de advocaat toe aan een nieuw bureau in uw advocatenkantoor", "tutorial_objective_assign_hr_manager": "<PERSON><PERSON><PERSON><PERSON>-manager toe aan een nieuw bureau in uw hoofdkantoor", "tutorial_objective_assign_employees_to_hr_manager": "Wijs minimaal <PERSON><PERSON> me<PERSON> toe aan uw HR-manager via de BizMan van het hoofdkantoor", "tutorial_objective_buy_ticket_to_casino": "<PERSON><PERSON> kaartje voor de casinoboot (alleen op vrijdag)", "tutorial_objective_2_mill_bank_balance": "Zorg dat er $ 2.000.000 op uw bankrekening staat", "bizman_purchasingagents_amount_target_on": "# doel", "npc_expression_no_path": "Het is onmogelijk om rond te komen in deze winkel. Ik ben hier weg!", "itemname_expensivetv": "P&S Sympathy 88\"", "click_to_play": "Klik om videospellen te spelen", "click_to_sleep": "Klik om te slapen", "bizman_browse_files": "<PERSON><PERSON><PERSON> verkennen", "common_refresh": "<PERSON><PERSON><PERSON><PERSON>", "common_investments": "Investeringen", "notification_cant_takeover_inside": "Je kunt een bedrijf niet overnemen als je binnen het gebouw zit.", "happinessmodifiertype_afreshstart": "<PERSON><PERSON>", "vehicletypename_mersaidimgagt": "Mersaidi MGA GT", "dialog_bank_investment_request_player": "Ik wil graag {amount} investeren in {investmentFund}", "dialog_bank_investment_header": "Investering", "transactiontype_investment": "{investmentFundName} investeren", "transactiontype_investmentpayout": "Uitbetaling van {investmentFundName}", "menu_options_controls_run_by_default_indoor": "<PERSON><PERSON><PERSON> binnen rennen", "transactiontype_banknegativeinterestrate": "Bank negatieve rente", "main_menu_custom_game_bank_negative_interest_rate": "Bank negatieve rente", "common_revert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "common_apply": "Toepassen", "myemployees_configure_uniform_presets": "Uniform voorinstellingen configureren", "myemployees_preset_customizer_generic_name": "Voorkeursinstelling #{number}", "myemployees_uniform": "uniform", "myemployees_preset_customizer_unsaved_changes_warning": "Niet-opgeslagen wijzigingen aan de voorinstelling gaan verloren. Doorgaan?", "myemployees_preset_customizer_remove_preset": "Weet u zeker dat u deze voorinstelling '{presetName}' wilt verwijderen?", "alert_low_stock": "{producer_itemname} heeft binnenkort geen <u>{itemname} meer!</u>", "alert_empty_stock": "{producer_itemname} heeft geen <u>{itemname} meer!</u>", "alert_missing_employee": "Bedrijf is open maar er werken geen werknemers.", "alert_missing_required_item": "Vereist ten minste één <u>{itemname}</u> .", "alert_missing_schedule": "<PERSON><PERSON><PERSON><PERSON> heeft geen openings<PERSON>jden gedefin<PERSON>erd", "alert_no_producers": "<PERSON><PERSON><PERSON><PERSON> hebben geen primaire producten om te verkopen", "alert_dirty_floors": "Zaken zijn vies en moeten worden schoongemaakt", "bizman_marketing_efficiency": "Marketingefficiëntie", "bizman_marketing_efficiency_description": "Marketinginspanningen veranderen van gebouw tot gebouw. G<PERSON><PERSON> bedrijven hebben grotere campagnes en een groter budget nodig om efficiënt te zijn.", "bizman_marketing_total_daily_expenses": "Totale dagelijkse uitgaven", "marketing_notification_no_money_campaigns_disabled": "Marketingcampagnes voor {businessName} zijn uitgeschakeld wegens onvoldoende geld", "marketingtypename_smallinternet": "Kleine internetcampagne", "marketingtypename_mediuminternet": "Middelgrote internetcampagne", "marketingtypename_largeinternet": "Grote internetcampagne", "marketingagencydialog_notification_campaign_already_running": "Er <PERSON>t al een campagne van dit type voor het geselecteerde bedrijf", "transactiontype_interiordesigner": "Interieur ontwerper", "itemcustomizer_set_colors": "Stel kleuren in", "itemcustomizer_color_label": "<PERSON><PERSON><PERSON> {number}", "transactiontype_furniturerecolor": "{itemName} her<PERSON><PERSON>", "itemcustomizer_no_colors_options": "Dit item heeft geen kleuropties", "common_default": "Standaard", "econoview_row_negative_interest_rates": "Negatieve rentetarieven", "itemname_groundplant01": "Plant in hoge vaas 1", "itemname_groundplant02": "Plant in hoge vaas 2", "itemname_groundplant03": "Plant in vierkante vaas", "itemname_groundplant04": "Plantenbak", "tutorial_17_objective_2": "<PERSON><PERSON> met het verkopen van dure cadeaus in je cadeauwinkel", "gamemanager_notification_autosave_started": "Automatisch opslaan ...", "menu_options_time_between_auto_saves": "Tijd tussen automatisch opslaan", "menu_options_max_auto_saves_per_game": "Maximale automatische opslagen per spel", "menu_options_time_between_auto_saves_mins": "{minutes} min", "menu_options_max_auto_saves_per_game_amount": "{amount} saves", "itemname_cornersofa01": "Preben-bank", "entertain_panel_play_headline": "Speel videospellen", "entertain_panel_watchtv_headline": "Televisie kijken", "entertain_panel_start_play_button": "<PERSON><PERSON> met spelen", "entertain_panel_start_watchtv_button": "<PERSON><PERSON> met tv kijken", "entertain_panel_stop_at": "Stoppen om {time}", "entertain_panel_boost_info": "{boost} zorgde voor {hours} uur meer geluk", "itemname_scorpiogamingsetup": "Scorpio Gaming Setup", "itemname_gamingchair": "Gaming stoel", "itemname_cupcake": "Cupcake", "econoview_full_transactions_title": "Transacties", "econoview_last_transactions_full_view_button": "Volledig overzicht van transacties", "econoview_full_transactions_export_to_csv": "Exporteren naar CSV", "econoview_full_transactions_all_types": "Alle typen", "econoview_full_transactions_all_days": "<PERSON>e dagen", "amountoption_all": "Alle bedragen", "amountoption_positive": "<PERSON><PERSON><PERSON><PERSON> bedragen", "amountoption_negative": "Negatieve bedragen", "common_label": "Label", "common_balance": "Balans", "transactiontype_rent_label": "<PERSON><PERSON>", "transactiontype_wage_label": "<PERSON><PERSON>", "transactiontype_revenue_label": "Winst", "transactiontype_loanpayout_label": "<PERSON><PERSON>", "transactiontype_parkingticket_label": "Parkeerbon", "transactiontype_depositreturn_label": "Waarborg teruggave", "transactiontype_playerjobsalary_label": "<PERSON><PERSON>", "transactiontype_unassignedwage_label": "<PERSON><PERSON>", "transactiontype_marketing_label": "Marketing", "transactiontype_cheat_label": "Bedriegen", "transactiontype_subwayride_label": "Metrorit", "transactiontype_recruitmentcampaign_label": "Aanwervingscampagne", "transactiontype_importdelivery_label": "Import levering", "transactiontype_importdeliveryrefund_label": "Terugbetaling van de importlevering", "transactiontype_deliverycontract_label": "Leveringscontract", "transactiontype_deliverycontractrefund_label": "Terugbetaling leveringscontract", "transactiontype_itempurchase_label": "<PERSON><PERSON><PERSON>", "transactiontype_loanpayment_label": "<PERSON><PERSON> a<PERSON>", "transactiontype_loanpayoff_label": "<PERSON>g afbetalen", "transactiontype_tuitionfee_label": "Collegegeld", "transactiontype_deposit_label": "Storting", "transactiontype_depositreturnfurniture_label": "Verzamelplek Meubels Terugbrengen", "transactiontype_employeetraining_label": "Werknemerstraining", "transactiontype_itemsold_label": "Artikel verk<PERSON>t", "transactiontype_vehiclebought_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> gek<PERSON>", "transactiontype_taxiride_label": "Tax<PERSON>t", "transactiontype_hospitalbill_label": "Ziekenhuisrekening", "transactiontype_buildingbought_label": "Gekocht Gebouw", "transactiontype_rentrevenue_label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactiontype_autotowservice_label": "Auto Sleepdienst", "transactiontype_taxpayment_label": "Belasting betaling", "transactiontype_buildingsold_label": "Gebouw Verkocht", "transactiontype_publicparking_label": "Openbare parkeerplaats", "transactiontype_casino_label": "Casino", "transactiontype_casinoboatticket_label": "Casino Boot Ticket", "transactiontype_hrtraining_label": "HR-opleiding", "transactiontype_replacementwage_label": "Vervangend loon", "transactiontype_investment_label": "Investering", "transactiontype_investmentpayout_label": "Investering Uitbetaling", "transactiontype_banknegativeinterestrate_label": "Bank negatieve rente", "transactiontype_interiordesigner_label": "Interieur ontwerper", "transactiontype_furniturerecolor_label": "Meubels opnieuw kleuren", "myemployees_mass_action_dropdown_placeholder": "Met de geselecteerde...", "myemployees_mass_action_uniform": "Instellen op uniforme voorinstelling {presetName}", "myemployees_mass_action_confirm": "Wilt u de volgende actie echt toepassen op {employeesAffected} werknemers: \"{action}\"", "myemployees_mass_action_no_employees_selected": "Geen medewerkers geselecteerd", "menu_options_controls_key_bindings": "Toetsbindingen", "menu_options_controls_key_bindings_change": "<PERSON><PERSON>", "menu_options_controls_key_bindings_reset": "Reset", "menu_options_controls_key_bindings_bind_tip": "U kunt opnieuw binden annuleren met ESC", "vehicletypename_anselmoaf90": "Anselmo AF90", "input_key_interact": "Interactie", "input_key_secondaryinteract": "Secundaire interactie", "input_key_specialinteract": "Speciale interactie", "input_key_cancel": "<PERSON><PERSON><PERSON>", "input_key_rotateleft": "<PERSON><PERSON><PERSON> naar links", "input_key_rotateright": "<PERSON><PERSON><PERSON> naar rechts", "input_key_confirm": "Bevestig", "input_key_nextoption": "Volgende optie", "input_key_previousoption": "Vorige optie", "input_key_menu": "<PERSON><PERSON>", "input_key_togglerunning": "<PERSON><PERSON><PERSON>", "input_key_skipsong": "Wijzig muziekstation", "input_key_sell": "Verkoop", "input_key_sleep": "Slaap", "input_key_opennotifications": "Open meldingen", "vehicletypename_vordtiaravic": "Vord Tiara Vic", "tutorial_22_objective_9": "Wijs de manager toe aan uw maga<PERSON>jn via het scherm Logistieke Managers op uw hoofdkantoor (BizMan)", "dialog_bank_amount_to_invest": "Bedrag om te investeren", "common_select_option": "Selecteer optie", "myemployees_mass_action_assign_uniform_title": "Selecteer een uniforme voorinstelling om aan werknemers toe te wijzen", "myemployees_mass_action_assign_uniform_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> aan uniforme voorinstelling {presetName}", "myemployees_mass_action_assign_business_option": "<PERSON><PERSON><PERSON><PERSON>", "myemployees_mass_action_assign_business_title": "Selecteer een bedrijf om toe te wijzen aan werknemers", "myemployees_mass_action_assign_business_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> aan bedrijf {businessName}", "myemployees_mass_action_cant_assign_business_to_training_employee": "Medewerker {<PERSON><PERSON><PERSON>} is aan het trainen en kan niet worden toegewezen aan een bedrijf", "myemployees_mass_action_train_primary_skill_confirm": "Train primaire v<PERSON><PERSON><PERSON><PERSON><PERSON>. Let op: toegewezen werknemers worden niet toegewezen en hun uurloon wordt verhoogd.", "myemployees_mass_action_fire_confirm": "Ontsla", "common_unassign": "<PERSON><PERSON>", "myemployees_mass_action_assign_business_unassign_confirm": "<PERSON><PERSON> aan bed<PERSON>", "common_no_option_selected": "<PERSON><PERSON> optie gese<PERSON>d", "radio_now_playing": "Nu aan het spelen...", "menu_options_controls_keyboard": "Toetsenbord", "menu_options_controls_controller": "Controller", "input_key_move": "Bewegen", "input_key_steering": "Sturen", "input_key_throttle": "<PERSON><PERSON><PERSON>", "input_key_brakes": "<PERSON><PERSON><PERSON>", "input_key_handbrake": "Handrem", "rebind_wait_for_input": "Wachten op invoer van {type}...", "rebind_wait_for_type_input": "Wachten op invoer van {type}...", "rebind_waiting": "<Wachten...>", "rebind_binding": "Bindende {name}.", "input_component_up": "Omhoog", "input_component_down": "Omlaag", "input_component_left": "Links", "input_component_right": "<PERSON><PERSON><PERSON>", "tutorial_5_objective_5": "<PERSON> een steekwagen", "item_customizer_reset_colors": "<PERSON><PERSON> k<PERSON>", "input_key_pause": "<PERSON><PERSON>", "tutorial_objective_open_law_firm": "Open advocate<PERSON><PERSON><PERSON>", "help_itemname_cupcake_content": "**Cupcake** is een type product dat voornamelijk wordt verkocht in [Coffee Shops](businesstypes-coffeeshop).\n\nDaarnaast kan het worden verkocht in [Supermarkets](businesstypes-supermarket).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Bakery Showcase](furniture-bakeryshowcase)\n\nHet product kan worden gekocht op de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [SeaSide Internationals](address: 2 pier)\n\nHet product kan worden vervaardigd met behulp van de volgende recepten:\n* [Cupcake Recipe](recipes-cupcakerecipe)", "rebinding_collision": "De invoer die u probeerde te binden, is al in gebruik", "input_expected_control_type_button": "Knop", "input_expected_control_type_vector2": "As", "dialog_marketing_agency_more_help_offered": "Kunnen we je ergens anders mee helpen?", "dialog_marketing_agency_start_new_campaign": "Nieuwe campagne", "happinessmodifiertype_watchedtv": "Tv gekeken", "itemname_restaurantbooth": "Restaurant Cabine", "tutorial_objective_return_to_the_city": "<PERSON>er terug naar de stad", "myemployees_mass_action_employee_has_no_valid_skills": "Medewerker {employeeName} heeft geen geldige vaardigheden voor het bedrijf {businessName}", "colors_orange": "<PERSON><PERSON><PERSON>", "econoview_row_parking_fees": "<PERSON><PERSON><PERSON><PERSON>", "boattypename_speedboat": "Dragonfly F1", "boattypename_yacht": "SeaKing 8000", "boattypename_luxuryyacht": "<PERSON><PERSON>we jachten EN", "boat_yearly_maintenance": "Jaarlijks onderhoud", "purchasevehicleui_notification_purchase_boat_successful": "Boot succesvol gekocht!", "transactiontype_boatbought_label": "Boot aangeschaft", "transactiontype_boatbought": "{boatName} aankoop", "transactiontype_boatyearlymaintenance_label": "Jaarlijks on<PERSON><PERSON><PERSON> van de <PERSON>", "transactiontype_boatyearlymaintenance": "{boatName} onderhoud", "transactiontype_boatsold_label": "Boot verkocht", "transactiontype_boatsold": "{boatName} verkocht", "boat_sell_confirmation": "Weet u zeker dat u {boatName} voor {price} wilt verkopen? Eventuele vooruitbetaalde onderhoudskosten worden niet terugbetaald", "boat_sleeppanel_header": "Ontspannen op de boot", "boat_sleeppanel_sleep_button": "Ontspannen", "input_key_autorun": "Automatisch rennen in-/uitschakelen", "main_menu_welcome_title": "Welkom bij Big Ambitions Early Access", "main_menu_welcome_message": "Allereerst, welkom bij het Big Ambitions Early Access Program! Wij zijn een kleine en volledig onafhankelijke gamestudio die iets nieuws en unieks probeert te brengen in het managementgenre. Omdat je hebt besloten om onze game te kopen, kunnen we doorgaan met het betalen van salarissen aan onze werknemers en de game verder verbeteren. <b>Daarom: heel erg bedankt, je bent een rockster! &lt;3</b>", "main_menu_welcome_message_language": "Taal", "main_menu_welcome_game_updates": "Abonneren op game-updates (optioneel):", "main_menu_welcome_game_updates_email_placeholder": "<EMAIL>", "businesstype_hospital": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_stoveoven": "<PERSON><PERSON><PERSON>", "itemname_bookshelf": "Boekenplank", "itemname_desktopcomputer": "ZanaMan-computer", "itemname_trafficsign01": "Verkeersbord 1", "itemname_trafficsign02": "Verkeersbord 2", "itemname_cheapcoffeemachine": "Goedkope koffiemachine", "dialog_doctor_npc_name": "Meneer Ternity", "dialog_doctor_start": "Hallo! Hoe kan ik u helpen?", "dialog_doctor_extended_warranty": "Vraag naar uitgebreide garantie", "dialog_doctor_too_young": "<PERSON>eel kunnen wij u geen speciale behandelingen aanbieden.", "dialog_doctor_operation_explanation_1": "Ahhh, jij bent het! <PERSON> heeft me al verteld dat ik je kon verwachten. Leuk je te ontmoeten. <PERSON><PERSON>, ik kom meteen ter zake. Wat we gaan doen is je injecteren met iets dat \"BA-1003-0817\" heet. Ik moet je vertellen, dit is niet goedge<PERSON>d, gecert<PERSON><PERSON> of zelfs maar bekend bij iets van de overheid. Dit is iets heel speciaals, dat we graag uit het zicht van het publiek houden.", "dialog_doctor_operation_explanation_2": "<PERSON><PERSON> paar minuten na de injectie zal uw lichaam beginnen met het <PERSON><PERSON><PERSON><PERSON><PERSON> van uw veroudering. Meestal zien we dat de leeftijd van het lichaam van onze cli<PERSON> met 10 jaar afnee<PERSON><PERSON>.", "dialog_doctor_operation_explanation_3": "Voor elke injectie wordt de DNA-blauwdruk moeilijker te ontwerpen. Daarom moeten we u voor elke injectie een klein beetje extra in rekening brengen.", "dialog_doctor_operation_explanation_proceed": "Wilt u doorgaan?", "hospital_operation_info": "Operatie uitvoeren...", "transactiontype_doctorappointment": "Doktersafspraak", "itemname_wardrobe": "Garderobe", "tutorial_4_objective_2": "Vind en huur een <b>winkelpand</b> van <b>maximaal {size}</b> in <b>Garment District</b> met behulp van <b>Voogle Maps</b>", "personalgoal_owned_vehicles": "Bezit in totaal {amount} voertuigen", "personalgoal_owned_boats": "In totaal {amount} boten bezitten", "personalgoal_apartment_value": "Bezit een appartement met meube<PERSON> ter waarde van minimaal {amount}", "personalgoal_apartments_rented": "Huur minimaal {amount} appartementen", "personalgoal_warehouses_rented": "Huur minimaal {amount} magazijnen", "personalgoal_bank_balance": "<PERSON><PERSON><PERSON> een <PERSON>aldo van {amount}", "personalgoal_business_weekly_income": "Bereik een wekelijks inkomen van {amount} voor <PERSON><PERSON>", "personalgoal_employee_max_skill": "Bereik niveau {amount}% van elke vaardigheid voor één werknemer", "personalgoal_boat_value": "Bezit een boot ter waarde van ten minste {price}", "personalgoal_taxi_rides": "Voltooi {amount} taxiritten", "personalgoal_vehicles_repairs": "Geef minimaal {amount} uit aan het repareren van voertuigen", "personalgoal_gas_spent": "Geef minimaal {amount} uit aan benzine", "personalgoal_age": "Vier je {amount}ste verja<PERSON>ag", "personalgoal_uncle_fred_objectives": "Voltooi {amount} van de <PERSON> van oom <PERSON>", "personalgoal_hr_managers": "Neem minimaal {amount} HR-managers in dienst en plan deze in", "personalgoal_lawyers": "<PERSON><PERSON> en plan ten minste {amount} advocaten in", "personalgoal_programmers": "Huur en plan minimaal {amount} programmeurs in", "personalgoal_deliverydrivers": "Huur en plan minimaal {amount} bezorgers in", "personalgoal_taxes_paid": "Betaal minimaal {amount} aan belastingen", "personalgoal_hospitalized": "Word {amount} keer gehospitaliseerd", "personalgoal_realestate": "Bezit minimaal {amount} gebouwen", "personalgoal_casinowin": "Win een totaal aantal van {amount} in het casino", "personalgoal_casinovisits": "Bezoek het casino minstens {amount} keer", "personalgoal_parkingtickets": "Ontvang minimaal {amount} parkeerkaarten", "personalgoal_interiordesigner": "Besteed minstens {amount} aan interieurontwerp", "personalgoal_dailycustomers": "Bereik minimaal {amount} klanten per dag voor <PERSON>én bedrijf", "loading_scene_loading": "<PERSON><PERSON> van {scene}", "loading_scene_unloading": "Uitladen {scene}", "loading_screen_loaded": "G<PERSON>den!", "loading_screen_mainscene": "Fundaties", "loading_screen_ui": "Interfaces", "loading_screen_buildings": "<PERSON><PERSON><PERSON><PERSON>", "loading_screen_exteriorprops": "Obstakels en andere ergernissen", "loading_screen_lightsandpostprocessing": "Zon en Maan", "loading_screen_streets": "Straten", "loading_screen_indoor": "Huizen en bedrijven", "loading_screen_mainmenu": "Hoofdmenu", "loading_screen_intro": "Introductie", "common_requires_dlc": "Voor deze content is de {dlc} DLC vereist", "tutorial_13_objective_6": "Voldoe aan alle vereisten voor uw winkel (Tip: Open BizMan Insight om de vereisten te bekijken)", "tutorial_nicer_apartment_objective_5": "Ko<PERSON> een bureau, een stoel en een computer bij IKA Bohag", "uncle_fred_tutorial_29": "Oh. Oh. <PERSON><PERSON><PERSON> mi<PERSON><PERSON> had ik niet verwacht. Ik hoop dat je die lening van $15.000 aan <PERSON> al hebt afbetaald? Hoe dan ook, je weet dat al dat geld op de bank hebben misschien goed voelt, maar zo slim is het niet. Ooit gehoord van negatieve rente? Dat betekent dat de bank geld wil voor het bewaren van je geld. En ten tweede verdien je vrijwel niets met al die miljoenen. Het zou veel logischer zijn om uw geld in een beleggingsfonds te stoppen.", "uncle_fred_tutorial_30": "Dat is een veel betere plek voor je dollars. <PERSON><PERSON><PERSON> me, geen enkele rijke man zal ooit veel geld in de bank laten. Beleggingsfondsen zijn de beste keuze. Het geld werkt voor jou, net zoals je werknemers. <PERSON><PERSON>, niet vergeten om voor jezelf te zorgen. Een beetje focussen op je blijheid. Het leven draait niet alleen maar om het verzamelen van geld.", "uncle_fred_tutorial_31": "Goed om te zien dat je lacht. <PERSON><PERSON><PERSON><PERSON>, dit wilde ik je nog vragen, heb je gemerkt dat er minder vekocht wordt in het Garment District? Je weet het maar nooit. Soms gaat het beter, soms gaat het slechter. Het beste wat je kan doen is het verspreiden van je bedrijf. Risicospreiden noemen we dit.", "uncle_fred_tutorial_32": "Alstjeblieft. Dit zal je imperium veel minder kkwetsbaar maken voor al die mogelijke marktveranderingen. Hoe dan ook, nu moet ik weer mijn golfswing gaan verbeteren. Hey, ni<PERSON> verge<PERSON>, die sangria in Marbella wacht nog steeds op je. Ik zie je later, jongen.", "uncle_fred_tutorial_33": "<PERSON><PERSON>, j<PERSON><PERSON>, weet je of het mogelijk is om vals te spelen bij golf? Dat moet ik hebben, <PERSON>. Ik kan hem niet verslaan. Ik kan hem niet verslaan. Ik word er gek van. <PERSON><PERSON><PERSON><PERSON>, hij vertelde me dat je waardering omhoog schiet. Dat is geweldig. <PERSON>ar onthoud wat ik je vertelde over risicospreiding. Hetzelfde geldt voor jouw beleggingen. Wat dacht je van een beetje baksteen en mortel? Ik denk dat je moet beginnen met beleggen in vastgoed.", "uncle_fred_tutorial_34": "<PERSON>jk eens naar dat getal, hè? Kun je het geloven? Zoveel geld. <PERSON><PERSON><PERSON> je je nog de goede oude tijd toen ik je huur moest betalen? Je begint je waarschijnlijk te realiseren dat geld verdienen je niet gelukkig maakt, toch? Dat is volkomen normaal. Het leven draait niet om geld verdienen. Het leven draait om het hebben van een doel en genieten van de reis. Hier is nog een doel voor je dat je vast en zeker zal uitdagen.", "uncle_fred_tutorial_35": "Weet je, ik heb die uitdaging net bedacht. Ik had nooit verwacht dat je het echt zou halen. <PERSON> bent geweldig. Onstuitbaar zelfs. Ik begin me zorgen te maken over mijn nummer één positie op de miljardairslijst. H<PERSON>, krijg geen ideeën, kind.", "tutorial_29_objective_1": "Investeer minimaal $1.000.000 in beleggingsfonds(en)", "tutorial_31_objective": "Bezit 2 bedrijven in {neighbourhood}", "tutorial_32_33_objective_1": "Bere<PERSON> een totaal vermogen van {valuation}", "tutorial_33_objective_1": "<PERSON><PERSON> een gebouw", "tutorial_34_objective_1": "Bezit 80 bedrijven in de stad", "tutorial_35_objective_2": "Be<PERSON>ik een leeftijd van 65 jaar", "tutorial_36_objective_1": "Onderga de speciale operatie in het Midtown Hospital", "happinessmodifiertype_restedonaboat": "Uitgerust op een boot", "autotowservicedialog_notification_no_free_space": "<PERSON>ets blokkeert de sleepbestemming. Verwijder eerst eventuele voorwerpen of voertuigen.", "personalgoal_items_in_stock": "Sla ten minste {amount} van elk product op", "systemrequirements_header": "Systeemvereisten", "credits_gameproducer": "Gameproducent", "credits_translations": "Vertalingen", "credits_musiclicensing": "Muzieklicenties", "menu_forum": "Forum", "help_itemname_industrialfreezer_content": "**Industriële vriezer** kan worden gebruikt voor de verkoop van:\n\n* [Diepvriesproducten](producten-diepvriesproducten)\n* [IJs](producten-ijs)\n\n**Productcapaciteit:** \n* Diepvriesproducten: 50\n* IJs: 300\n**Klantcapaciteit:** 30\n\nHet meubilair kan worden gekocht bij de volgende locaties:\n* [Square Appliances](adres: 16 4a)", "color_picker_hex_value": "Hexadecimale waarde", "click_to_get_mop": "Klik om een dweil te krijgen", "click_to_change_clothes": "Klik om van kleding te wisselen", "click_to_watch": "Klik om tv te kijken", "employee_contact_description": "Medewerker", "employee_contact_message_retire": "Hoi baas!\n\n<PERSON><PERSON><PERSON>, de tijd vliegt! <PERSON><PERSON><PERSON> is mijn laatste dag als {skillKey}. <PERSON><PERSON> ben 67 en kan e<PERSON><PERSON><PERSON> met pen<PERSON><PERSON> van het hamsterwiel.\n\nBedankt voor de baan!\n\nMet vrien<PERSON><PERSON><PERSON><PERSON> groet,\n{employeeName} ({businessName})", "itemname_framedcertificate": "Ingelijst certificaat", "itemname_roundtable": "<PERSON><PERSON>", "itemname_moderncounter": "Moderne kast", "itemname_moderncountercorner": "Moderne kasthoek", "itemname_officelockers": "<PERSON><PERSON><PERSON>", "itemname_exitsign": "Uitgangsbord", "itemname_beltbarrier": "wachtrij post", "itemname_ropebarrier": "wachtrij post", "itemname_glassbarrier": "<PERSON><PERSON><PERSON> bar<PERSON>", "itemname_clothingrackangled": "<PERSON><PERSON><PERSON>", "itemname_counterwithglasscornerright": "<PERSON><PERSON> met g<PERSON><PERSON><PERSON>", "itemname_paperbin": "<PERSON><PERSON><PERSON><PERSON>", "itemname_modernshelfhorizontal": "Moderne plank horizontaal", "item_customizer_waiting_line_add_point": "Voeg een positie toe aan de wachtrij door op [{interactKey}] te drukken", "input_key_openbugreport": "Bugrapport openen", "transactiontype_compatibilityfix": "{text}", "transactiontype_compatibilityfix_label": "Compatibiliteitsoplossing", "menu_options_controls_vehicle_mouse_input": "<PERSON><PERSON><PERSON> m<PERSON> rijden in", "dialog_uncle_fred_cancel_tutorial_start_dialog": "Hé jongen! Wat heb je nodig?", "dialog_uncle_fred_cancel_tutorial_confirm_dialog": "Oh... <PERSON><PERSON> je wilt van mij af? Ik zal je niet meer begeleiden. <PERSON><PERSON>, geen aanmoedigende telefoontjes meer... Weet je het zeker?", "dialog_uncle_fred_cancel_tutorial_end_dialog": "<PERSON><PERSON> jongen. <PERSON><PERSON> je ooit van gedachten verandert, bel me. Tot z<PERSON>!", "dialog_uncle_fred_first_cancel_dialog_button": "Niets", "dialog_uncle_fred_second_cancel_dialog_button": "Laat maar zitten", "dialog_uncle_fred_cancel_tutorial_button": "tutorial annuleren", "dialog_uncle_fred_reenable_tutorial_start_dialog": "<PERSON>o jongen, heb je hulp nodig?", "dialog_uncle_fred_reenable_tutorial_end_dialog": "<PERSON>racht<PERSON>! Ik ga je weer wat tips geven!", "dialog_uncle_fred_cancel_reenable_button": "<PERSON><PERSON>", "dialog_uncle_fred_reenable_tutorial_button": "<PERSON><PERSON><PERSON> tutorial in", "help_itemname_clothingrackangled_content": "**Kledingrek (hoekig)** kan worden gebruikt om te verkopen:\n\n* [Kleding (klassiek goedkoop vrouwelijk)](products-classiccheapfemaleclothing)\n* [Kleding (klassiek goedkoop mannelijk)](products-classiccheapmaleclothing)\n* [Kleding (klassiek duur vrouwelijk)](products-classicexpensivefemaleclothing)\n* [Kleding (klassiek duur mannelijk)](products-classicexpensivemaleclothing)\n* [Kleding (modern goedkoop vrouwelijk)](products-moderncheapfemaleclothing)\n* [Kleding (modern goedkoop mannelijk)](products-moderncheapfemaleclothing)\n* [Kleding (modern duur vrouwelijk)](products-modernexpensivefemaleclothing)\n* [Kleding (modern duur mannelijk)](products-modernexpensivemaleclothing)\n\n**Productcapaciteit:** 25\n**Klantcapaciteit:** 5\n\nHet meubilair kan worden gekocht op de volgende locaties:\n* [AJ Pederson & Son](adres: 13 5a)", "notifications_title": "Notificaties", "notifications_remove_all": "Verwijder alle notificaties", "notifications_remove_all_confirm": "Weet je het zeker? Deze actie kan niet ongedaan worden gemaakt", "notifications_no_notifications": "Er zijn geen notificaties om te tonen", "bizman_settings_save_logo": "Logowijzigingen opslaan", "bizman_settings_sign_appearance_info": "<PERSON><PERSON>", "transactiontype_furnituredeliveryrefund": "{businessName} heeft de bezorging voor {address} gean<PERSON><PERSON><PERSON>", "transactiontype_furnituredeliveryrefund_label": "Terugbet<PERSON> meubellevering", "playeractivityui_timemachine": "<PERSON><PERSON><PERSON><PERSON>", "playeractivityui_fast_forward": "Tijd versnellen", "playeractivityui_time_remaining": "{hours} uur, {minutes} minuten resterend", "playeractivityui_decrease_slider": "Verminderen", "playeractivityui_increase_slider": "<PERSON><PERSON><PERSON>", "workoutui_start": "<PERSON><PERSON> met trainen", "workoutui_stop": "Stop met trainen", "workouttype_jump": "Springen", "workouttype_run": "<PERSON><PERSON>", "workouttype_situps": "Sit ups", "workouttype_boxing": "<PERSON><PERSON><PERSON>", "workouttype_squats": "Squats", "workouttype_benchpressing": "Bankdrukken", "happinessmodifiertype_exercised": "Getraind", "entertainui_stop_play": "Stop met spelen", "entertainui_stop_watchtv": "Stop met tv kijken", "entertainui_slider_label_play": "Speel {entertainingHours} uur, {entertainingMinutes} minuten.\nGeeft een blijdschapsboost van {boostPercentage}% gedurende {boostHours} uur", "entertainui_slider_label_watchtv": "Kijk TV voor {entertainingHours} uur en {entertainingMinutes} minuten.\nGeeft een blijdschapsboost van {boostPercentage}% gedurende {boostHours} uur", "workui_headline_label_default": "<PERSON><PERSON>en", "workui_headline_label_with_job": "Werk als {jobName}", "workui_start": "<PERSON><PERSON> met werken", "workui_stop": "Stop met werken", "workui_no_shifts_for_today": "<PERSON>r zijn voor vandaag geen diensten toegewezen", "sleepui_bed_headline": "Slaap op bed", "sleepui_car_headline": "Slapen in voertuig", "sleepui_slider_label_boat": "Slaap voor {sleepHours} uur en {sleepMinutes} minuten.\nWordt wakker om {time}.", "sleepui_start_bed_button": "<PERSON><PERSON> met slapen", "sleepui_start_car_button": "<PERSON><PERSON> met slapen", "sleepui_start_bench_button": "<PERSON><PERSON> met rusten", "sleepui_start_boat_button": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "sleepui_stop_bed_button": "Stop met slapen", "sleepui_stop_car_button": "Stop met slapen", "sleepui_stop_bench_button": "Stop met rusten", "sleepui_stop_boat_button": "Stop met ontspannen", "studyui_slider_label": "<PERSON><PERSON>er voor {studyHours} uur en {studyMinutes} minuten. Eindigt om {time}.", "studyui_stop_button": "Stop met studeren", "studyui_studying_until": "<PERSON><PERSON><PERSON> tot {time}", "input_key_sliderleft": "Schuifregelaar links", "exitzonedespawner_notification_cant_leave_without_paying_playerowned": "Je kunt de winkel niet verlaten met een winkel<PERSON>. Ga naar een verkooppunt om je producten te betalen.", "notification_cashregister_no_employee": "<PERSON>r zijn momenteel geen medewerkers aanwezig die je van dienst kunnen zijn", "workui_do_order": "Plaats bestelling (free)", "menu_options_game_speed": "<PERSON><PERSON><PERSON><PERSON>", "elevatoroverlay_header": "Lift", "business_description_gym": "Krachtige lichamen. Verheven g<PERSON>ten. Op<PERSON>len, <PERSON><PERSON><PERSON>, her<PERSON>n", "gym_enter_button": "Betaal (${price})", "gym_exit_button": "Uitgang", "transactiontype_gymentrance": "{businessName} ingang", "transactiontype_gymentrance_label": "Ingang sportschool", "contacts_remove_contact_confirm": "Weet je zeker dat je dit contact wil verwijderen?", "contacts_header_clear_chat_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> chat", "contacts_clear_chat_confirm": "Weet je zeker dat je alle berichten van dit contact wil verwijderen?", "contacts_remove_message_confirm": "Weet je zeker dat je dit bericht wil verwijderen?", "input_key_performactionwithoutconfirm": "Voer actie uit zonder bevestiging", "help_vehicletypename_anselmoaf90_content": "**Anselmo AF90** is een type voertuig dat je kunt kopen.\n\nVoertuigspecificaties:\n* Vrachtcapaciteit: 3\n* Brandstofcapaciteit: 68\n* Ondersteuning voor automatisch parkeren: Ja\n* Maximale snelheid: 100\n* Totaalprijs: $ 520.000\n* Belasting aftrekbaar: Nee\n\nHet voertuig kan worden gekocht op de volgende locaties:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_bima320_content": "**Bima 320** is een type voertuig dat je kunt kopen.\n\nVoertuigspecificaties:\n* Vrachtcapaciteit: 10\n* Brandstofcapaciteit: 57\n* Automatische parkeerondersteuning: Nee\n* Maximale snelheid: 70\n* Totaalprijs: $ 48.000\n* Belasting aftrekbaar: Nee\n\nHet voertuig kan worden gekocht op de volgende locaties:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_mersaidimgagt_content": "**Mersaidi MGA GT** is een type voertuig dat u kunt kopen.\n\nVoertuigspecificaties:\n* Laadvermogen: 5\n* Brandstoftankinhoud: 66\n* Automatische parkeerondersteuning: Ja\n* Maximumsnelheid: 100\n* Totaalprijs: $ 220.000\n* Fiscaal aftrekbaar: Nee\n\nHet voertuig kan worden gekocht bij de volgende locaties:\n* [Manhattan Luxury Cars] (adres: 15 bw)", "help_vehicletypename_petrollsfanton_content": "**Petrolls Fanton** is een type voertuig dat u kunt kopen.\n\nVoertuigspecificaties:\n* Laadvermogen: 8\n* Brandstoftankcapaciteit: 80\n* Automatische parkeerondersteuning: Ja\n* Maximale snelheid: 80\n* Totale prijs: $ 720.000\n* Belastingaftrekbaar: Nee\n\nHet voertuig kan worden gekocht op de volgende locaties:\n* [Manhattan Luxury Cars] (adres: 15 bw)", "common_bizphone": "BizPhone", "help_common_bizphone_content": "Met je **BizPhone** kun je snel zaken doen, waar je ook bent.\n\nBizPhone-apps:\n\n* **Persona** - bekijk je persoonlijke statistieken en doelen.\n* **Contacten** - bel bedrijven waarmee je eerder contact hebt gehad, zoals de bank of recruiters!\n* [**Rivals**](rivals-overview) - Volg hoe goed uw rivalen presteren en volg eventuele aanvallen/rivaliteiten op u.\n* [**MyEmployees**](skill-myemployees) - <PERSON><PERSON><PERSON> de werving van medewerkers, bedrijfsopdrachten, trainingen en tevredenheid.\n* **BizMan** - Behee<PERSON> de roosters, uniformen, prijzen en marketing van uw bedrijf.\n* [**Econoview**](finance-econoview) - Bekijk uw leningen, investeringen, laatste transacties en winst-en-verliesrekeningen.\n* **Voogle Maps** - Krijg een overzicht van de stad en gebruik kaartfilters om locaties te markeren en waypoints in te stellen.\n* **MarketInsider** - Krijg een kijkje achter de schermen bij concurrentie, vraag, importprijsindex en marktgebeurtenissen.", "help_businesstype_fruitandvegetablestore_content": "**Fruit- en groentewinkels** zijn detailhandelsbedrijven.\n\nKlanten zijn egoïstisch.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [Stapel winkelmandjes] (meubelstapel winkelmandjes)\n* [Staande digitale weegschaal] (staande digitale weegschaal)\n* [Verkooppunt](meubelitemgroepverkooppunt)\n* Minstens één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen voornamelijk:\n\n* [Appel](producten-appel)\n* [Banaan](producten-banaan)\n* [Wortel](producten-wortel)\n* [Sla](producten-sla)\n* [Peer](producten-peer)\n* [Tomaat](producten-tomaat)\n\nEn kan daarnaast verkopen:\n\n* [Salade](producten-salade)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klantenservice] (vaardigheid-klantenservice)\n* [Reiniging] (vaardigheidsreiniging)", "help_itemname_tomato_content": "**Zak tomaten** is een type product dat voornamelijk wordt verkocht in [Fruit and Vegetable Stores](businesstypes-fruitandvegetablestore).\n\nDaarnaast kan het worden verkocht in [Supermarkets](businesstypes-supermarket).\n\n**Let op:** Een [Staande digitale weegschaal](furniture-standingdigitalscale) is vereist om fruit en groenten te verkopen.\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Houten productkrat](furniture-woodenproductkrat)\n\nHet product kan worden gekocht op de volgende locaties:\n* [Total Produce Trading](adres: 6 6a)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)\n* [Lunar Tide Shipments](adres: 6 pier)", "furniture_restaurantbooth": "Restaurant stand", "help_itemname_graphictabletwithscreen_content": "**Grafisch tablet (met scherm)** kan worden toegevoegd aan een [Computerwerkstation](meubilair-computerwerkstation) om te voldoen aan de \"Grafisch tablet met scherm\" [Employee Equipment Demand](employees-demands-equipment) \n\nHet meubilair kan worden gekocht op de volgende locaties: \n* [I<PERSON> Bohag](adres: 50 4s)\n* [Mr. Scott's Office Supplies](adres: 39 4a)", "help_itemname_mousepad_content": "**Muispad** kan worden toegevoegd aan een [computerwerkstation](meubel-computerwerkstation) om te voldoen aan de \"muispad\" [vraag van werknemersapparatuur](werknemers-eisen-apparatuur).\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON><PERSON>'s Kantoorbenodig<PERSON>eden](adres:39 4a)\n* [<PERSON><PERSON>](adres:50 4s)", "customers_title": "Klanten", "customers_demands": "<PERSON><PERSON><PERSON><PERSON>", "customers_traffic": "Verkeer / Marketing", "customers_buildinglimit": "Bo<PERSON>wl<PERSON><PERSON>", "help_skillname_graphicdesigner_content": "Werknemers met de vaardigheid **Grafisch ontwerper** worden gebruikt voor grafisch ontwerpers.\n\nGrafisch ontwerpers werken allemaal met digitale klanten om hun [Grafisch ontwerpersvergoeding (per uur)](vergoedingen-uurvergoedinggrafischontwerper) te verdienen.\n\nHet vaardigheidsniveau (%) bepaalt hoeveel klanten bereid zijn te betalen voor de diensten.\n\nZe kunnen worden toegewezen aan:\n* [Computerwerkstation](meubilair-computerwerkstation)\n\nZe kunnen worden ingehuurd bij:\n* [City Workforce Inc.](adres: 41 4a)", "importertypename_jetcargo": "Import van straalvracht", "importertypename_seaside": "Internationale kusten", "help_importers_overview_content": "**Importeren naar uw magazijn of fabriek**\n\nUw [Purchasing Agent](skill-purchasingagent) beheert voorraadorders in de BizMan-app op uw [BizPhone](general-bizphone). De voorraad die u kunt bestellen, is a<PERSON><PERSON><PERSON><PERSON><PERSON> van uw [importcontract](importers-contract). \n\n**Kies leveringstypen**\n\n*Eenmalig:*\nDe agent levert de ingevoerde hoeveelheid, ongeacht de hoeveelheid bestaande voorraad in het magazijn of de fabriek.\n\n*Auto-stock:*\nDe agent past de leveringshoeveelheid aan om ervoor te zorgen dat de totale voorraad in het magazijn of de fabriek gelijk is aan de streefwaarde. Als de streefwaarde al is bereikt, worden er geen producten geleverd.\n\n* *Voorbeeld:* Als u een streefbedrag van 500 hamburgers instelt, maar uw magazijn of fabriek heeft momenteel 300 hamburgers, zal uw inkoopagent er 200 meer bestellen, zodat uw totaal 500 is.\n\nU kunt selecteren hoe vaak u de automatische voorraad wilt: elke 1, 3 of 7 dagen.\n\n**Zorg voor succesvolle leveringen:**\n\nOm importorders succesvol te ontvangen in uw magazijnen of fabrieken om 02:00 (2 AM):\n* Elk aangewezen magazijn of fabriek heeft ten minste één [Pallet Shelf](furniture-palletshelf) nodig met voldoende vrije ruimte om de inventaris te ontvangen\n* Het totaalbedrag van uw importorder moet ook hoger zijn dan het minimale aankoopbedrag\n* U moet voldoende geld hebben om de bestelling te betalen.", "vehicles_title": "[b]Big Ambitions[/b] is een revolutionaire business simulator. <PERSON><PERSON> van niets naar iets en wordt de grootste ondernemen in New York door het openen van een klein bedrijf of het langzaam opbouwen van grote ketens zoals jij ze wil hebben.", "store_page_description_short": "[b]Big Ambitions[/b] is een revolutionaire bed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met roll<PERSON><PERSON>. <PERSON><PERSON> van niets naar de grootste ondernemer in New York door kleine bedrijven te openen of lang<PERSON><PERSON> grote bedrijven op te bouwen, zoals jij dat wilt.", "store_page_paragraph_1": "<PERSON><PERSON> van niets naar iets en wordt de meest prominente tycoon van New York. Start een bedrijf, werk slim, laat het bedrijf bloeien en geniet van het leven. Hoe wil je succesvol worden? Hoe lang kun jij het volhouden?", "store_page_paragraph_2": "Begin je eigen cadeauwinkel, supermarkt, café, advocate<PERSON><PERSON>or, kled<PERSON><PERSON><PERSON>, slij<PERSON><PERSON><PERSON>, bloemist en meer. Jij bepaalt wat succes is - of het nu gaat om een enkele winkel, een keten van fastfoodrestaurants, een groot bedrijf of het verdienen van miljoenen door websites te ontwikkelen.", "store_page_paragraph_3": "Sluit een lening af, huur een gebouw, ontwer<PERSON> het logo, renoveer de winkel, zorg voor voorraad, beheer uw geld, huur personeel in en bouw uw infrastructuur. Binnenkort moet u misschien producten uit de haven gaan importeren en deze via een netwerk van magazijnen distribueren.", "store_page_paragraph_5": "Heb je een bezorgwagen nodig? Ga naar een dealer en koop er een. Heb je eten nodig? Ga naar de supermarkt, maar zorg wel dat je thuis een koelkast hebt. Big Ambitions is een lifesim waarin je personage fysiek met de wereld moet communiceren om te overleven en te slagen.", "itemname_angledwoodendisplaystand": "<PERSON><PERSON><PERSON> ho<PERSON>", "itemname_flatwoodendisplaystand": "Displaystandaard (plat hout)", "itemname_standingdigitalscale": "Staande digitale weegschaal", "itemname_woodenproductcrate": "Houten productkrat", "itemname_apple": "Zak appels", "itemname_pear": "<PERSON><PERSON> peren", "itemname_banana": "<PERSON><PERSON>", "itemname_carrot": "<PERSON><PERSON> wortelen", "itemname_lettuce": "<PERSON><PERSON> sla", "itemname_tomato": "<PERSON><PERSON> to<PERSON>n", "itemname_laptop": "Laptop", "itemname_mousepad": "Muismat", "itemname_graphictablet": "Grafisch tablet", "itemname_graphictabletwithscreen": "Grafische Tablet Met Scherm", "itemname_shopentranceleft": "Winkelingang links", "itemname_shopentranceright": "Winkelingang rechts", "itemname_kettlebell2kg": "Ke<PERSON>bell 2 kg", "itemname_pullupbar": "Optrekstang", "businesstype_fruitandvegetablestore": "Groente- en fruitwinkel", "business_description_wholesalestore_3": "Alle soorten verse producten, van de boerderij tot bij u op het bedrijf", "skillname_graphicdesigner": "<PERSON><PERSON>", "job_demand_priority_high": "<PERSON><PERSON><PERSON>", "bizman_purchasingagents_repeating_delivery_explanation": "De agent zorgt ervoor dat het ingevoerde bedrag het totaal is dat be<PERSON><PERSON><PERSON><PERSON> is in het magazijn. <b>Als het doel al is bereikt, worden er geen producten geleverd.</b>", "business_description_car_dealership_3": "Snelheid. Stroom. Vaar in stijl door uw stad met onze luxe voertuigen", "uncle_fred_tutorial_interior_designer": "Goed gedaan! <PERSON>ar dit is niet genoeg om ze blij te maken! <PERSON><PERSON><PERSON> je hoort, vinden ze je muren en vloeren lelijk. Heh, weet je wat? Ik ben het er wel een beetje mee eens.", "uncle_fred_tutorial_improve_employee": "<PERSON><PERSON>, terug naar je klanten. <PERSON>w altijd klagende klanten, toch? <PERSON><PERSON>. <PERSON><PERSON>, maak je geen zorgen. Uiteindelijk zullen ze blij zijn en uw bankrekening ook. Maar op dit moment hebben we twee problemen: het klantenserviceniveau van uw medewerker en het ontbreken van een uniform!", "healthinsurancemanagerdialog_notification_select_manager": "Selecteer e<PERSON>-manager", "healthinsurancemanagerdialog_notification_select_plan_type": "Selecteer een abonnementstype", "dialog_health_insurance_partnership_hr_manager": "HR Manager", "hospital_health_insurance_manager_description": "Er<PERSON>or zorgen dat uw werknemers de beste zorgverzekering op de markt hebben.", "dialog_health_insurance_plan_offer_price": "Prijs per dag per medewerker: <b>{price}</b>", "dialog_negotiate_button": "Onderhandelen", "dialog_send_offer_button": "<PERSON><PERSON><PERSON><PERSON> verzenden", "healthinsuranceplantype_bronze": "Brons", "healthinsuranceplantype_silver": "Zilver", "healthinsuranceplantype_gold": "<PERSON>ud", "dialog_health_insurance_negotiation_start": "Hall<PERSON>. Het lijkt erop dat u niet tevreden bent met het aanbod. Wat stelt u voor?", "dialog_health_insurance_player_offer": "Ik stel {amount} per dag per medewerker voor.", "dialog_health_insurance_declined_player_offer": "Het spijt me, maar we zijn niet geïnteresseerd.", "dialog_health_insurance_accepted_player_offer": "Het lijkt erop dat we een deal hebben!", "dialog_player_offer_input": "<PERSON><PERSON><PERSON>", "dialog_negotiation_impossible_1": "Ik moet helaas bekennen dat we in deze onderhandelingen ver uit elkaar liggen.", "dialog_negotiation_impossible_2": "Dit aanbod slaat helemaal nergens op. Waar zie je ons voor aan?", "dialog_negotiation_bad_1": "Ik weet niet zeker of we hier ook maar in de buurt komen...", "dialog_negotiation_bad_2": "Ik denk dat u de service die wij bieden onderschat. <PERSON><PERSON> aanbo<PERSON> is niet realistisch.", "dialog_negotiation_acceptable_1": "Oh, hmm, interessant.", "dialog_negotiation_acceptable_2": "Dat vind ik leuk. Maar het is niet perfect.", "common_accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "common_declined": "gew<PERSON><PERSON><PERSON>", "dialog_health_insurance_partnership_employee_skill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-manager", "bizman_hrmanager_health_insurance_label": "Ziektekostenverzekering voor deze manager:", "bizman_hrmanager_health_insurance_info": "{planType} (dagelijks {pricePerDayAndEmployee} per werknemer, minimaal {minimumEmployees} werknemers)", "bizman_hrmanager_no_health_insurance_info": "Neem contact op met het ziekenhuis om een plan op te stellen voor deze HR Manager", "bizman_hrmanager_cancel_insurance_button": "<PERSON><PERSON><PERSON>", "bizman_hrmanager_cancel_insurance_confirm": "Weet u zeker dat u de huidige Zorgverzekering van deze HR Manager wilt opzeggen?", "transactiontype_healthinsurance_label": "Ziektekostenverzekering", "transactiontype_healthinsurance": "Dagelijkse ziektekostenverzekering ({werknemer})", "educationdoorcontroller_closed": "De school is momenteel gesloten. Alle cursussen zijn voorbij. Kom alstublieft later terug.", "workout_machine_already_in_use": "Deze machine is al in gebruik.", "menu_options_local_radio_songs": "Lokale radioliedjes", "menu_options_open_folder": "Open folder", "menu_options_song_files_found": "bestanden gevonden: {count}", "businessrequirement_scale": "<PERSON><PERSON> schaal", "workout_machine_gym_need_sport_clothes": "Je hebt sportkleding nodig om deze machine te gebruiken. Je kunt je omkleden in de kleedkamer", "bizman_temporarily_closed": "Tijdelijk gesloten", "npc_expression_no_scales": "Ehh, hoe meet ik het gewicht hiervan?", "dialog_health_insurance_manager_more_help_offered": "Is er nog iets waarmee ik je kan helpen?", "jobdemand_bronzehealthinsurance_description": "<PERSON>t toegewezen aan een HR-manager met een bronze<PERSON> (of superieur) partnerschap voor zorgverzekeringen", "jobdemand_silverhealthinsurance_description": "<PERSON>t toegewezen aan een HR-manager met e<PERSON> (of superieur) Health Insurance-partnerschap", "jobdemand_goldhealthinsurance_description": "<PERSON><PERSON> toegewezen aan een HR-manager met een Gold Health Insurance-partnerschap", "gymentrance_notification_cant_enter_with_vehicle_or_item": "Je mag de sportschool niet betreden met een voertuig of item in de hand", "workout_machine_cant_use_with_vehicle_or_item": "Om deze machine te gebruiken heb je lege handen nodig", "itempanelui_notification_cant_grab_while_in_activity": "Kan een item niet pakken tijdens een activiteit", "tutorial_improve_employee_objective_3": "Wijs uw medewerker opnieuw toe aan uw cadeauwinkel", "tutorial_13_objective_7": "Open het nieuwe bedrijf door \"Tijdelijk gesloten\" uit te schakelen", "notification_citymap_interiordesigner_open": "Interieurontwerper is geopend. Sluit deze eerst.", "tutorial_delivery_contract_objective_2": "Plaats het opbergrek in je cadeauwinkel", "tutorial_delivery_contract_objective_3": "Stel een leveringscontract op voor je cadeauwinkel bij NY Distro Inc", "tutorial_delivery_contract_objective_4": "Configureer het leveringscontract om minimaal 1 doos Goedkope Cadeaus per week te bezorgen met behulp van \"BizMan Deliveries\"", "common_gross_profit": "Brutowinst", "itemname_itemgroupscale": "Weegschaal", "bizman_hrmanager_insurance_demand": "Verzekeringsvraag", "studyui_diploma_price": "{pricePerHour} per uur", "contacts_manage_employee_button": "<PERSON><PERSON><PERSON> medewer<PERSON>", "employee_contact_message_new_demand": "Hey baas, tot nu toe heb ik hier met veel plezier gewerkt, maar in de toekomst heb ik een nieuwe eis: {jobDemandName}", "alert_business_temporarily_closed": "Is tijdelijk gesloten", "employee_demands": "<PERSON><PERSON><PERSON> van werknemers", "tutorial_12_objective_1": "Volg de cursus ‘Fundamentals of Business Administration’", "workouttype_pullups": "Optrekken", "dialog_health_insurance_manager_hr_manager_skill_too_low": "<PERSON><PERSON><PERSON> kunnen we deze aanbieding voor een {healthPlanType}-plan niet voortzetten omdat uw huidige HR-manager niet over de minimale vaardigheden beschikt die voor dit plantype vereist zijn.", "cashregister_no_paperbags": "<PERSON>r zijn geen papieren zakken besch<PERSON>", "itemname_djbooth": "DJ-booth", "itemname_discoball": "Discobal", "itemname_barstool": "Barkruk", "itemname_cocktailbar": "Cocktail bar", "itemname_cactusneon": "Cactus <PERSON>", "itemname_circleneon": "Cirkel Neon", "itemname_irisneon": "<PERSON>", "itemname_cocktailneon": "Cocktail Neon", "itemname_beer": "<PERSON><PERSON>", "itemname_margarita": "<PERSON><PERSON><PERSON>", "itemname_martini": "martini", "itemname_whisky": "Whisky", "itemname_windowedroomdivider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met raam", "itemname_pouf": "Gebloemde Ottoman", "itemname_decorativeshampoo": "Decoratieve shampoo", "itemname_hightable": "Hoge tafel", "itemname_classicloudspeakersmall": "Klassieke luidspreker (klein)", "itemname_classicloudspeakermedium": "Klassiek<PERSON> luidspreker (medium)", "itemname_classicloudspeakerbig": "Klassieke luidspreker (groot)", "itemname_hangingproductsignrectangular": "Productbord (rechthoekig hangend)", "itemname_hangingproductsignsquare": "Productbord (hangend vierkant)", "itemname_wallproductsign": "Productbord (muur)", "itemname_previewterminal": "Voorbeeldterminal", "itemname_hairshampooingfee": "Haarshampoo en föhnkosten", "itemname_haircuttingfee": "Kosten voor het knippen van haar", "itemname_hairstylingfee": "Kosten voor haarstyling", "itemname_hairchemicalfee": "Haarchemicaliën/kleurkosten", "itemname_hairdresserchair": "<PERSON><PERSON><PERSON><PERSON>", "itemname_hairdresserchairmodern": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Modern)", "itemname_hairdresserheadwash": "<PERSON><PERSON> hoofdwas", "itemname_hairdressermirror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_hairdressershelf": "<PERSON><PERSON>", "itemname_hairdressersign": "<PERSON><PERSON> teken", "itemname_haircareproduct": "Haarverzorgingsproduct", "itemname_pictureframe2": "Fotolijst 2", "itemname_pictureframe3": "Fotolijst 3", "itemname_pictureframe4": "Fotolijst 4", "itemname_pictureframe5": "Fotolijst 5", "itemname_pictureframe6": "Fotolijst 6", "itemname_securitycamera": "Beveiligingscamera", "itemname_securitycameraroof": "Beveiligingscamera (koepel)", "itemname_securitypanel": "Beveiligingspaneel", "itemname_securityguardlocker": "Beveiligingskluisje", "itemname_securitysign": "Beveiligingsteken", "itemname_icecreamcounter": "IJsbalie", "itemname_icecream": "<PERSON>j<PERSON><PERSON>", "itemname_classicphone": "Klassieke telefoon", "itemname_officephone": "Ka<PERSON>or telefoon", "itemname_drinksshelf": "Drankenplank", "phone_wholesale_store_product_on_backorder": "Het spijt ons je te moeten mededelen dat {itemName} momenteel niet op voorraad is en is uit<PERSON><PERSON><PERSON> van jouw levering aan {businessName}", "phone_import_product_on_backorder": "Het spijt ons je te moeten mededelen dat {itemName} momenteel niet op voorraad is en is uit<PERSON><PERSON><PERSON> van jouw levering aan {businessName}", "wholesale_store_item_out_of_stock": "Dit product is momenteel niet op voorraad.", "common_undo": "Ongedaan maken", "common_redo": "Opnieuw", "input_key_interiordesignerundo": "Interieurontwerp Ongedaan maken", "input_key_interiordesignerredo": "Interieurontwerp herstellen", "input_key_interiordesignerpicker": "Interieurontwerperkiezer", "businesstype_nightclub": "Nachtclub", "businesstype_hairdresser": "<PERSON><PERSON>", "transactiontype_entrancefee": "Toegangsprijs voor {businessName}", "buildingmanager_entrance_fee_confirm": "Voor toegang tot het bedrijf {businessName} geldt een toeslag van {entreeFee}. Wil je doorgaan?", "bizman_product_shortage_warning": "Er is momenteel een tekort aan dit product. Er moeten hogere prijzen worden verwacht.", "bizman_product_backorder_warning": "Dit product is momenteel in nabestelling. Er moet rekening worden gehouden met lege voorraden.", "skillname_dj": "DJ", "skillname_hairstylist": "Haar stylist", "skillname_securityguard": "Beveiligingsagent", "skillname_headhunter": "Headhunter", "vehiclespawner_rent_confirm": "Weet u zeker dat u dit voertuig wilt huren voor {price}?", "vehicletypename_electricscooter": "Elektrische scooter", "vehiclespawner_rent_price": "Huurprijs: {price}", "escooter_cant_carry_item": "Tijdens het besturen van dit voertuig mag u alleen een papieren zak meenemen.", "input_key_selectmultipleelements": "Selecteer <PERSON><PERSON><PERSON>en", "contacts_taxes_tax_others": "Anderen:", "contacts_taxes_gambling": "Gokwinsten", "common_blueprints": "Blauwdrukken", "common_building_size": "<PERSON><PERSON><PERSON> van het gebouw", "common_all": "Allemaal", "blueprints_author": "Door {auteur}", "blueprints_author_you": "jij", "blueprints_addtoyourlibrary": "Voeg toe aan uw bibliotheek", "blueprints_uploadtosteamworkshop": "Uploaden naar Steam Workshop", "blueprints_no_downloads": "Geen downloads", "blueprints_one_download": "1 download", "blueprints_downloads_amount": "{downloadsAmount} downloads", "blueprints_downloads_amount_thousands": "{downloadsInThousands}k downloads", "blueprints_downloads_amount_millions": "{downloadsInMillions}M downloads", "blueprints_list_element_no_steam_info": "Lokaal opgeslagen", "blueprintcategory_gallery": "<PERSON><PERSON><PERSON>", "blueprintcategory_mylibrary": "Mijn bibliotheek", "requirement_placed_at_correct_height": "Dit item is op deze hoogte niet bruikbaar", "workout_machine_requirements_not_met": "Dit apparaat mist vereisten en kan niet worden gebruikt", "blueprints_ui_invalid_name": "Vul alstublieft een geldige naam in", "blueprints_ui_name_overwrite_confirm": "Er bestaat al een lay-out met deze naam. Weet u zeker dat u deze wilt overschrijven?", "blueprints_ui_save_layout_as_blueprint": "Sla de lay-out op als blauwdruk", "blueprints_ui_blueprint_name_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueprints_ui_save_blueprint_button": "Blauwdruk opslaan", "blueprints_removefromyourlibrary": "Verwijderen uit uw bibliotheek", "blueprints_ui_confirm_deleting_from_library": "Weet u zeker dat u deze blauwdruk uit uw bibliotheek wilt verwijderen?", "main_menu_delete_character_confirm": "Wilt u het karakter \"{characterName}\" echt verwijderen?", "main_menu_delete_save_game_confirm": "Wil je de opgeslagen game \"{saveGameName}\" echt verwijderen?", "blueprints_ui_shelf_stock_disclaimer": "Bevat geen inventaris", "blueprints_ui_workshop_item_uploaded": "Blauwdruk succesvol geüpload naar Steam Workshop!", "npc_expression_no_coat_checks": "Ik kan geen plek vinden om mijn jas op te bergen!", "npc_expression_no_available_coat_checks": "Ik ga niet zo lang wachten, enkel en alleen om mijn jas op te hangen!", "npc_expression_no_dance_floors": "Oh, er is geen dansvloer! Dat is echt triest", "npc_expression_no_available_dance_floors": "<PERSON>k z<PERSON> graag willen dansen, maar de vloer staat vol!", "blueprints_ui_workshop_item_upload_failed": "Blueprint kan niet worden geüpload naar Steam Workshop. Probeer het opnieuw en als de fout zich blijft voordoen, rapporteer deze dan via het menu \"Een bug rapporteren (F2)\".", "blueprints_ui_upload_to_workshop_headline": "Upload item naar workshop", "blueprints_ui_upload_to_workshop_confirm": "Door dit item in te dienen, ga je ak<PERSON><PERSON> met de <u>servicevoorwaarden van Steam Workshop</u>", "blueprints_ui_workshop_terms_of_service": "Servicevoorwaarden van Steam Workshop", "blueprint_info_ui_downloads": "Downloads", "blueprint_info_ui_released": "Uitgegeven", "blueprint_info_ui_rating": "Beoordeling", "blueprintdata_price": "<PERSON><PERSON><PERSON><PERSON>", "blueprintdata_buildingtype": "Type gebouw", "blueprintdata_buildingsize": "<PERSON><PERSON><PERSON> van het gebouw", "blueprintdata_interiorscore": "Interieurscore", "blueprintdata_workstations": "Werkplekken", "blueprintdata_pointsofsales": "Verkooppunten", "blueprintdata_palletshelves": "Palletplanken", "blueprintdata_storageshelves": "Opbergplanken", "blueprints_ui_workshop_item_upload_failed_file_not_found": "Blueprint kon niet worden geüpload naar Steam Workshop: Blueprint-map verwijderd of niet <PERSON>jk", "dancetype_dance1": "Boogie", "dancetype_dance2": "<PERSON><PERSON><PERSON><PERSON>", "dancetype_dance3": "<PERSON><PERSON><PERSON>", "dancetype_dance4": "<PERSON>", "dancetype_dance5": "Verlegen dans", "dancetype_dance6": "Vuist pomp", "dancetype_dance7": "<PERSON><PERSON><PERSON><PERSON>", "dancetype_dance8": "Twist", "dancetype_dance9": "Smooth Operator", "blueprints_synctoworkshop": "Synchroniseren met werkplaats", "businesstype_interiorinstallationfirm": "Interieur <PERSON><PERSON><PERSON><PERSON><PERSON>", "specialbuilding_interiorinstallationfirm": "Interieur <PERSON><PERSON><PERSON><PERSON><PERSON>", "preview_terminal_preview_button": "Voorbeeldontwerp", "preview_terminal_select_building_type": "Selecteer gebouwtype", "preview_terminal_select_building_size": "Selecteer <PERSON> <PERSON><PERSON><PERSON> van het geb<PERSON>w", "preview_terminal_select_business_type": "Selecteer bedrijfstype", "preview_terminal_select_design": "Ontwerp selecteren", "happinessmodifiertype_wenttonightclub": "<PERSON><PERSON> naar de nachtclub", "itemname_loudspeaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialog_interior_installation_firm_npc_name": "Firma voor interieurmontage", "dialog_interior_installation_firm_start": "<PERSON>o en welkom bij {businessName}. Hoe kan ik je vandaag helpen?", "dialog_interior_installation_firm_select_notification_select_design": "Selecteer een ontwerp", "dialog_interior_installation_cant_install_in_unrented_building": "<PERSON>o, we hadden een installatie gepland voor het gebouw op {address}, maar het lijkt erop dat het gebouw niet meer op uw naam staat. We hebben de installatie geannu<PERSON>.", "dialog_interior_installation_couldnt_find_layout": "Het spijt ons heel erg, maar het ontwerp dat u heeft gekozen om de installatie uit te voeren voor uw gebouw op {address} is niet langer besch<PERSON>. Als u nog steeds ge<PERSON><PERSON><PERSON><PERSON><PERSON> bent, neem dan opnieuw contact met ons op en kies een ander ontwerp.", "dialog_interior_installation_not_enough_money": "<PERSON><PERSON>, het lijkt erop dat we u de kosten voor de binneninstallatie op {address} niet in rekening kunnen brengen. We hebben de bestelling geannuleerd. Zorg ervoor dat u voldoende geld heeft voor onze diensten.", "dialog_interior_installation_done": "Hallo! De installatie voor het pand op {address} is afgerond. Bedankt voor het gebruik van onze diensten!", "dialog_interior_installation_cant_install_while_inside_building": "We konden de installatie op {address} niet uitvoeren vanwege onverwachte activiteit binnen. We proberen het morgen opnieuw.", "transactiontype_interiorinstallation": "Interieurinstallatie door {businessName}", "transactiontype_interiorinstallation_label": "Interieur installatie", "common_retry": "Opnieuw proberen", "common_accept": "Accept<PERSON><PERSON>", "blueprints_ui_illegal_characters_warning": "Illegale tekens zijn verwijderd. Nieuwe naam: \"{fixedName}\"", "interior_design_love": "<PERSON><PERSON><PERSON>", "interior_design_modern": "Modern", "interior_design_pizza": "Pizza", "interior_design_lemon": "Citroen", "interior_design_dark": "<PERSON><PERSON>", "interior_design_sky": "<PERSON><PERSON>", "businessrequirement_loudspeaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialog_interior_installation_fee": "Installatiekosten", "dialog_interior_installation_layout_price": "Indeling Prijs", "dialog_interior_installation_day": "Installatie dag", "dialog_interior_installation_design": "Ontwerp", "dialog_interior_select_address": "Selecteer adres", "dialog_interior_select_day": "Selecteer installatiedag", "dialog_interior_select_design": "Ontwerp selecteren", "cant_enter_building_while_interior_installation": "Wegens werkzaamheden in het gebouw is het niet mogelijk om naar binnen te gaan", "common_total": "Totaal", "econoview_row_theft": "<PERSON><PERSON><PERSON>", "bizman_security_level": "{securityLevel} ({securityPercentage}%)", "bizman_security_level_poor": "Slecht", "bizman_security_level_average": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bizman_security_level_good": "Goed", "bizman_security_level_perfect": "Perfect", "itemoverlay_security_info_protected": "Dit item wordt bewaakt door beveiligingscamera's", "itemoverlay_security_info_not_protected": "Dit item wordt niet bewaakt door beveiligingscamera's", "businessrequirement_djbooth": "DJ-booth", "npc_expression_no_djs": "Er zijn geen DJ's! Wie speelt de muziek? Wat een oplichterij!", "requirement_placed_in_the_entrance": "Wordt bij de ingang gepla<PERSON>t", "blueprints_workshop_cant_upload_not_connected": "<PERSON>n de blauwdruk niet uploaden naar de Steam Workshop. Dit komt waarschijnlijk doordat je geen verbinding hebt met Steam of internet.", "blueprints_workshop_cant_load_blueprints": "<PERSON>n blauwdruk<PERSON> van Steam Workshop niet laden. Dit komt waarschijnlijk doordat je geen verbinding hebt met Steam of internet.", "requirement_placed_in_order_based_business": "<PERSON><PERSON><PERSON><PERSON><PERSON> in op bestelling geb<PERSON><PERSON>", "requirement_placed_in_retail_business": "G<PERSON><PERSON><PERSON><PERSON> in detail<PERSON><PERSON>", "djbooth_notification_cant_use": "Je kunt geen interactie hebben met dit item.", "entertain_panel_dj_headline": "DJ", "entertain_panel_start_dj_button": "<PERSON><PERSON> met DJ'en", "click_to_dj": "Klik om te DJ'en", "entertainui_stop_dj": "Stop met DJ'en", "entertainui_slider_label_dj": "DJ voor {entertainingHours} uur, {entertainingMinutes} minuten. Geeft een geluksboost van {boostPercentage}% gedurende {boostHours} uur", "happinessmodifiertype_djed": "<PERSON> de DJ", "npc_expression_no_scales_retail_business": "Hoe verwachten ze dat ik deze groenten en fruit weeg zonder weegschaal?", "myemployees_employees_tab": "Medewerkers", "myemployees_candidates_tab": "Kandidaten", "myemployees_mass_action_assignbusiness": "<PERSON><PERSON><PERSON><PERSON>", "myemployees_mass_action_trainprimaryskill": "Train primaire v<PERSON><PERSON><PERSON><PERSON><PERSON>", "myemployees_mass_action_assignuniform": "Uniform toewijzen", "myemployees_mass_action_hirecandidate": "Kandidaat aanwerven", "myemployees_mass_action_fire": "Ontsla<PERSON>", "order_based_self_service_no_stock": "<PERSON><PERSON> van de geselecteerde producten is op voorraad.", "myemployees_mass_action_discardcandidate": "Kandidaat weggooien", "myemployees_hourlywage": "<PERSON><PERSON>", "myemployees_schedule": "<PERSON><PERSON><PERSON>", "myemployees_recruitmentsource": "Wervingsbron", "myemployees_expiresin": "<PERSON><PERSON><PERSON><PERSON> in", "myemployees_assignbusiness": "<PERSON><PERSON><PERSON><PERSON>", "myemployees_hire": "Aanwerven", "myemployees_candidates_source_headhunter_default": "Headhunter", "myemployees_candidates_source_headhunter": "Headhunter: {headhunterName}", "common_hours": "{uur} uur", "recruitmentagencydialog_notification_select_schedule": "Selecteer ten minste één planningstype", "dialog_recruitment_agency_schedule_types": "Schematypen", "bizman_menu_headhunters": "Headhunters", "bizman_headhunters_hint_seated_only": "Alleen zittende headhunters zijn <PERSON>", "bizman_menu_headhunters_recruiting_header": "Rekru<PERSON>en", "bizman_menu_headhunters_automatic_replacement_header": "Automatische vervanging", "npc_expression_no_hair_care_products": "Ik kan mijn haar niet laten doen als er geen producten beschikbaar zijn!", "jobdemand_hasphone": "Elke telefoon", "jobdemand_hasofficephone": "Ka<PERSON>or telefoon", "phone_recruitment_agency_campaign_finished": "Hallo! We hebben de campagne afgerond. Alle kandidaten zijn naar uw MijnEmployees app gestuurd. Bedankt dat u zaken met ons doet!", "npc_expression_no_head_washers": "Het lijkt erop dat ik mijn haar niet kan laten wassen!", "npc_expression_no_hairdresser_chairs": "Oh... Er zijn geen stylisten beschik<PERSON>ar...", "bizman_headhunters_start_recruiting_button": "<PERSON><PERSON> met werven", "bizman_headhunters_view_current_recruits_button": "Bekijk huidige kandidaten", "bizman_headhunters_stop_recruiting_button": "Stop met re<PERSON><PERSON><PERSON><PERSON>", "bizman_headhunters_type_of_employee_title": "Type werknemer", "bizman_headhunters_type_of_employee_description": "Selecteer het type medewerker dat u aanwerft.", "bizman_headhunters_currently_recruiting_title": "<PERSON><PERSON> aan het werven", "bizman_headhunters_employee_type_title": "Werknemerstype", "bizman_headhunters_skill_level_title": "Vaardigheidsniveau", "bizman_headhunters_skill_level_value": "{minSkill}% - {maxSkill}%", "bizman_headhunters_salary_range_title": "<PERSON><PERSON> schaal", "bizman_headhunters_salary_range_value": "{minWagePerHour} - {maxWagePerHour} / uur", "bizman_headhunters_points_used_value": "Gebruikte punten: {pointsUsed}", "bizman_headhunters_headhunter_skill_value": "Vaardigheid: {skillPercentage}%", "bizman_headhunters_wage_and_skill_title": "Baanloon en vaardigheid", "bizman_headhunters_wage_and_skill_description": "<PERSON><PERSON><PERSON><PERSON> de <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bizman_headhunters_wage_per_hour_value": "tussen {minWagePerHour} en {maxWagePerHour} per uur", "bizman_headhunters_deal_breakers_title": "Dealbrekers", "bizman_headhunters_deal_breakers_description": "Gebruik wervingspunten (RP) om eisen te verwijderen waaraan u niet wilt voldoen. Headhunters krijgen één rekruteringspunt voor elk vaardigheidsniveau (%), dus zorg ervoor dat je ze goed traint.", "bizman_headhunters_deal_breakers_clear_all_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> alles", "bizman_headhunters_automatic_replacement_not_enough_skill_title": "<PERSON><PERSON> g<PERSON> v<PERSON>", "bizman_headhunters_automatic_replacement_not_enough_skill_description": "Deze headhunter heeft minimaal 75% vaardigheid nodig om automatische vervanging uit te voeren", "bizman_headhunters_assigned_hr_managers_title": "Toegewezen HR-managers", "bizman_headhunters_assigned_hr_managers_description": "Aan deze headhunter kunt u HR-managers toewijzen. Door dit te doen, vervangt de headhunter automatisch alle werknemers die ontslag nemen of met pensioen gaan en momenteel zijn toegewezen aan die HR-manager.", "bizman_headhunters_no_dealbreakers_for_selected_skill": "Er zijn geen dealbreakers beschikbaar voor het geselecteerde werknemerstype", "headhunter_deal_breaker_not_enough_rp": "Headhunter heeft niet genoeg wervingspunten meer", "headhunter_deal_breaker_group_label": "{dealBreakerType} ({rpCost} RP)", "headhuntersdealbreakertype_fulltime": "Voltijds", "headhuntersdealbreakertype_parttime": "Deelti<PERSON><PERSON>", "headhuntersdealbreakertype_noweekends": "Geen weekends", "headhuntersdealbreakertype_fivedaysaweek": "5 dagen per week", "headhuntersdealbreakertype_fourdaysaweek": "4 dagen per week", "headhuntersdealbreakertype_nomorningshifts": "<PERSON><PERSON>", "headhuntersdealbreakertype_noafternoonshifts": "<PERSON><PERSON>", "headhuntersdealbreakertype_noeveningshifts": "<PERSON><PERSON>", "headhuntersdealbreakertype_nonightshifts": "<PERSON>n nachtdiensten", "headhuntersdealbreakertype_nocleaningshifts": "<PERSON><PERSON> scho<PERSON>die<PERSON>", "headhuntersdealbreakertype_environmentdemand": "Milieuvraag", "headhuntersdealbreakertype_equipmentdemand": "Vraag naar apparatuur", "headhuntersdealbreakertype_benefitdemand": "Vraag naar voordelen", "headhunter_couldnt_find_employee_with_requirements": "<PERSON><PERSON> baa<PERSON>, het spijt me u te moeten mededelen dat ik niemand kon vinden die voldoet aan de vereisten die zijn gedefinieerd voor het laatste rekruteringsproces. We zullen de eisen moeten verlagen. <PERSON><PERSON> ben voor<PERSON> gest<PERSON> met zoeken naar kandidaten.", "jobdemand_noafternoons": "<PERSON><PERSON>", "jobdemand_noafternoons_description": "<PERSON>r zijn geen diensten toegewezen tussen 14.00 uur en 16.00 uur", "headhunter_select_employee": "Selecteer medewerker", "headhunter_selected_employee_already_selected": "De geselecteerde medewerker is al toegewezen aan een ander slot", "bizman_headhunters_automatically_replace_on_resign_label": "Vervang medewerkers automatisch wanneer ze ontslag nemen", "bizman_headhunters_automatically_replace_on_retire_label": "Vervang medewerkers automatisch als ze met pensioen gaan", "bizman_headhunters_employees_close_to_needing_replacement": "{amount}/{maxAmount} werknemers hebben bijna vervanging nodig", "bizman_headhunters_replacement_reason": "<PERSON><PERSON> van ve<PERSON>", "bizman_headhunters_replace_now": "Vervang nu", "bizman_headhunters_retiring_in_days": "Met pensioen: {daysBeforeRetiring} dagen", "bizman_headhunters_low_satisfaction": "Tevredenheid: {percentage}%", "bizman_headhunters_replacement_fee": "Vervangingskosten: {replacementFee}", "transactiontype_replacementfee_label": "Vervangingskosten", "transactiontype_replacementfee": "Vervanging van werknemer door {werknemer}", "savegame_compatibility_panel_headline": "Welkom bij Big Ambitions Early Access 0.{eaVersion}", "savegame_compatibility_panel_first_line": "Sinds de laatste keer dat je speelde, is er een nieuwe versie uitgebracht. Om je bestaande games te updaten dien je eerst de changelog hieronder te lezen en accepteren.", "savegame_compatibility_panel_accept_button": "Accepteer wijzigingen en upgrade savegames", "savegame_compatibility_panel_skip_button": "Upgrade overslaan", "savegame_compatibility_panel_last_line": "Wil je de nieuwe versie niet spelen? Onthoud dat je altijd terug kunt schakelen naar de oude versie via het tabblad 'Bè<PERSON>'s' via de eigenschappen van het spel op Steam.", "savegame_compatibility_panel_successful": "{saveGamesUpgraded} savegames zijn succesvol geüpgraded. We hopen dat je geniet van de update!", "change_character_hair_title": "<PERSON><PERSON> haar", "change_character_hair_save_button": "<PERSON><PERSON> haar", "change_character_hair_not_paid_for_warning": "Onbetaalde wijzigingen worden niet uitgevoerd", "businessrequirement_hairdresserchair": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "businessrequirement_headwasher": "Hoofdwasmachine", "businessrequirement_shelfwithhaircareproducts": "Haarverzorgingsproduct", "notifications_headhunter_employee_replaced": "Headhunter {employeeName} heeft {fromname} vervangen door {toname}", "main_menu_upgrade_savegames": "Upgrade alle savegames van de meest recent gespeelde versie", "main_menu_savegames_upgraded_notification": "Savegames geüpgraded!", "itempanel_click_sell_first_quest_not_completed": "Je kunt pas items verkopen als je de eerste missie hebt voltooid", "employees_skill_with_percentage": "{skillName} ({percentage}%)", "notification_no_items_available": "<PERSON>n {itemname} be<PERSON><PERSON><PERSON><PERSON>", "shop_sign_perderson_security_title": "Beveiliging", "shop_sign_perderson_security_secondtitle": "<PERSON>u het veilig", "shop_sign_perderson_hairdresser_title": "<PERSON><PERSON>", "shop_sign_perderson_hairdresser_secondtitle": "Zakelijke producten", "shop_sign_perderson_clothingstore_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shop_sign_perderson_clothingstore_secondtitle": "Voor de mode", "shop_sign_perderson_nightclub_title": "Nachtclub", "shop_sign_perderson_nightclub_secondtitle": "Nieuwe Producten!", "shop_sign_perderson_supermarket_title": "Supermarkt", "shop_sign_perderson_supermarket_secondtitle": "0% korting", "shop_sign_perderson_producers_title": "Producenten", "shop_sign_perderson_producers_secondtitle": "<PERSON><PERSON> je bedrijf", "shop_sign_perderson_shelves_title": "Planken", "shop_sign_perderson_shelves_secondtitle": "Voor je opslag", "tutorial_objective_hire_customer_service": "Werf een klantenservicemedewerker aan", "djbooth_notification_need_free_hands": "Lege handen nodig om de DJ Booth te gebruiken", "dialog_furniture_store_on_contract_settings_set_player_business_name": "Ik wil dat deze {amount} artikelen worden afgeleverd bij {businessName} ({address}) op {day} om {hour}:<br>{text}", "notification_delivery_contract_arrived_business_name": "Je verzending van {fromname} naar {businessName} ({toname}) is gearriveerd", "character_customization_notification_name_invalid_characters": "Kan geen ongeldige tekens gebruiken voor de karakternaam", "bizman_hrmanager_health_insurance_wait_for_hospital": "Wacht tot het ziekenhuis je een aanbod stuurt", "bizphone_econoview": "EconoView app", "help_bizphone_econoview_content": "De *EconoView App* toont je al jouw financiële gegevens.\n\n **Laatste transacties**\n\nIn deze sectie worden je recente aankopen weergeven. Klik op 'Volledige transactieweergave' om je meest recente transacties op te zoeken of te exporteren.\n\n**Leningen / Investeringen**\n\nIn deze sectie kunt je jou [Leningen en beleggingen](finance-banks) beheren.\n\n**Winst-en verliesrekening**\n\nIn dit gedeelte ziet je jouw inkomsten- en uitgavenoverzicht van de afgelopen dagen. Als je op een bedrijfsnaam klikt, zie je de specifieke inkomstenverdeling voor dat bedrijf.\n\nLet op: de resultatenrekening laat zien hoe winstgevend jouw bedrijf is, inclusief de kosten van de inventaris. Maar, omdat je eerder voor die inventaris hebt betaald, wordt dat bedrag niet daadwerkelijk opnieuw van je inkomen afgetrokken, maar wordt het alleen gebruikt om de algehele winstgevendheid weer te geven.", "building_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "building_types": "Maten / Soorten", "building_limit": "Klantcapaciteit:", "building_interiordesigner": "Interieurontwerper", "building_blueprints": "Blauwdrukken", "building_installationfirms": "Installatiebedrijven", "building_security": "Beveiliging / Diefstal", "help_businesstype_hairdresser_content": "**Kappers** zijn detailhand<PERSON>bedrijven.\n\nKlanten worden bediend door medewerkers.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [Kassa](meubel-kassa)\n* [Kapperplank](meubel-kappersplank)\n* [Kapperstoel](meubel-kappersstoel) of [Kapperstoel Modern](meubel-kappersstoelmodern)\n* [Hoofdwas voor kapper] (hoofdwas voor meubels-kapper)\n\nBedrijven van dit type verkopen voornamelijk:\n\n* [Haarchemische / kleurkosten] (kosten-haarchemische kosten)\n* [Haarknipkosten](kosten-haarknipkosten)\n* [Haarshampoo- en föhnkosten] (kosten-haarshampookosten)\n* [Haarstylingkosten](kosten-haarstylingkosten)\n\nEn kan daarnaast verkopen:\n\n* [Haarverzorgingsproduct](producten-haarverzorgingsproduct)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Haarstylist](vaardigheid-haarstylist)\n* [Klantenservice] (vaardigheid-klantenservice)\n* [Reiniging] (vaardigheidsreiniging)", "help_businesstype_nightclub_content": "**Nachtclubs** zijn detailhandelsbedrijven. \n\nKlanten worden bediend door werknemers.\n\nHet bedrijf heeft de volgende meubels nodig om te functioneren:\n\n* [Kassa](meubels-kassa)\n* [Garderobe (links)](meubels-garderobelinks) of [Garderobe (rechts)](meubels-garderoberechts)\n* [<PERSON>](meubels-djbooth)\n* [<PERSON><PERSON>rek<PERSON>](meubels-musicgroup)\n* Ten minste één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen voornamelijk:\n\n* [Bier](producten-bier)\n* [Garderobekosten](kosten-garderobekosten)\n* [Nachtclubkosten](kosten-nachtclubkosten)\n* [Margarita](producten-margarita)\n* [Martin<PERSON>](producten-martini)\n* [Whisky](producten-whisky)\n\nEn kan daarnaast verkopen:\n\n* [Frisdrank Kan](producten-sodacan)\n\nMedewerkers met de volgende vaardigheden kunnen worden toegewezen:\n\n* [DJ](vaardigheid-dj)\n* [Klantenservice](vaardigheid-klantenservice)\n* [Schoonmaken](vaardigheid-schoonmaken)", "itemname_covercharge": "Dekking", "help_itemname_covercharge_content": "**Dekkingstoeslag** is een vergoeding die wordt geïnd van klanten wanneer ze [Nachtclubs](businesstypes-nachtclub) betreden.\n\nDe vergoeding bestaat in twee varianten:\n* Dekkingskosten op weekdagen\n* Weekenddekkingskosten\n\nEr is niets vereist om deze vergoeding te innen, het gebeurt automatisch wanneer klanten het bedrijf binnen<PERSON>.", "employee_types": "Typen werknemers", "help_skillname_dj_content": "Werknemers met de **DJ**-vaardigheid worden ingezet voor nachtclubs.\n\nDJ's draaien muziek en zorgen ervoor dat klanten blijven dansen en meer geld uitgeven.\n\nHet vaardigheidsniveau (%) bepaalt hoe lang klanten in de nachtclub blijven.\n\nZe kunnen worden toegewezen aan:\n* [<PERSON>](furniture-djbooth)\n\nZe kunnen worden ingehuurd bij:\n* [Anderson Recruitment Corp.](adres: 16 5a)", "help_skillname_hairstylist_content": "Werknemers met de vaar<PERSON>gh<PERSON><PERSON> **Hair Stylist** worden gebruikt voor kappers.\n\nHair Stylists verdienen geld aan klanten door verschillende haarbehandelingen aan te bieden:\n* [Hair Chemical / Color Fee](fees-hairchemicalfee)\n* [Hair Cutting Fee](fees-haircuttingfee)\n* [Hair Shampooing & Blowout Fee](fees-hairshampooingfee)\n* [Hair Styling Fee](fees-hairstylingfee)\n\nHet vaardigheidsniveau (%) bepaalt hoe snel ze elke klant kunnen stylen.\n\nZe kunnen worden toegewezen aan:\n* [Hairdresser Chair](furniture-hairdresserchair)\n* [Hairdresser Chair (Modern)](furniture-hairdresserchairmodern)\n* [Hairdresser Headwash](furniture-hairdresserheadwash)\n\nZe kunnen worden ingehuurd bij:\n* [Anderson Recruitment Corp.](adres: 16 5a)", "help_skillname_securityguard_content": "Op elke winkellocatie worden medewerkers met de vaardigheid **Beveiliger** ingezet.\n\nBewakers beheren elk 1 Bewakingskluis en voorkomen diefstal.\n\nHet vaardigheidsniveau (%) bepaalt hoe effectief hun beveiliging is.\n\nZe kunnen worden toegewezen aan:\n* [Beveiligingskast] (furniture-securityguardlocker)\n\nZe zijn te huur bij:\n* [Anderson Recruitment Corp.](adres: 16 5a)", "employee_management": "<PERSON><PERSON><PERSON> van <PERSON>", "bizphone_myemployees": "Mijn Werknemers-app", "employee_stations": "Werkplekken", "itemname_computerworkstation": "Computerwerkplek", "help_itemname_securityguardlocker_content": "**Beveiliger Kluis** is een speciaal *werknemer station* voor medewerkers met de vaardigheid [Beveiliger](skill-securityguard).\n\nHet kan in elke detailhandelsbedrijf worden gebruikt om een hoog niveau van [Beveiliging](building-security) toe te voegen.\n\nHet meubel is te koop op de volgende locaties:\n* [Essentials Appliances](address:16 11s)", "itemname_musicgroup": "Muziek Opties", "itemname_diningtablegroup": "Eettafel opties", "itemname_computergroup": "Computeropties", "itemname_deskgroup": "Bureau-opties", "itemname_chairgroup": "<PERSON><PERSON><PERSON>", "itemname_sofagroup": "Bankopties", "help_itemname_classicloudspeakerbig_content": "**Klassieke luidspreker (groot)** kan in een bedrijf of thuis worden gebruikt om naar muziek te luisteren en aan bepaalde [klanteisen] (eisen van de klant in het gebouw) voor muziek te voldoen.\n\nHet meubilair is te koop op de volgende locaties:\n* [Essentiële apparaten](adres:16 11s)", "help_itemname_drinksshelf_content": "**Drankenplank** kan worden gebruikt voor de verkoop van:\n\n* [<PERSON><PERSON>](producten-bier)\n* [Margarita](producten-margarita)\n* [Martini](producten-martini)\n* [Whisky](producten-whisky)\n\n**Productcapaciteit:** 50\n**Klantcapaciteit:** 10\n\nHet meubilair kan worden gekocht bij de volgende locaties:\n* [Essentiële apparaten](adres: 16 11s)", "help_itemname_hairdressershelf_content": "**Kapsalonplank** kan worden gebruikt voor de verkoop van:\n\n* [Haarverzorgingsproduct](producten-haarverzorgingsproduct)\n\n**Productcapaciteit:** 40\n**Klantcapaciteit:** 30\n\nHet meubilair is te koop op de volgende locaties:\n* [AJ Pederson & Zoon](adres:13 5a)", "help_itemname_laptop_content": "**Laptop** kan worden gebruikt om een [computerwerkstation] (meubel-computerwerkstation) in te richten.\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON> Bohag](adres:50 4s)\n* [<PERSON><PERSON><PERSON>'s Ka<PERSON>orbenodigdheden](adres:39 4a)", "help_itemname_largemeetingtable_content": "**Grote vergadertafel** kan worden gebruikt om te voldoen aan de \"Grote vergadertafel\" [Vraag werknemersapparatuur](werknemers-eisen-apparatuur).\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON><PERSON>'s Kantoorbenodigdheden](adres:39 4a)", "help_itemname_loudspeaker3_content": "**P&S Peolab 90** kan in een bedrijf of thuis worden gebruikt om naar muziek te luisteren en aan bepaalde [klanteisen] (eisen van bouwklanten) voor muziek te voldoen.\n\nHet meubilair is te koop op de volgende locaties:\n* [Lux Concept](adres:68 5a)", "help_itemname_expensivetv_content": "**P&S Sympathy 88\"** kan worden gebruikt om tv te kijken en [geluk] te verkrijgen (algemeen geluk)\n\nHet meubilair is te koop op de volgende locaties:\n\n* [Lux Concept](adres:68 5a)", "help_itemname_palletshelf_content": "**<PERSON><PERSON><PERSON> Shelf** wordt gebruikt in [Warehouses](businesstypes-warehouse) om inventaris op te slaan. \n\n**Productcapaciteit:** 60 dozen\n\nHet meubilair kan worden gekocht op de volgende locaties:\n* [AJ Pederson & Son](adres:13 5a)\n* [Factory Supply Depot](adres:57 5a)", "help_itemname_cornersofa01_content": "**Preben Sofa** kan worden gebruikt om te voldoen aan de \"Elke bank\" [Employee Equipment Demand](employees-demands-equipment)\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON> Bohag](adres:50 4s)", "help_itemname_restaurantbooth_content": "**Restaurant Booth** kan worden gebruikt als zitplaatsen voor klanten terwijl ze eten om aan bepaalde [klanteisen] te voldoen (eisen van bouwklanten).\n\n4 Klanten kunnen per stand plaatsnemen, waarmee wordt voldaan aan de vraag van hun klanten naar zitplaatsen.\n\nHet meubilair is te koop op de volgende locaties:\n* [AJ Pederson & Zoon](adres:13 5a)", "help_itemname_armchair1_content": "**Sommerhaus Fauteuil** kan worden gebruikt voor het opzetten van een [Computerwerkstation](meubel-computerwerkstation) of [Eettafel](meubel-eettafelgroep).\n\nHet meubilair is te koop op de volgende locaties:\n* [Ika Bohag](adres:50 4s)", "help_itemname_securitycamera_content": "**Beveiligingscamera** biedt een gemiddeld niveau van [beveiliging](gebouwbeveiliging) voor uw gebouw wanneer deze aan de muur is bevestigd.\n\nHet meubilair kan worden gekocht op de volgende locaties:\n* [Essentials Appliances](adres: 16 11s)", "help_itemname_bed1_content": "**Standaardbed** kan worden gebruikt om te slapen en [Energie] op te doen (general-energy)\n\nHet meubilair is te koop op de volgende locatie:\n* [<PERSON><PERSON>](address:50 4s)", "help_itemname_standardfridge_content": "**Standaardkoelkast** kan voedsel opslaan voor persoonlijk gebruik. Het kan ook worden gebruikt om te voldoen aan de \"Standaardkoelkast\" [Employee Equipment Demand] (werknemers-eisen-apparatuur).\n\nHet meubilair is te koop op de volgende locaties:\n* [Vierkante Apparaten](adres:16 4a)\n* [Ika Bohag](adres:50 4s)", "help_itemname_scorpiogamingsetup_content": "**Scorpio Gaming Setup** kan worden gebruikt om computerspellen te spelen en [Geluk](general-happiness) te verhogen.\n\nHet meubel is te koop op de volgende locaties:\n* [Lux Concept](address:68 5a)", "help_itemname_trashbin_content": "**Vuilnisbakken** kunnen door klanten worden gebruikt om na het eten afval weg te gooien.\n\nHet meubilair kan op de volgende locatie gekocht worden:\n* [AJ Pederson & Son](address:13 5a)", "help_importers": "Groothandel / import", "wholesalers_overview": "Groothandel overzicht", "wholesalers_metro": "Metro Wholesale", "wholesalers_nydistro": "NY Distro Inc", "wholesalers_totalproduce": "Total Produce", "wholesalers_shortages": "Tekorten en nabestellingen", "help_wholesalers_shortages_content": "**Tekorten en nabestellingen** kunnen van tijd tot tijd natuurlijk gebeuren of kunnen kunstmatig worden gecreëerd door [Rivalen] (rivalenoverzicht) die met u concurreren.\n\nAls een artikel een *Tekort* heeft, wordt de prijs voor dat artikel verhoogd, maar kun je het nog steeds kopen.\n\nAls een item in een *Backorder* staat, kun je dat item pas kopen nadat de backorder is afgelopen.\n\nIn beide gevallen zal de vraag van de klant voor de duur toenemen, omdat het artikel slechts beperkt verkrijgbaar is. Als u voldoende spullen in voorraad kunt houden om een tekort te doorstaan, kunt u een mooie inkomensverhoging realiseren!", "help_wholesalers_metro_content": "**Metro Wholesale** is een groothandelslocatie.\n\nZe verkopen de volgende artikelen:\n* [Burger](producten-burger)\n* [Goedkoop cadeau](producten-goedkoopcadeau)\n* [Klassieke goedkope herenkleding] (producten-klassieke goedkope herenkleding)\n* [Vers voedsel](producten-vers voedsel)\n* [Bevroren voedsel](producten-diepvriesvoedsel)\n* [<PERSON><PERSON><PERSON> zak](producten-paperbag)\n* [Salade](producten-salade)\n* [Frisdrankblikje](producten-frisdrank)\n\nZe bevinden zich hier:\n* [Metrogroothandel](adres:2 5a)", "help_wholesalers_totalproduce_content": "**Total Produce Trading** is een groothandelslocatie.\n\nZe verkopen de volgende artikelen:\n* [<PERSON><PERSON>](products-apple)\n* [Banaan](products-banana)\n* [<PERSON>ort<PERSON>](products-carrot)\n* [Goedkope Bloem](products-cheapflower)\n* [<PERSON><PERSON>](products-expensiveflower)\n* [Vers Voedsel](products-freshfood)\n* [Diepvriesvoedsel](products-frozenfood)\n* [Sla](products-lettuce)\n* [Peer](products-pear)\n* [Salade](products-salad)\n* [Tom<PERSON><PERSON>](products-tomato)\n\nZe bevinden zich hier:\n* [Total Produce Trading](address:6 6a)", "vehicletypename_alternatetraveloptions": "Alternatieve reisopties", "help_vehicletypename_alternatetraveloptions_content": "Als je geen auto wil kopen en je niet wil bezighouden met parkeren en verkeer, zijn er een paar opties:\n\n**Taxi's**\n\nTaxi's rijden willekeurig door de stad, klaar om je tegen een vergoeding rond te rijden.\n\nAls er één bij je in de buurt is, klik je erop en selecteer je het gebouw waar je naartoe wil reizen.\n\n**Metrostations**\n\nMetrostations bevinden zich op verschillende locaties in de stad.\n\nAls je in de buurt bent van een metrostation, klik je erop en klik je vervolgens op een ander metrostation om er tegen betaling naartoe te reizen.\n\n**Elektrische Scooters**\n\nElektrische Scooters zijn op verschillende locaties in de stad aan het opladen.\n\nTegen betaling kan je deze scooters huren.\n\n*Opmerking:* Je kan een papieren zak vasthouden, maar je kan geen dozen dragen of steekwagens of diepladers gebruiken terwijl je de elektrische scooter gebruikt.", "transactiontype_electricscooterfee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Elek<PERSON>", "interior_installation_firm_no_designs_for_selected_address": "Er zijn geen ontwerpen beschikbaar voor het geselecteerde adres.", "transactiontype_electricscooterfee_label": "Elektrische Scooter", "interior_installation_firm_no_addresses_available": "U heeft geen adressen beschikbaar waar {businessName} service aan kan leveren.", "business_description_installation_firm_residential": "Het is jouw thuis. En jij verdient perfectie", "business_description_installation_firm_retail_warehouse": "Van retail tot magazijnen, ontwerpen op maat voor u", "business_description_installation_firm_office": "Kantorenontwerpen geschikt voor elk koninkrijk", "jobdemand_hasphone_description": "Werknemer eist dat er een telefoon op zijn bureau staat", "blueprintdata_businesstypename": "Bedrijfs Type", "bizman_security_business_closed": "Gesloten/beveiligd", "jobdemand_hasofficephone_description": "Werknemer eist dat er een kantoortelefoon op zijn bureau staat", "sortby_popularity": "Populariteit", "sortby_price": "<PERSON><PERSON><PERSON><PERSON>", "sortby_uploaddate": "Upload datum", "common_page": "Pagina\n{pageNo}", "interior_design_purple": "paars", "blueprint_notification_cannot_contain_special_gifts": "U kunt geen blauwdruk opslaan die speciale geschenken bevat", "loading_screen_christmasstreetdecorations": "<PERSON><PERSON><PERSON><PERSON>", "itempanelui_hud_confirm_discard_special_gift": "Weet je het zeker? Dit is een speciaal item en je zal het niet kunnen herstellen", "itemname_specialgift2023": "Speciaal cadeau 2023", "bizman_presentation_hud_confirm_special_gift_inside": "Weet je het zeker? Hierin zit een speciaal item dat je na deze actie niet meer terug krijgt.", "itemname_christmastree": "Voorverlichte kunstkerstboom", "itemname_candycane": "Gigantisch decoratief snoepgoed", "itemname_christmasgift01": "<PERSON>en<PERSON><PERSON>g k<PERSON>cadeau", "itemname_christmasstockings": "Kerstkousen", "itemname_christmaslights01": "<PERSON><PERSON> l<PERSON>", "itemname_christmasgift02": "<PERSON><PERSON>", "itemname_christmasgift03": "<PERSON><PERSON>", "itemname_christmasgift04": "Luxe kerstcadeau", "itemname_christmasgifts01": "Kleine kerstcadeaubundel", "itemname_christmasgifts02": "Middelgrote kerstcadeaubundel", "itemname_christmasgifts03": "<PERSON><PERSON> kerstcadeaub<PERSON>el", "itemname_christmaslights02": "<PERSON>rstster lichtsnoer", "itemname_christmaslights03": "Kerst Maretak lichtsnoer", "itemname_christmascookies": "Koekjes & Melk", "notification_specialgift_need_empty_hands": "Je moet lege handen hebben om het cadeau op te pakken", "itemname_jackolanternsmall": "Lichtgevende Jack-O-Lantern (klein)", "itemname_jackolanternmedium": "G<PERSON>eiende Jack-O-Lantern medium", "itemname_jackolanternbig": "Gloeiende Jack-O-Lantern groot", "streetname_twelfthstreet": "12th Street", "streetname_thirteenthstreet": "13th Street", "streetname_fourteenthstreet": "14th Street", "businesstype_gasstation": "Tankstation & Voertuigreparatie", "businesstype_truckgarage": "Vrachtwagengarage", "businesstype_irs": "<PERSON>ne belast<PERSON> (IRS)", "businesstype_gym": "Sportschool", "businesstype_casino": "Casino", "notification_cannot_open_interior_designer_in_work_station": "U kunt de binnenhuisarchitect niet openen tijdens interactie met een werk<PERSON>k", "cash_register_notifications_no_paperbags_needed_for_businesstype": "De<PERSON> winkel heeft geen papieren tassen nodig voor de kassa's", "contacts_candidates_button": "Kandidaten", "playerhud_fps_counter": "{fps} FPS", "itemname_watercooler": "Waterkoeler", "jobdemand_watercooler": "Waterkoeler", "jobdemand_watercooler_description": "Werknemer eist dat er een waterko<PERSON>r in het gebouw aanwezig is", "notification_fell_through_map": "Dat mocht niet gebeuren! Help ons het spel te verbeteren door dit probleem te melden!", "itemname_tobaccoshelf2": "Tabaksplank (Medium)", "itemname_productdisplaytable": "Productweergavetabel", "itemname_smartphone1": "<PERSON><PERSON>-telefoon", "itemname_smartwatch1": "ZanaMan-smartwatch", "itemname_smartwatch2": "<PERSON><PERSON>-smartwatch", "itemname_checkoutcounterleft": "Kassa links", "itemname_officedesk2right": "Executive Bureau Rechts", "itemname_coatcheckright": "Vachtcheck rechts", "itemname_cornersofa02left": "Preben Bank met Chaiselong Links", "shop_sign_electronic_store_title": "Verkoop de nieuwste elektronica", "shop_sign_electronic_store_secondtitle": "<PERSON><PERSON> tafel", "jobdemand_seatedatofficechair": "Bureaustoel", "jobdemand_seatedatofficechair_description": "Werknemer eist plaats te nemen op een bureaustoel", "jobdemand_seatedatofficedesk1": "Standaard bureau", "jobdemand_seatedatofficedesk1_description": "Werknemer eist plaats te nemen aan een Standaard Bureau", "action_consumefood": "<PERSON><PERSON>", "action_consumedrink": "<PERSON><PERSON><PERSON>", "itemname_earbuds01": "Noize Boss-<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_headphones01": "Rhythm By <PERSON><PERSON>", "npc_expression_cannot_reach_item": "<PERSON>k kan <b>{itemname}</b> niet bereiken", "loading_screen_lowereastsidetemporalborder": "<PERSON><PERSON><PERSON><PERSON><PERSON> aan het bouwen", "itemname_kabob": "<PERSON><PERSON><PERSON><PERSON>", "tooltip_right_click_to_remove": "<PERSON><PERSON> met de rechtermuisknop om te verwijderen", "dialog_negotiation_accepted_strange_tactic": "<PERSON><PERSON><PERSON><PERSON> tactiek, maar we gaan akkoord.", "dialog_negotiation_accepted_glad_you_accepted": "We zijn blij dat je ons aanbod hebt geaccepteerd.", "dialog_negotiation_declined_take_it_seriously": "Neem deze onderhandelingen alstublieft serieus. Laten we dit nog eens proberen.", "dialog_negotiation_accepted_accept_player_offer": "<PERSON>ij <PERSON>eren uw tegenbod.", "dialog_negotiation_counter_offer": "In plaats daarvan willen we graag {counterOffer} voorstellen.", "dialog_negotiation_final_counter_offer": "We kunnen niet lager gaan dan {counterOffer}. Di<PERSON> is ons laatste bod.", "dialog_negotiation_negotiate_counter_offer": "Wat stelt u voor?", "dialog_negotiation_player_declined_counter_offer": "Het spijt me, maar ik ben niet langer geïnteresseerd.", "dialog_negotiation_accepted_close_enough": "Dit lijkt een eerlijke deal, we accepteren het.", "help_businesstype_electronicsstore_content": "**Elektronicawinkels** zijn detailhandelsbedrijven.\n\nKlanten zijn egoïstisch.\n\nHet bedrijf heeft het volgende meubilair nodig om te kunnen functioneren:\n\n* [Stapel winkelmandjes] (meubelstapel winkelmandjes)\n* [Verkooppunt](meubelitemgroepverkooppunt)\n* Minstens één product om te verkopen (zie hieronder)\n\nBedrijven van dit type verkopen voornamelijk:\n\n* [Arty Fish Phone](producten-smartphone1)\n* [Arty Fish Smartwatch] (producten-smartwatch2)\n* [Noize Boss-oordopjes] (products-earbuds01)\n* [Rhythm By Tre](producten-hoofdtelefoon01)\n* [ZanaMan Telefoon](producten-smartphone2)\n* [ZanaMan Smartwatch](producten-smartwatch1)\n\nEn kan daarnaast verkopen:\n\n* [Frisdrankblikje](producten-frisdrank)\n\nMedewerkers met de volgende vaardigheden kunnen worden ingezet:\n\n* [Klantenservice] (vaardigheid-klantenservice)\n* [Reiniging] (vaardigheidsreiniging)", "help_itemname_smartphone1_content": "**Arty Fish Phone** is een type product dat voornamelijk wordt verkocht via [Electronics Stores](businesstypes-electronicsstore).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Displaytabel (Product)](meubel-productdisplaytafel)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [JetCargo Imports] (adres: 1 pier)", "help_itemname_smartphone2_content": "**ZanaMan Phone** is een type product dat voornamelijk wordt verkocht via [Elektronicawinkels](businesstypes-elektronicawinkel).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Productweergavetabel] (meubel-productweergavetafel)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [JetCargo Imports] (adres: 1 pier)", "help_itemname_earbuds01_content": "**Noize Boss Earbuds** is een producttype dat voornamelijk wordt verkocht in [Elektronicawinkels](businesstypes-electronicsstore).\n\nDaarnaast kunnen ze ook worden verkocht in [Boekhandel](businesstypes-bookstore).\n\nHet product kan in de volgende meubels worden geplaatst:\n* [Display Table (Product)](furniture-productdisplaytable)\n\nHet product kan worden gebruikt als [Karakteraccessoire](common_accessories) om naar muziek te luisteren.\nHet accessoire kan worden opgeborgen in een [Earbuds Stand](furniture-earbudsstand)\n\nHet product kan worden gekocht op de volgende locaties:\n* [NY Distro Inc](address:37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [JetCargo Imports](address: 1 pier)\n\nHet product kan worden geproduceerd met behulp van de volgende recepten:\n* [Noize Boss Earbuds Recept](recipes-earbuds01recipe)", "help_itemname_kabob_content": "**Kip Kabob Spies** is een type product dat voornamelijk wordt verkocht door [Fastfoodrestaurants] (bedrijfstypes-fastfoodrestaurant).\n\nHet product kan in de volgende meubelen geplaatst worden:\n* [Industriële grill](meubel-industriële grill)\n\nHet product kan op de volgende locaties worden gekocht:\n* [NY Distro Inc] (adres: 37 1s)\n\nHet product kan worden geïmporteerd vanaf de volgende locaties:\n* [SeaSide Internationals](adres: 2 pier)", "itemname_coatcheckleft": "Jasruit links", "itemname_cornersofa02right": "Preben Bank met <PERSON><PERSON><PERSON>s", "help_itemname_cornersofa02left_content": "**Preben bank met chaiselong links** kan worden gebruikt om te voldoen aan de \"Elke bank\" [Employee Equipment Demand](employees-demands-equipment)\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON> Bo<PERSON>g](adres:50 4s)\n* [Lux Concept](adres:68 5a)", "itemname_officedesk2left": "Directiebureau links", "help_itemname_officedesk2right_content": "**Executive Office Desk Right** kan worden gebruikt voor het inrichten van een [Computerwerkstation](meubel-computerwerkstation).\n\nHet meubilair is te koop op de volgende locaties:\n* [<PERSON><PERSON>](adres:50 4s)\n* [<PERSON><PERSON><PERSON> <PERSON>'s Kantoorbenodigdheden](adres:39 4a)", "itemname_checkoutcounterright": "<PERSON><PERSON> rechts", "help_itemname_checkoutcounterleft_content": "**<PERSON><PERSON> links** is een speciaal *werknemersstation* waarvoor werknemers met de vaardigheid [Klantenservice](vaardigheid-klantenservice) nodig zijn.\n\nMet Interior Designer kunt u de klantenwachtrij aanpassen.\n\nHet meubilair heeft de volgende producten nodig om te kunnen functioneren:\n* [<PERSON><PERSON><PERSON> zak](producten-paperbag)\n\n**Productcapaciteit:** 1000\n**Klantcapaciteit:** 30\n\nHet meubilair is te koop op de volgende locaties:\n* [AJ Pederson & Zoon](adres:13 5a)", "help_itemname_checkoutcounterright_content": "**<PERSON><PERSON> rechts** is een speciaal *werknemersstation* waarvoor medewerkers met de vaardigheid [Klantenservice](vaardigheid-klantenservice) nodig zijn.\n\nMet Interior Designer kunt u de klantenwachtrij aanpassen.\n\nHet meubilair heeft de volgende producten nodig om te kunnen functioneren:\n* [<PERSON><PERSON><PERSON> zak](producten-paperbag)\n\n**Productcapaciteit:** 1000\n**Klantcapaciteit:** 30\n\nHet meubilair is te koop op de volgende locaties:\n* [AJ Pederson & Zoon](adres:13 5a)", "happinessmodifiertype_gambling": "Gokken", "happinessmodifiertype_playingvideogames": "Videogames spelen", "happinessmodifiertype_watchingtv": "Tv kijken", "happinessmodifiertype_djing": "DJ'en", "happinessmodifiertype_inanightclub": "In een nachtclub", "happinessmodifiertype_walkinginthepark": "Wandelen in het park", "happinessmodifiertype_exercising": "Trainen", "notification_cant_interact_with_item_in_hand": "Je kunt geen <PERSON>ie hebben met {fromname} terwijl je {toname} in handen hebt", "hud_confirm_delivery_contract": "Weet je zeker dat je dit leveringscontract wil annuleren?", "colors_darkblue": "Donkerblauw", "hud_confirm_return_items_to_shelf": "Weet je zeker dat je deze artikelen terug wil plaatsen in de schappen?", "hud_confirm_autofill_schedule": "Weet je zeker dat je de planning automatisch wil invullen? Hierdoor wordt het bestaande schema overschreven.", "dialog_more_help_new_order": "Nieuwe <PERSON>", "credits_projectmanager": "Projectleider", "notification_cant_use_while_using_item": "Je kunt een {fromname} niet geb<PERSON>iken terwijl je een {toname} bij je hebt", "priority_low": "Laag", "priority_medium": "Medium", "priority_high": "<PERSON><PERSON>", "itemname_seasonalneon": "Seizoensgebonden neon", "itemname_seasonaldecoration": "Seizoensgebonden decoratie", "interiordesigner_cant_use_while_driving": "Je kunt de Interieur Ontwerper niet gebruiken terwijl je een voertuig bestuurt.", "interiordesigner_cant_use_while_interacting_with_workstation": "Je kunt de Interieur Ontwerper niet gebruiken tijdens interactie met een werkstation.", "loading_screen_lowermanhattan": "Lower Manhattan", "notification_cant_open_interior_designer_in_vehicle": "Je kunt de interieurontwerper niet openen terwijl je in een voertuig zit.", "notification_cant_open_interior_designer_while_on_workstation": "U kunt de binnenhuisarchitect niet openen tijdens interactie met een werk<PERSON>k", "contactcategoryname_general": "<PERSON><PERSON><PERSON><PERSON>", "contactcategoryname_business": "<PERSON><PERSON><PERSON><PERSON>", "contactcategoryname_finance": "Financiën", "contactcategoryname_furnitureandequipment": "Meubilair / uitrusting", "contactcategoryname_importsandgoods": "Invoer / Go<PERSON>ren", "contactcategoryname_employees": "Medewerkers", "contactcategoryname_rivals": "Rivalen", "itemname_casinoslotmachine": "Gokmachine", "itemname_casinoslotmachinechair": "Slot<PERSON>chine stoel", "itemname_casinoblackjacktable": "Blackjack", "itemname_casinoroulettetable": "Roulette", "itemname_specialemployeedesk": "Bureau", "itemname_healthinsurancemanagerdesk": "Manager <PERSON><PERSON> zorgverzekeringen", "itemname_mrternitydesk": "Meneer Ternity Bureau", "headhunter_amount_of_candidates": "Aantal kandidaten", "headhunter_recruit_continuously": "Con<PERSON>u rek<PERSON><PERSON>en", "itemname_woodenSaladBar": "Houten Saladebar", "itemname_handtruckspawner": "Steekwagen Spawner", "itemname_flatbedspawner": "Vlakke bed spawner", "notification_slot_machine_is_occupied": "Deze gokautomaat is bezet", "notification_blackjack_table_is_full": "Deze blackjacktafel is volzet", "notification_roulette_table_is_full": "Deze roulettetafel is volzet", "myemployees_configure_pay_bonus": "Betaalbonus ({bonusAmount})", "myemployees_configure_pay_bonus_tooltip_title": "Bonus", "myemployees_configure_pay_bonus_tooltip_description": "Door medewerkers een bonus te betalen, stijgt hun tevredenheid direct naar 100%. Bonussen hebben een afkoelingsperiode van 30 dagen.", "transactiontype_employeebonus": "Werknemersbonus voor {werknemer}", "transactiontype_employeebonus_label": "Werknemersbonus", "myemployees_mass_action_pay_bonuses_confirm": "Betaal bonussen ({bonusesSum})", "myemployees_mass_action_paybonuses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myemployees_pay_bonus_cooldown": "Afkoeling: {dagen} dagen", "dialog_bank_investment_risk_info_tooltip": "Investering<PERSON> zullen in de loop van de tijd will<PERSON><PERSON>ig binnen dit bereik fluctueren", "colors_lightred": "Lichtrood", "colors_midnight": "Middernacht", "colors_gold": "<PERSON>ud", "streetname_eleventhstreet": "11e straat", "myemployees_skills_tooltip": "Het vaardigheidsniveau van de werknemer geeft aan hoe efficiënt hij/zij is in zijn/haar werk. Het trainen van een werknemer zal zijn/haar vaardigheden en efficiëntie verbeteren, maar zal ook zijn/haar uurloon verhogen.", "itemname_novel": "Roman", "itemname_youngnovel": "<PERSON><PERSON><PERSON><PERSON>", "itemname_motivationalbook": "Motivationeel boek", "itemname_technicalmanual": "Technische handleiding", "itemname_picturebook": "Prentenboek", "itemname_limitededitionbook": "Boek in beperkte oplage", "businesstype_bookstore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_computermonitor": "Computermonitor", "click_to_read": "Klik om te lezen", "entertain_panel_read_headline": "<PERSON><PERSON>", "entertainui_slider_label_read": "Lees {entertainingHours} uur en {entertainingMinutes} minuten lang een boek.\n\nGeeft een geluksboost van {boostPercentage}% gedurende {boostHours} uur.", "entertainui_stop_read": "Stop met lezen", "happinessmodifiertype_read": "<PERSON><PERSON> een boek", "local_songs_are_loading": "Lokale liedjes worden geladen", "smartphone_rivals": "Rivalen", "common_weekly_income": "Wekelijks inkomen", "rivals_jessica_johnson_entrance": "<PERSON><PERSON>, jongen. <PERSON><PERSON> Fred je altij<PERSON>, toch, jongen? Dat is schattig. <PERSON><PERSON> dan ook, ve<PERSON> p<PERSON><PERSON> met je bed<PERSON><PERSON><PERSON><PERSON>, maar geef mij geen enkele reden om me zorgen over je te maken, oké? Houd het klein, jongen.", "rivals_jessica_johnson_hirebestemployees": "Gisteren hebben we wat onderzoek gedaan. Eigenlijk. We hebben online gekeken wie er voor u werkt. We hebben een aantal zeer bekwame mensen gevonden. Misschien wordt het tijd dat ze loonsverhoging krijgen en misschien een nieuwe werkgever.", "rivals_jessica_johnson_lowdemand": "<PERSON><PERSON> had net een geweldig idee dat ik met jullie wilde delen. Wat moet ik doen als ik de gehele inventaris van mijn bedrijf uitbreid met precies datgene wat u verkoopt? Stel je voor wat er met de vraag zal gebeuren. Stelt u zich eens voor hoe moeilijk het voor u zou zijn om uw inkomsten op peil te houden. Dat is een heel goed idee, denk ik.", "rivals_poach_employee_rival_surrender": "Ik heb gehoord dat {rival<PERSON>ame} failliet is, dus ik heb besloten om voor je te blijven werken. Die salarisverhoging zou ik overigens niet erg vinden.", "employee_contact_complaint_low_skill": "{rivalName} heeft mij gratis jobtraining aangeboden als ik voor hen ga werken. Als u bereid bent mij binnen 24 uur naar een training te sturen, hoeft er verder niets te veranderen. Anders verlaat ik {businessName}.", "help_itemname_computermonitor_content": "**Computermonitor** kan worden toegevoegd aan een [Computerwerkstation](meubilair-computerwerkstation) om te voldoen aan de \"Computermonitor\" [Employee Equipment Demand](employees-demands-equipment).\n\nHet meubilair kan worden gekocht op de volgende locaties: \n* [Mr. <PERSON>'s Office Supplies](adres:39 4a)\n* [Ika Bohag](adres:50 4s)", "employee_skill": "Werknemersvaardigheden / Training", "help_employee_skill_content": "Het *vaardigheidsniveau* van uw werknemer heeft invloed op zijn/haar werkprestaties en klantenservice.\n\n**Training**\n\nDe vaardigheden van werknemers kunnen worden verbeterd door training.\n\n* **Handmatige training** - train de werknemer via de app [MyEmployees](skill-myemployees) op uw [BizPhone](general-bizphone). Dit verhoogt de vaardigheden het snelst, maar vereist dat de werknemer tijdens de training wordt vrijgesteld van zijn/haar positie. De training eindigt om 17:00 (17:00 uur) de dag nadat hij/zij naar de training is gestuurd.\n* **Automatische HR Manager Training** - als uw werknemer is toegewezen aan een [HR Manager](skill-hrmanager), zal hij/zij zijn/haar vaardigheden elke dag automatisch langzaam verbeteren. De werknemer blijft tijdens deze training werken.\n* **On-the-job Training** - terwijl uw werknemer werkt, zal hij/zij heel langzaam vaardigheden verwerven door natuurlijke on-the-job training. Dit is de langzaamste manier om vaardigheden te verbeteren.\n\n**Lonen**\n\nNaarmate uw werknemers in vaardigheden toenemen, zal hun loon ook stijgen.", "headhunter_started_searching_for_a_replacement": "Headhunter ging op zoek naar een vervanger.", "headhunter_insufficient_funds": "Headhunter kon vanwege onvoldoende middelen geen vervanger zoeken.", "headhunter_expected_completion_days_range": "Headhunter ging op zoek naar een vervanger. Verwachte doorlooptijd: {startingDay}-{days} dagen", "headhunter_expected_completion_1_day": "Headhunter ging op zoek naar een vervanger. Verwachte doorlooptijd: 1 dag", "contacts_rival_button": "Bekijk rivaal", "business_description_appliance_store_3": "Als je het nodig hebt, wij hebben het.", "business_description_wholesalestore_4": "Groothandel op tijd. Dat is de Hudson-belofte.", "business_description_furniture_store2": "Hogere klasse concepten verdienen luxe kwaliteit", "transactiontype_entrancefee_label": "Toegangsprij<PERSON>", "bizman_marketing_no_contacts": "U kunt een bezoek brengen aan Marketing Offices om contact op te nemen.", "bankdialog_notification_select_investment_fund": "Selecteer een beleggingsfonds", "bizman_deliveries_no_contracts": "U heeft geen leveringsovereenkomsten voor dit bedrijf. Neem contact op met een manager van een groothandel om er een op te zetten.", "itemname_printer": "Printer", "itemname_walllight01": "Wandlamp (koepel)", "itemname_walllight02": "Wandlamp (langwerpig)", "business_description_truck_garage": "Voor het repareren van vrachtwagens, kunt u bij ons terecht!", "inventory_pricing_sold_breakdown": "{amount} verkocht voor {price}", "inventory_pricing_sold_breakdown_in_service": "{amount} gebru<PERSON>t in dienst", "rivals_leaderboard_defeated": "Verslagen", "itemname_displaytable": "<PERSON><PERSON> weerge<PERSON>", "business_description_furniture_store3": "Heeft uw fabriek een upgrade nodig? Wij hebben alles wat u nodig heeft!", "experimental_build_warning_title": "Experimentele constructie", "experimental_build_warning_description": "Savegames en voortgang zijn niet compatibel met Live Build!", "itemname_metalshelf": "<PERSON><PERSON> schap", "itemname_decorativecarwheels": "Decoratieve wielen", "itemname_stackofjerrycans01": "Stapel Jerrycans 1", "itemname_stackofjerrycans02": "Stapel Jerrycans 2", "itemname_smalldecorativebox": "Kleine decoratieve doos", "itemname_mediumdecorativebox": "Middelgrote decoratieve doos", "itemname_largedecorativebox": "Grote decoratieve doos", "subwaystation_hellskitcheneaststation": "Hell's Kitchen - Ooststation", "subwaystation_hellskitchenweststation": "Hell's Kitchen - Weststation", "subwaystation_murrayhilleaststation": "Murray Hill - Ooststation", "subwaystation_murrayhillsouthstation": "Murray Hill - Zuidstation", "subwaystation_murrayhillnorthstation": "Murray Hill - Noordstation", "subwaystation_midtownnorthweststation": "Midtown - Noordweststation", "subwaystation_lowermanhattancenterstation": "Lower Manhattan - Centrumstation", "credits_voiceactors": "Stemacteurs", "main_menu_custom_game_rivals_difficulty_multiplier": "Vermeerdering voor rivaliserende aanvallen", "main_menu_custom_game_rivals_difficulty_multiplier_tooltip": "Dit heeft invloed op de ernst van rivaliserende aanvallen. <PERSON><PERSON> deze in op 0 om alle rivaliserende aanvallen uit te schakelen", "jobdemand_hascomputermonitor_description": "Werknemer eist een extra computermonitor op zijn bureau", "topbar_money_tooltip": "Totaal: {totalValue}\nGisteren: {yesterdayValue}", "bizman_presentation_show_employees": "Laat medewerkers zien", "bizman_presentation_rival_employees_list": "Medewerkers van {businessName}", "help_counterattack_rivals_content": "Het is tijd om terug te vechten tegen de rivalen!\n\n**Tegenaanvallen**\n* Verlaagde prijzen - als je de verkoopprijs verlaagt van items die je rivalen ook verkopen, worden alle rivalen in die buurt gedwongen hun prijzen ook te verlagen en verliezen ze een deel van hun wekelijkse inkomsten. En als je dit een paar weken volhoudt, zul je ook de waarde van hun winkels zien dalen!\n\n* Verlaagde vraag - je kunt nieuwe bedrijven openen die je rivalen ook runnen om de vraag naar de producten die ze verkopen te verlagen, wat ook hun wekelijkse inkomsten verlaagt.\n\n* Werknemers afpakken - je vindt de werknemers van elke concurrent op de BizMan-pagina van hun bedrijf. Hier kun je proberen werknemers van een concurrent af te pakken door een beter loon voor ze te onderhandelen. Als je ze succesvol afpakt, wordt de concurrent gedwongen hen te vervangen door een inferieure werknemer, waardoor de wekelijkse inkomsten van hun winkel dalen.", "itemname_chickenbreast": "<PERSON><PERSON><PERSON><PERSON>", "common_remove": "Verwijderen", "bizman_logisticsmanager_lower_skill_confirm": "Deze logistiek manager he<PERSON>t niet genoeg vaardigheden om zoveel leveringen te beheren. De extra leveringslocaties zullen geen leveringen ontvangen.", "bizman_factory_production_flow_title": "Productiestroom", "skillname_factoryworker": "Fabrieksarbeider", "click_to_rest": "Klik om te rusten", "employee_name_with_skill": "{name} (Vaardigheid: {skillValue}%)", "itemoverlay_click_to_expand": "Klik om uit te vouwen", "contacts_wage_value": "{wage}/u", "interiordesigner_blueprint_tool": "Blauwdruktool", "help_skillname_factoryworker_content": "Werknemers met de vaardigheid **Factory Worker** worden gebruikt voor [Factories](businesstypes-factory).\n\nFabrieksarbeiders worden elk toegewezen aan een fabrieksmachine in de fabriek en bedienen die machine.\n\nZe kunnen worden toegewezen aan:\n* [Input Station](furniture-inputstation)\n* [Output Station](furniture-outputstation)\n\nZe kunnen worden ingehuurd bij:\n* [Anderson Recruitment Corp.](adres: 16 5a)", "recipes_kabobrecipe": "Recept voor kipspiesjes", "help_recipes_icecreamrecipe_content": "**Ijsrecept** kan worden geproduceerd in een [Fabriek](businesstypes-factory).\n\n**Vereiste ruwe ingrediënten:**\n\n* [Room](producten-room)\n* [Melk](producten-melk)\n* [Suiker](producten-suiker)\n\n**Vereiste fabrieksmachines:**\n\n* [Batchpasteur](meubel-batchpasteur)\n* [Harderingstunnel](meubel-hardingstunnel)\n* [Invoerstation](meubel-invoerstation)\n* [Uitvoerstation](meubel-uitvoerstation)\n\n**Recept**\n\n* Maak [IJsmengsel](producten-ijsmengsel)\n\n* Maak [IJs](producten-ijs) in de [Harderingstunnel](meubel-hardingstunnel)", "input_key_openhandtool": "Handgereedschap", "input_key_openfloortool": "Vloergereedschap", "input_key_openwalltool": "<PERSON><PERSON>", "input_key_openqueuetool": "Wachtrij-tool", "input_key_opensecuritytool": "Beveiligingstool", "input_key_redo": "<PERSON><PERSON><PERSON>", "input_key_openselltool": "Verkooptool", "itemname_cigarette": "<PERSON><PERSON>", "click_to_add_inventory": "<PERSON>lik om toe te voegen aan de inventaris", "click_to_manage": "Klik om te beheren", "click_to_grab": "Klik om te pakken", "click_to_add_to_storage": "Klik om toe te voegen aan de opslag", "click_to_exercise": "Klik om te oefenen", "click_to_view_job_offer": "Klik om de vacature te bekijken", "click_to_use": "Klik om te gebruiken", "click_to_talk": "Klik om te praten", "click_to_drive": "Klik om te rijden", "click_to_purchase": "Klik om te kopen", "help_itemname_consumergoodsassemblymachine_content": "**Consumptiegoederen assemblagemachine** is een fabrieksmachine.\n\nDe machine ontvangt items van een [Input Station](furniture-inputstation) of een andere Factory Machine om de volgende items te produceren:\n\n* [Arty Fish Phone](products-smartphone1)\n* [Arty Fish Smartwatch](products-smartwatch2)\n* [Audio Module (Medium)](products-mediumaudiomodule)\n* [Audio Module (Small)](products-smallaudiomodule)\n* [Circuit Board (Medium)](products-mediumcircuitboard)\n* [Circuit Board (Small)](products-smallcircuitboard)\n* [Noize Boss Earbuds](products-earbuds01)\n* [Gift (Duur)](products-expensivegift)\n* [<PERSON><PERSON><PERSON> (Goedkoop)](products-cheapjewelry)\n* [<PERSON><PERSON><PERSON> (Duur)](products-expensivejewelry)\n* [Rhythm By Tre Koptelefoons](products-headphones01)\n* [ZanaMan Phone](products-smartphone2)\n* [ZanaMan Smartwatch](products-smartwatch1)\n\nDe machine kan worden gekocht op de volgende locaties:\n* [Factory Supply Depot](adres:57 5a)", "recipes_cheapgiftrecipe": "<PERSON><PERSON> (Goedkoop) Productieplan", "help_recipes_cheapgiftrecipe_content": "**<PERSON><PERSON> (Goedkoop) Recept** kan worden geproduceerd in een [Fabriek](businesstypes-factory).\n\n**Vereiste ruwe ingrediënten:**\n\n* [Klei](producten-klei)\n\n**Vereiste fabrieksmachines:**\n\n* [Ovenmachine](meubel-ovenmachine)\n* [Lasersnijmachine](meubel-lasercuttingmachine)\n* [Invoerstation](meubel-invoerstation)\n* [Uitvoerstation](meubel-uitvoerstation)\n\n**Recept**\n\n* Maak [Keramische mok](producten-keramicmug) in de [Ovenmachine](meubel-ovenmachine)\n* Maak [Cadeau (Goedkoop)](producten-goedkopegift) in de [Lasersnijmachine](meubel-lasercuttingmachine)", "recipes_expensivegiftrecipe": "<PERSON><PERSON> (duur) recept", "help_recipes_expensivegiftrecipe_content": "**<PERSON>au (duur) recept** kan worden geproduceerd in een [fabriek](businesstypes-factory).\n\n**Vereiste ruwe ingrediënten:**\n\n* [Glas](producten-glas)\n* [Plastic](producten-plastic)\n* [Water](producten-water)\n\n**Vereiste fabrieksmachines:**\n\n* [Oven](meubels-ovenmachine)\n* [Consumptiegoederenassemblagemachine](meubels-consumptiegoederenassemblagemachine)\n* [Lasersnijmachine](meubels-lasersnijmachine)\n* [Invoerstation](meubels-invoerstation)\n* [Uitvoerstation](meubels-uitvoerstation)\n\n**Recept**\n\n* Maak [<PERSON><PERSON><PERSON> koepel (klein)](producten-kleineglazenkoepel)\n* Maak [Gegoten sneeuwbolbasis](producten-gegotensneeuwbolbasis)\n* Maak [Gegoten sneeuwbolfiguur](producten-gegotensneeuwbolfiguur)\n\n* Maak [<PERSON><PERSON>chenk (Duur)](producten-duurgeschenk) in de [Consumptiegoederenassemblagemachine](meubels-consumptiegoederenassemblagemachine)", "click_to_learn": "Klik om te leren", "click_to_preview_designs": "Klik om een voorbeeld van de ontwerpen te bekijken", "click_to_return": "Klik om terug te keren", "itemoverlay_machine_speed": "{bedrag} minuten om te produceren", "click_to_get_haircut": "Klik om een knipbeurt te krijgen", "click_to_order": "Klik om te bestellen", "click_to_refuel": "Klik om te tanken", "click_to_work": "Klik om te werken", "employeehelper_notification_employee_amount_finished_training": "{bedrag} medewerkers hebben de opleiding afgerond", "common_discard_changes": "Veranderingen ongedaan maken", "click_to_pay_taxes": "Klik om belasting te betalen", "itemname_smallsignwithtext": "<PERSON> bordje met tekst", "itemname_cardreader": "<PERSON><PERSON><PERSON><PERSON>", "itemname_board01": "Bord 1", "itemname_board02": "Bord 2", "itemname_productdisplaystandtiered": "Displaystandaard (gelaagd product)", "itemname_turnstilepillar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_nightclubweekdaycovercharge": "Nachtclub Toegangsprijs doordeweeks", "notification_clickpark_cannotpark": "Het voertuig kan niet op deze positie worden geparkeerd", "help_itemname_nightclubcovercharge_content": "**Nightclub Cover Charge** is een vergoeding die wordt geïnd bij klanten wanneer ze [Nightclubs](businesstypes-nightclub) betreden.\n\nDe vergoeding bestaat in twee varianten: \n* Weekday Cover Charge\n* Weekend Cover Charge\n\nEr is niets vereist om deze vergoeding te innen, het gebeurt automatisch wanneer klanten het bedrijf betreden.", "common_character": "KARAKTER", "custom_game_presets_preset_already_exists": "Preset met naam '{name}' bestaat al. Wilt u deze overschrijven?", "itemname_flatbedspawnerstacked": "Vlakke bed spawner", "notification_deleted_all_contacts_from_category": "<PERSON>e contacten van {name} verwi<PERSON><PERSON>d", "character_creation_blendshape_mouth_corners": "<PERSON><PERSON><PERSON>", "character_creation_blendshape_eyes_angle": "Hoek", "bizman_schedule_employee_days": "Medewerker is toegewezen om {daysAssigned} dagen te werken en heeft deze week {daysWorked} dagen gewerkt", "start_editing_seasons_warning": "Je probeert een bedrijf te bewerken zonder de seizoensdecoraties aan of uit te zetten. Weet je het zeker?", "view_candidates": "Bekijk kandidaten", "change_text": "Tekst wijzigen"}