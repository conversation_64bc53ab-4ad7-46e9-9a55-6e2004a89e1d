<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">成长计划</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-regression@1.1.0/build/chartjs-plugin-regression.min.js"></script>
    <script>
    // 声明全局变量，解决作用域问题
    const ASYLUM_DAYS = 30; // Insanity Asylum总天数
    const MONTHLY_DAYS = 30; // 每月天数
    const WEEKS_TO_SHOW = 6; // 习惯矩阵显示最近6周
    const DAYS_PER_WEEK = 7;
    const WEEKDAYS_ZH = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const DAY_IN_MINUTES = 1440;
    
    // 全局数据和图表实例
    let habitList = [];
    let bookData = [];
    let transactionData = [];
    let guitarPracticeLog = [];
    let guitarRepertoire = [];

    let spotlightChart = null;
    let lifestyleWeightChart = null;
    let lifestyleFatChart = null;
    let lifestyleCalorieChart = null;
    let modalChart = null;
    
    // 力量+有氧平衡训练计划 (保持肌肉量 + 700大卡消耗)
    const asylumSchedule = [
        // Week 1: 基础适应期 (建立力量基础，适应训练强度)
        {
            startDay: 1, endDay: 7, phase: '基础适应期', week: 1,
            workouts: ['P90X胸背+骑行', 'T25双练+骑行', 'P90X腿部+T25', 'Insanity+P90X腹肌', 'P90X肩臂+骑行', '纯Insanity强化', 'P90X瑜伽恢复']
        },
        // Week 2: 强度提升期 (增加训练密度和强度)
        {
            startDay: 8, endDay: 14, phase: '强度提升期', week: 2,
            workouts: ['P90X腿部+T25', '力量循环训练', 'P90X胸背+骑行', 'T25双练+骑行', 'P90X肩臂+骑行', 'Insanity+P90X腹肌', '积极恢复骑行']
        },
        // Week 3: 综合挑战期 (力量与有氧高强度结合)
        {
            startDay: 15, endDay: 21, phase: '综合挑战期', week: 3,
            workouts: ['力量循环训练', 'P90X腿部+T25', '纯Insanity强化', 'P90X胸背+骑行', 'T25双练+骑行', 'P90X肩臂+骑行', 'P90X瑜伽恢复']
        },
        // Week 4: 巅峰冲刺期 (最大强度综合挑战)
        {
            startDay: 22, endDay: 30, phase: '巅峰冲刺期', week: 4,
            workouts: ['P90X胸背+骑行', '纯Insanity强化', 'P90X腿部+T25', '力量循环训练', 'T25双练+骑行', 'Insanity+P90X腹肌', 'P90X肩臂+骑行', '纯Insanity强化', '积极恢复骑行']
        }
    ];

    // 力量+有氧平衡训练项目 (P90X力量 + Insanity有氧 + 公路车耐力)
    const asylumWorkouts = {
        // === P90X 力量训练 (6-8大卡/分钟，保持肌肉量) ===
        'P90X 胸部+背部': { duration: 60, calories: 400, type: 'strength', description: '上肢推拉肌群训练' },
        'P90X 肩膀+手臂': { duration: 60, calories: 350, type: 'strength', description: '肩部手臂专项训练' },
        'P90X 腿部+背部': { duration: 60, calories: 450, type: 'strength', description: '下肢+背部复合训练' },
        'P90X 胸肩三头': { duration: 60, calories: 380, type: 'strength', description: '推举肌群专项' },
        'P90X 背部二头': { duration: 60, calories: 370, type: 'strength', description: '拉力肌群专项' },
        'P90X 腿部专项': { duration: 60, calories: 420, type: 'strength', description: '下肢力量强化' },

        // === Insanity 高强度有氧 (12-15大卡/分钟) ===
        'Insanity Plyometric': { duration: 42, calories: 500, type: 'cardio', description: '爆发力间歇训练' },
        'Insanity Cardio Power': { duration: 40, calories: 480, type: 'cardio', description: '心肺功能训练' },
        'Insanity Pure Cardio': { duration: 38, calories: 450, type: 'cardio', description: '纯有氧高强度' },
        'Insanity Max Interval': { duration: 59, calories: 600, type: 'interval', description: '最大间歇挑战' },
        'Insanity Cardio Abs': { duration: 37, calories: 400, type: 'cardio', description: '心肺+核心' },

        // === T25 高效训练 (10-12大卡/分钟) ===
        'T25 Total Body': { duration: 25, calories: 250, type: 'strength', description: '全身力量循环' },
        'T25 Cardio': { duration: 25, calories: 280, type: 'cardio', description: '心肺循环训练' },
        'T25 Speed': { duration: 25, calories: 300, type: 'speed', description: '速度敏捷训练' },
        'T25 Upper Focus': { duration: 25, calories: 220, type: 'strength', description: '上肢专项训练' },
        'T25 Lower Focus': { duration: 25, calories: 280, type: 'strength', description: '下肢专项训练' },

        // === 公路车训练 (8-12大卡/分钟) ===
        '公路车中等30分钟': { duration: 30, calories: 280, type: 'endurance', description: '中等强度骑行' },
        '公路车中等45分钟': { duration: 45, calories: 400, type: 'endurance', description: '稳态有氧骑行' },
        '公路车强度30分钟': { duration: 30, calories: 350, type: 'interval', description: '高强度间歇骑行' },
        '公路车恢复30分钟': { duration: 30, calories: 250, type: 'recovery', description: '恢复性骑行' },

        // === 力量+有氧组合 (确保700大卡 + 肌肉保持) ===
        'P90X胸背+骑行': { duration: 90, calories: 700, type: 'combo', description: 'P90X胸背(400) + 30分钟中等骑行(300)' },
        'P90X腿部+T25': { duration: 85, calories: 700, type: 'combo', description: 'P90X腿部(450) + T25 Cardio(250)' },
        'P90X肩臂+骑行': { duration: 90, calories: 700, type: 'combo', description: 'P90X肩臂(350) + 30分钟强度骑行(350)' },
        'Insanity+P90X腹肌': { duration: 70, calories: 700, type: 'combo', description: 'Insanity训练(480) + P90X腹肌(220)' },
        'T25双练+骑行': { duration: 80, calories: 700, type: 'combo', description: 'T25上肢(220) + T25下肢(280) + 骑行(200)' },
        '纯Insanity强化': { duration: 60, calories: 700, type: 'cardio', description: 'Insanity Max Interval + 腹肌强化' },
        '力量循环训练': { duration: 90, calories: 700, type: 'circuit', description: 'P90X动作 + 有氧间歇循环' },

        // === 恢复训练 ===
        'P90X瑜伽恢复': { duration: 45, calories: 200, type: 'recovery', description: 'P90X瑜伽 + 拉伸' },
        '积极恢复骑行': { duration: 40, calories: 300, type: 'recovery', description: '轻松骑行 + 拉伸' }
    };
    </script>
    <style>
        /* --- 页面加载动画 --- */
        body {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }
        
        body.loaded {
            opacity: 1;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f8f9ff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
        }
        
        body.loaded .loading-overlay {
            opacity: 0;
            visibility: hidden;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 4px solid rgba(249, 115, 22, 0.1);
            border-top-color: #f97316;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* --- 现代化UI主题：动态圆角 --- */
        :root {
            /* 苹果风格配色 - 橙色主题 */
            --bg-color: #fafafa;
            --bg-gradient: linear-gradient(135deg, #fafafa 0%, #f5f5f7 100%);
            --container-bg: rgba(255, 255, 255, 0.8);
            --card-bg: rgba(255, 255, 255, 0.7);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --border-color: rgba(0, 0, 0, 0.08);
            --input-bg: rgba(255, 255, 255, 0.9);
            --bg-secondary: #f5f5f7;
            --hover-bg: rgba(255, 149, 0, 0.08);

            /* 苹果风格文字颜色 */
            --text-primary: #1d1d1f;
            --text-secondary: #86868b;
            --text-tertiary: #a1a1a6;
            --text-accent: #ff9500;
            --text-white: #ffffff;

            /* 橙色主色调 */
            --primary-accent: #ff9500;
            --primary-accent-rgb: 255, 149, 0;
            --primary-accent-hover: #e6850e;
            --secondary-accent: #ff6b35;
            --tertiary-accent: #ffb84d;
            --primary-gradient: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
            --orange-gradient: linear-gradient(45deg, #ff9500, #ff6b35, #ffb84d);
            --highlight-bg: rgba(255, 149, 0, 0.1);

            /* 状态颜色 */
            --success-color: #30d158;
            --success-gradient: linear-gradient(135deg, #30d158, #32d74b);
            --failure-color: #ff453a;
            --error-color: #ff3b30;
            --warning-color: #ff9f0a;
            --failure-gradient: linear-gradient(135deg, #ff453a, #ff3b30);

            /* 图表颜色 */
            --sleep-color: #5856d6;
            --sleep-gradient: linear-gradient(135deg, #5856d6, #af52de);
            --week-header-bg: #f2f2f7;

            /* 苹果风格阴影 */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
            --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            --orange-glow: 0 0 20px rgba(255, 149, 0, 0.3);
            --inner-light: inset 0 1px 0 rgba(255, 255, 255, 0.6);

            /* 苹果风格圆角 */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
            --radius-full: 9999px;

            /* 苹果风格动画 */
            --transition-fast: 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --transition-normal: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --transition-slow: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        /* --- 全局样式与现代排版 --- */
        * { 
            box-sizing: border-box;
            transition: all var(--transition-fast) ease;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.47059;
            margin: 0;
            padding: 20px;
            font-size: 17px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            position: relative;
            font-weight: 400;
        }

        /* 苹果风格背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 149, 0, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 184, 77, 0.02) 0%, transparent 50%);
            z-index: -1;
        }

        h1, h2, h3 {
            font-weight: 600;
            letter-spacing: -0.022em;
            margin-bottom: 0.5em;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            text-align: center;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            font-weight: 700;
            letter-spacing: -0.03em;
        }

        h2 {
            font-size: 1.5rem;
            margin-top: 2.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-primary);
            position: relative;
        }

        h2::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 40px;
            height: 2px;
            background: var(--primary-gradient);
            border-radius: 1px;
        }

        h3 {
            font-size: 1.25rem;
            margin-top: 1.5rem;
            color: var(--text-primary);
            font-weight: 600;
        }
        
        /* --- 苹果风格容器 --- */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--container-bg);
            padding: 2rem;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
            box-shadow: var(--card-shadow);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: relative;
        }
        
        .instructions { 
            background-color: var(--highlight-bg); 
            border-left: 4px solid var(--primary-accent); 
            padding: 1rem 1.25rem; 
            margin: 1.25rem 0; 
            border-radius: var(--radius-md); 
            color: #9a3412;
            font-size: 0.95em;
            box-shadow: var(--shadow-sm);
            animation: fadeIn 0.6s ease-out;
        }
        
        /* --- 苹果风格导航标签 --- */
        .nav-tabs {
            display: flex;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 0.5rem;
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(250, 250, 250, 0.8);
            padding: 0.75rem;
            border-radius: var(--radius-lg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .nav-tab {
            padding: 0.75rem 1.25rem;
            cursor: pointer;
            background: transparent;
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.9em;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all var(--transition-normal);
            position: relative;
            white-space: nowrap;
        }

        .nav-tab.active {
            color: var(--text-white);
            font-weight: 600;
            background: var(--primary-gradient);
            box-shadow: var(--shadow-sm);
            transform: scale(1.02);
        }

        .nav-tab:not(.active):hover {
            color: var(--primary-accent);
            background: var(--hover-bg);
            transform: scale(1.01);
        }

        /* --- 标签内容 --- */
        .tab-content { 
            display: none; 
            opacity: 0;
            transform: translateY(10px);
        }
        
        .tab-content.active { 
            display: block; 
            animation: fadeInUp 0.5s forwards;
        }
        
        @keyframes fadeInUp { 
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            } 
            to { 
                opacity: 1; 
                transform: translateY(0); 
            } 
        }
        
        @keyframes fadeIn { 
            from { opacity: 0; } 
            to { opacity: 1; } 
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* --- 苹果风格表格 --- */
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 1.5rem;
            font-size: 0.9em;
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
        }

        th, td {
            padding: 0.875rem 0.75rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--week-header-bg);
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.85em;
            position: sticky;
            top: 0;
            z-index: 5;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            background: rgba(255, 255, 255, 0.5);
            transition: all var(--transition-fast);
        }

        .week-header td {
            background: var(--primary-gradient);
            font-weight: 600;
            text-align: left;
            padding: 1rem 1.25rem;
            font-size: 0.95em;
            color: var(--text-white);
        }

        tr:hover td {
            background: var(--hover-bg);
        }

        tr.today-highlight td, th.today-highlight {
            background: rgba(255, 149, 0, 0.1) !important;
            font-weight: 600;
            border-left: 3px solid var(--primary-accent);
        }
        
        /* --- 苹果风格表单与输入 --- */
        input, select, textarea {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.75rem 1rem;
            width: 100%;
            font-family: inherit;
            font-size: 1rem;
            transition: all var(--transition-normal);
            background: var(--input-bg);
            color: var(--text-primary);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: var(--inner-light);
        }

        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 3px rgba(255, 149, 0, 0.2);
            outline: none;
            background: rgba(255, 255, 255, 0.95);
        }

        input[type="number"] {
            width: 80px;
        }

        input[type="checkbox"] {
            width: auto;
            height: 1.25rem;
            width: 1.25rem;
            cursor: pointer;
            accent-color: var(--primary-accent);
            border-radius: 3px;
        }

        .date-display {
            font-size: 0.8em;
            color: var(--text-tertiary);
            margin-left: 6px;
        }

        button {
            background: var(--primary-gradient);
            color: var(--text-white);
            border: none;
            border-radius: var(--radius-sm);
            padding: 0.75rem 1.25rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-sm);
            font-size: 0.9em;
            letter-spacing: -0.01em;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            background: linear-gradient(135deg, var(--primary-accent-hover) 0%, var(--secondary-accent) 100%);
        }

        button:active {
            transform: translateY(0);
            transition: all 0.1s;
        }
        
        /* --- 苹果风格数据卡片 --- */
        .summary-box {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .summary-item {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--card-shadow);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all var(--transition-normal);
            position: relative;
        }

        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .summary-icon {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-md);
            background: var(--primary-gradient);
            color: var(--text-white);
            box-shadow: var(--shadow-sm);
            font-size: 1.5em;
        }

        .summary-icon.failure {
            background: var(--failure-gradient);
        }

        .summary-icon svg {
            width: 24px;
            height: 24px;
        }

        .summary-content {
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        .summary-item .label {
            font-size: 0.85em;
            color: var(--text-secondary);
            margin: 0;
            line-height: 1.2;
            font-weight: 500;
        }

        .summary-item .value {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1.2;
            margin-top: 0.25rem;
            font-feature-settings: 'tnum';
        }

        .summary-item .value.success {
            color: var(--success-color);
        }

        .summary-item .value.failure {
            color: var(--failure-color);
        }
        
        .progress-container {
            margin: 1.5rem 0;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: var(--radius-full);
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: var(--primary-gradient);
            height: 100%;
            transition: width 1s var(--spring);
            border-radius: var(--radius-full);
            box-shadow: 0 0 8px rgba(255, 149, 0, 0.3);
        }
        
        /* --- 苹果风格图表容器 --- */
        .chart-container {
            margin-top: 2rem;
            padding: 1.5rem;
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-normal);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .chart-container:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .chart-container h3 {
            cursor: pointer;
            user-select: none;
            margin: 0;
            padding: 0.75rem;
            transition: all var(--transition-fast);
            border-radius: var(--radius-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
        }

        .chart-container h3:hover {
            background: var(--hover-bg);
        }

        .chart-container .toggle-arrow {
            transition: all var(--transition-normal) ease;
            font-size: 1.2em;
            color: var(--primary-accent);
        }

        .chart-container.open .toggle-arrow {
            transform: rotate(180deg);
        }
        
        #today-chart-spotlight { 
            height: 380px; 
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #spotlight-canvas-container { 
            max-width: 400px; 
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #charts-grid-container { 
            display: none; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 1.75rem; 
            margin-top: 1.25rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all var(--transition-slow);
        }
        
        .chart-container.open #charts-grid-container { 
            display: grid;
            animation: fadeInUp 0.5s forwards;
        }
        
        .line-chart-wrapper { 
            height: 320px; 
            position: relative;
        }
        
        /* 组件特定样式 */
        #book-adder, #habit-manager { 
            gap: 0.85rem; 
            margin: 1.25rem 0; 
            padding: 1.25rem; 
            background-color: #f8fafc; 
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
        
        #book-list, #habit-tracker-grid-container { 
            margin-top: 1.75rem;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.25rem;
        }
        
        .book-card {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
            border: 1px solid var(--border-color);
        }
        
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }
        
        .book-card.finished {
            border-left: 4px solid var(--success-color);
        }
        
        .book-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .book-title {
            font-weight: 600;
            font-size: 1.1em;
        }
        
        .book-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .book-finish-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }
        
        .book-delete-btn {
            background: transparent;
            color: var(--failure-color);
            box-shadow: none;
            padding: 0.25rem 0.5rem;
        }
        
        .habit-table {
            overflow-x: auto;
        }
        
        .habit-day-cell {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .habit-day-cell input {
            padding: 0.4rem 0.5rem;
            font-size: 0.85em;
        }
        
        .habit-name-col {
            font-weight: 500;
            min-width: 120px;
        }
        
        .habit-delete-btn {
            padding: 0.3rem 0.75rem;
            font-size: 0.85em;
        }
        
        .speech-challenge-box {
            background: var(--card-bg);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
        }
        
        .speech-day-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .speech-day-item {
            background: white;
            border-radius: var(--radius-sm);
            padding: 0.75rem;
            box-shadow: var(--shadow-sm);
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 0.75rem;
            align-items: center;
        }
        
        .speech-day-item.today-highlight {
            box-shadow: inset 0 0 0 2px var(--primary-accent);
        }
        
        /* 数据迁移部分 */
        .data-migration-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: var(--highlight-bg);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
        }
        
        .migration-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .migration-btn {
            padding: 0.75rem 1.5rem;
        }
        
        #import-data-input {
            display: none;
        }
        
        #import-data-label {
            background: white;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 响应式调 ?*/
        @media (max-width: 768px) {
            .container {
                padding: 1.25rem;
                border-radius: var(--radius-md);
            }
            
            .summary-box {
                grid-template-columns: 1fr;
            }
            
            .nav-tabs {
                gap: 0.25rem;
            }
            
            .nav-tab {
                padding: 0.6rem 0.75rem;
                font-size: 0.85em;
            }
            
            h1 {
                font-size: 1.8rem;
            }
            
            h2 {
                font-size: 1.3rem;
            }
        }
        
        /* 高级动态效 ?*/
        .summary-item:nth-child(1) {
            animation: slideInLeft 0.8s ease-out 0.1s both;
        }
        .summary-item:nth-child(2) {
            animation: slideInLeft 0.8s ease-out 0.2s both;
        }
        .summary-item:nth-child(3) {
            animation: slideInLeft 0.8s ease-out 0.3s both;
        }
        .summary-item:nth-child(4) {
            animation: slideInLeft 0.8s ease-out 0.4s both;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px) translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0) translateY(0);
            }
        }

        .summary-item.highlight-effect {
            animation: neonPulse 2s ease-in-out infinite;
        }

        @keyframes neonPulse {
            0%, 100% {
                box-shadow: var(--shadow-md), var(--inner-glow);
                border-color: var(--border-color);
            }
            50% {
                box-shadow: var(--neon-glow-strong), var(--shadow-lg);
                border-color: var(--primary-accent);
            }
        }

        /* 数据加载动画 */
        .loading-data {
            position: relative;
            overflow: hidden;
        }

        .loading-data::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.3), transparent);
            animation: dataLoad 1.5s infinite;
        }

        @keyframes dataLoad {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 个人设置部分 */
        .profile-settings {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.25rem;
            margin: 1.5rem 0;
            padding: 1.5rem;
            background: var(--card-bg);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .profile-field {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .profile-field label {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 0.95em;
        }

        /* 今日数据录入样式 */
        .today-input-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
            transition: all 0.3s ease;
        }

        /* 今天模式样式 */
        .today-input-grid.today-mode {
            border: 2px solid var(--primary-accent);
        }

        /* 历史模式样式 */
        .today-input-grid.historical-mode {
            opacity: 0.9;
        }

        .today-input-grid.historical-mode .input-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .today-input-grid.historical-mode .card-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        /* 只读模式输入框样 ?*/
        .readonly-mode {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .readonly-mode:focus {
            box-shadow: none !important;
            border-color: #dee2e6 !important;
        }

        /* 只读模式下的滑块样式 */
        input[type="range"].readonly-mode {
            pointer-events: none;
            opacity: 0.7;
        }

        /* 只读模式下的选择框样 ?*/
        select.readonly-mode {
            pointer-events: none;
            background-color: #f8f9fa !important;
        }

        .input-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all var(--transition-normal);
        }

        .input-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .input-card.full-width {
            grid-column: 1 / -1;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            color: white;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
        }

        .card-icon {
            font-size: 1.3em;
        }

        .card-title {
            font-size: 1em;
        }

        .card-content {
            padding: 1.5rem;
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group:last-child {
            margin-bottom: 0;
        }

        .input-group label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9em;
            margin-bottom: 0.5rem;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--input-bg);
            font-family: inherit;
            font-size: 0.9em;
            transition: all var(--transition-normal);
        }

        .input-group input:focus,
        .input-group select:focus,
        .input-group textarea:focus {
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 3px rgba(255, 149, 0, 0.2);
            outline: none;
        }

        .input-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        /* 睡眠输入样式 */
        .sleep-inputs {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .sleep-inputs input {
            width: 100px;
            text-align: center;
        }

        .arrow {
            color: var(--primary-accent);
            font-weight: bold;
            font-size: 1.2em;
        }

        .sleep-duration {
            text-align: center;
            font-weight: 600;
            color: var(--primary-accent);
            font-size: 1em;
            padding: 0.5rem;
            background: var(--highlight-bg);
            border-radius: var(--radius-sm);
        }

        /* 心情控制样式 */
        .mood-control input[type="range"] {
            width: 100%;
            margin-bottom: 0.75rem;
            height: 6px;
            border-radius: 3px;
            background: var(--bg-secondary);
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .mood-control input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-accent);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .mood-display {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.75rem;
        }

        .mood-display #mood-value {
            font-weight: 700;
            color: var(--primary-accent);
            font-size: 1.3em;
        }

        .mood-emoji {
            font-size: 1.8em;
        }

        /* 饮食输入样式 */
        .meal-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .total-display {
            text-align: center;
            padding: 0.75rem;
            background: var(--highlight-bg);
            border-radius: var(--radius-sm);
            font-weight: 600;
        }

        .total-value {
            color: var(--primary-accent);
            font-size: 1.1em;
        }

        /* 计算显示样式 */
        .calc-display {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .calc-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            background: var(--bg-secondary);
            border-radius: var(--radius-sm);
            font-size: 0.9em;
        }

        .calc-item.highlight {
            background: var(--highlight-bg);
            font-weight: 600;
            color: var(--primary-accent);
        }

        /* 复盘输入样式 */
        .reflection-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 1.5rem;
            align-items: start;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            color: var(--text-primary);
        }

        .checkbox-label input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-accent);
        }

        /* 睡眠卡片样式 */
        .sleep-inputs {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .sleep-inputs input {
            width: 85px;
            padding: 0.6rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            text-align: center;
            font-size: 0.9em;
            background: var(--input-bg);
        }

        .arrow {
            color: var(--primary-accent);
            font-weight: bold;
            font-size: 1.2em;
        }

        .sleep-duration {
            font-weight: 600;
            color: var(--primary-accent);
            font-size: 1em;
            padding: 0.5rem 1rem;
            background: var(--highlight-bg);
            border-radius: var(--radius-sm);
        }

        /* 心情卡片样式 */
        .mood-card input[type="range"] {
            width: 100%;
            margin-bottom: 1rem;
            height: 6px;
            border-radius: 3px;
            background: var(--bg-secondary);
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .mood-card input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-accent);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .mood-display {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.75rem;
        }

        .mood-display #mood-value {
            font-weight: 700;
            color: var(--primary-accent);
            font-size: 1.5em;
        }

        .mood-emoji {
            font-size: 2em;
        }

        /* 目标卡片样式 */
        .goal-card input[type="range"] {
            width: 100%;
            margin-bottom: 1rem;
            height: 6px;
            border-radius: 3px;
            background: var(--bg-secondary);
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .goal-card input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--success-color);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .goal-display {
            font-weight: 700;
            color: var(--success-color);
            font-size: 1.8em;
        }

        /* 复盘卡片样式 */
        .checkbox-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            font-weight: 600;
        }

        .checkbox-label input[type="checkbox"] {
            width: 24px;
            height: 24px;
            accent-color: var(--primary-accent);
            transform: scale(1.2);
        }

        /* 今日数据输入样式 */
        .today-data-input {
            margin: 2rem 0;
        }

        .input-section {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .input-section h4 {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            color: white;
            margin: 0;
            padding: 1rem 1.5rem;
            font-size: 1em;
            font-weight: 600;
        }

        .input-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .input-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-item label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9em;
        }

        .input-item input,
        .input-item select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--input-bg);
            font-family: inherit;
            font-size: 0.9em;
            transition: all var(--transition-normal);
        }

        .input-item input:focus,
        .input-item select:focus {
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 3px rgba(255, 149, 0, 0.2);
            outline: none;
        }

        .auto-calc-display {
            padding: 0.75rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            font-weight: 600;
            color: var(--primary-accent);
            text-align: center;
            font-size: 0.9em;
        }

        /* 今日记录样式 */
        .today-notes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .note-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all var(--transition-normal);
        }

        .note-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .note-header {
            background: linear-gradient(135deg, var(--highlight-bg), rgba(255, 149, 0, 0.1));
            padding: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .note-card textarea,
        .note-card input {
            width: 100%;
            padding: 1rem;
            border: none;
            background: transparent;
            font-family: inherit;
            resize: vertical;
            outline: none;
        }

        .note-card textarea {
            min-height: 80px;
        }

        /* 数据概览网格样式 */
        .data-overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .overview-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-accent);
        }

        .overview-header {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .overview-icon {
            font-size: 1.5em;
        }

        .overview-title {
            font-weight: 600;
            flex: 1;
            margin-left: 0.75rem;
        }

        .overview-action {
            font-size: 1.2em;
            opacity: 0.8;
        }

        .overview-chart {
            padding: 1rem;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-secondary);
        }

        .overview-stats {
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            background: var(--card-bg);
        }

        .stat-item {
            font-size: 0.85em;
            color: var(--text-secondary);
        }

        .stat-item span {
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--container-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            max-width: 90vw;
            max-height: 90vh;
            width: 800px;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: white; /* Ensure text is white */
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color var(--transition-fast);
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 2rem;
            max-height: 70vh;
            overflow-y: auto;
        }

        #modal-chart-container {
            margin-bottom: 2rem;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #modal-table-container {
            overflow-x: auto;
        }

        /* 弹窗表格样式 */
        .modal-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            font-size: 0.9em;
        }

        .modal-table th {
            background: var(--week-header-bg);
            padding: 0.75rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-align: center;
            font-size: 0.85em;
        }

        .modal-table td {
            padding: 0.75rem;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            background: rgba(255, 255, 255, 0.5);
        }

        .modal-table tr:hover td {
            background: var(--hover-bg);
        }

        .modal-table tr:last-child td {
            border-bottom: none;
        }

        /* 日期导航栏样 ?*/
        .date-navigation {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 2rem;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .date-nav-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .date-nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .date-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .current-date-display {
            text-align: center;
            flex: 1;
        }

        #current-date-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        #current-date-subtitle {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .date-quick-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-1px);
        }

        .quick-action-btn.active {
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-accent);
            font-weight: 600;
        }

        .date-picker-input {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 0.5rem;
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        /* 点击外部关闭弹窗 */
        .modal-overlay {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 响应式设 ?*/
        @media (max-width: 768px) {
            .today-input-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .data-overview-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .meal-inputs {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .reflection-inputs {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .card-header {
                padding: 0.75rem;
            }

            .card-content {
                padding: 1rem;
            }

            .sleep-inputs input {
                width: 80px;
                padding: 0.5rem;
                font-size: 0.9em;
            }

            .modal-content {
                width: 95vw;
                margin: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            #modal-chart-container {
                height: 200px;
            }
        }

        @media (max-width: 480px) {
            .today-input-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .overview-stats {
                flex-direction: column;
                gap: 0.5rem;
            }

            .stat-item {
                text-align: center;
            }

            .sleep-inputs {
                flex-direction: column;
                gap: 0.5rem;
            }

            .sleep-inputs input {
                width: 120px;
            }
        }

        /* 睡眠时间特殊样式 */
        .sleep-duration-display {
            margin-left: 1rem;
            padding: 0.5rem 1rem;
            background: var(--highlight-bg);
            border-radius: var(--radius-sm);
            font-weight: 600;
            color: var(--primary-accent);
        }

        /* 生活数据网格布局 */
        #lifestyle-data-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-top: 1rem;
        }

        .data-category {
            background: var(--card-bg);
            padding: 1.5rem;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .data-category h4 {
            margin: 0 0 1rem 0;
            color: var(--primary-accent);
            font-size: 1em;
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        .compact-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 0.85em;
            border-radius: var(--radius-sm);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .compact-table th {
            background: var(--week-header-bg);
            padding: 0.5rem;
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.8em;
            text-align: center;
        }

        .compact-table td {
            padding: 0.4rem;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            background: rgba(255, 255, 255, 0.5);
        }

        .compact-table input,
        .compact-table select {
            width: 100%;
            padding: 0.25rem;
            border: 1px solid var(--border-color);
            border-radius: 3px;
            font-size: 0.8em;
            background: var(--input-bg);
        }

        .compact-table .auto-calc {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 响应式调 ?*/
        @media (min-width: 1200px) {
            #lifestyle-data-grid {
                grid-template-columns: 1fr 1fr;
            }

            .data-category:last-child {
                grid-column: 1 / -1;
            }
        }

        .sleep-duration-display {
            margin-left: auto;
            padding: 0.5rem;
            background: var(--week-header-bg);
            border-radius: var(--radius-sm);
            font-weight: 500;
            color: var(--text-secondary);
        }

        /* 动态效果增 ?*/
        @keyframes slideInRight {
            from {
                transform: translateX(30px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .summary-item {
            animation: fadeInUp 0.5s forwards;
        }

        .summary-item:hover .summary-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* 苹果风格微妙效果 */
        .subtle-hover {
            transition: all var(--transition-normal);
        }

        .subtle-hover:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* 响应式增 ?*/
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem;
                margin: 10px;
            }

            .summary-box {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .nav-tabs {
                gap: 0.5rem;
                padding: 0.5rem 0;
            }

            .nav-tab {
                padding: 0.8rem 1rem;
                font-size: 0.85em;
            }

            h1 {
                font-size: 2.2rem;
            }

            h2 {
                font-size: 1.4rem;
            }

            .profile-settings,
            #daily-reflection-container {
                grid-template-columns: 1fr;
            }

            .reflection-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .summary-item {
                padding: 1.5rem;
            }

            .summary-icon {
                width: 48px;
                height: 48px;
            }

            .summary-item .value {
                font-size: 1.5rem;
            }
        }

        .tab-ripple {
            position: absolute;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 1s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(2.5);
                opacity: 0;
            }
        }
        
        .nav-tab {
            position: relative;
            overflow: hidden;
        }
        
        .highlight-row {
            animation: highlightFade 2s forwards;
        }
        
        @keyframes highlightFade {
            0% { background-color: rgba(249, 115, 22, 0.2); }
            100% { background-color: transparent; }
        }
        
        /* 增强表格行悬停效 ?*/
        tbody tr {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        tbody tr:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            z-index: 1;
            position: relative;
        }
        
        /* 增强输入框焦点效 ?*/
        input:focus, select:focus, textarea:focus {
            transform: translateY(-1px);
        }
        
        /* 苹果风格简洁动 ?*/
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 苹果风格加载动画 */
        .loading-overlay {
            background: var(--bg-color);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--bg-secondary);
            border-top: 3px solid var(--primary-accent);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 苹果风格滚动 ?*/
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        /* 苹果风格选中文本 */
        ::selection {
            background: rgba(255, 149, 0, 0.2);
            color: var(--text-primary);
        }

        ::-moz-selection {
            background: rgba(255, 149, 0, 0.2);
            color: var(--text-primary);
        }

        /* 苹果风格焦点 ?*/
        *:focus {
            outline: 2px solid var(--primary-accent);
            outline-offset: 2px;
        }

        /* 滚动动画 */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }
        
        .animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 表格行动 ?*/
        .row-animate {
            opacity: 0;
            transform: translateX(-20px);
            animation: rowFadeIn 0.5s forwards;
            animation-delay: 0s;
        }
        
        @keyframes rowFadeIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* 标签切换动画增强 */
        .tab-content {
            position: relative;
        }
        
        .tab-content.active {
            z-index: 1;
        }
        
        /* 按钮点击效果 */
        button:active {
            transform: scale(0.95);
        }

        /* 时间分析表格样式调整 */
        #analysis-content table {
            font-size: 0.85em;
        }

        #analysis-content table th,
        #analysis-content table td {
            padding: 0.5rem 0.4rem;
            vertical-align: middle;
        }

        #analysis-content .pie-chart-container {
            height: 60px;
            width: 60px;
            margin: 0 auto;
        }

        #analysis-content tbody tr {
            line-height: 1.2;
        }

        #analysis-content .date-display {
            font-size: 0.8em;
        }

        /* 对比数据样式 */
        small.comparison {
            display: block;
            font-size: 0.75em;
            font-weight: 500;
            margin-top: 2px;
        }
        small.comparison.positive {
            color: var(--success-color);
        }
        small.comparison.negative {
            color: var(--failure-color);
        }

        #analysis-content .week-header td {
            padding: 0.5rem;
            font-size: 0.9em;
        }

        /* 时间数字显示样式 */
        #analysis-content span[id^="analysis-"] {
            font-size: 0.9em;
            display: inline-block;
            min-width: 30px;
            text-align: center;
        }

        #analysis-content strong[id^="analysis-tracked-total"] {
            color: var(--primary-accent);
        }

        #analysis-content span[id^="analysis-untracked-time"] {
            color: var(--text-secondary);
        }

        /* 表格行悬停效果优 ?*/
        #analysis-content tbody tr:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* 记账功能样式 */
        .finance-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .finance-add-transaction, .finance-budget-settings {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--card-shadow);
        }

        .finance-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .finance-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background: var(--input-bg);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .finance-input:focus {
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 2px rgba(var(--primary-accent-rgb), 0.2);
        }

        .finance-btn {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .finance-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .finance-btn-small {
            padding: 6px 10px;
            font-size: 0.9em;
            margin-right: 10px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .finance-btn-small:hover {
            background: var(--primary-hover);
        }

        .finance-btn-small.active {
            background: var(--secondary-color);
            color: var(--text-primary);
        }

        .finance-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .finance-chart-container {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--card-shadow);
        }

        .finance-chart-wrapper {
            height: 250px;
        }

        .finance-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .finance-filter {
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background: var(--input-bg);
        }

        .finance-transactions-container {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--card-shadow);
            overflow-x: auto;
        }

        .finance-table {
            width: 100%;
            border-collapse: collapse;
        }

        .finance-table th, .finance-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .finance-table th {
            background: var(--bg-secondary);
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .finance-table tr:hover {
            background: var(--hover-bg);
        }

        .finance-table .expense {
            color: var(--error-color);
        }

        .finance-table .income {
            color: var(--success-color);
        }

        .finance-action-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .finance-action-btn:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
        }

        /* 吉他练习页面样式 */
        .guitar-main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .guitar-practice-section, .guitar-repertoire-section {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--card-shadow);
        }

        .guitar-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .guitar-input {
            width: 100%;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background: var(--input-bg);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .guitar-input:focus {
            border-color: var(--primary-accent);
            box-shadow: 0 0 0 2px rgba(var(--primary-accent-rgb), 0.2);
        }

        .guitar-btn {
            background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .guitar-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .rating-input {
            display: flex;
            gap: 5px;
        }

        .rating-star {
            font-size: 1.5em;
            color: var(--text-secondary);
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .rating-star.active {
            color: #FFD700;
        }

        .song-list-container, .practice-log-container {
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .guitar-table {
            width: 100%;
            border-collapse: collapse;
        }

        .guitar-table th, .guitar-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .guitar-table th {
            background: var(--bg-secondary);
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .guitar-table tr:hover {
            background: var(--hover-bg);
        }

        .guitar-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .guitar-chart-container {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--card-shadow);
        }

        .guitar-chart-wrapper {
            height: 250px;
        }

        .difficulty-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            background: var(--bg-secondary);
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .status-learning {
            background: rgba(var(--primary-accent-rgb), 0.2);
            color: var(--primary-accent);
        }

        .status-practicing {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
        }

        .status-polishing {
            background: rgba(33, 150, 243, 0.2);
            color: #2196F3;
        }

        .status-mastered {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        /* 书架抽屉样式 */
        .bookshelf-drawer {
            margin-top: 30px;
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .bookshelf-header {
            padding: 20px;
            background: linear-gradient(135deg, #D2691E, #CD853F);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-bottom: 3px solid #8B4513;
        }

        .bookshelf-header:hover {
            background: linear-gradient(135deg, #E2751E, #DD953F);
            transform: translateY(-2px);
        }

        .bookshelf-title {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
        }

        .bookshelf-icon {
            width: 24px;
            height: 24px;
            color: white;
        }

        .book-count {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
            font-weight: normal;
        }

        .bookshelf-toggle {
            transition: transform 0.3s ease;
        }

        .bookshelf-drawer.expanded .bookshelf-toggle {
            transform: rotate(180deg);
        }

        .toggle-arrow {
            width: 20px;
            height: 20px;
            color: white;
        }

        .bookshelf-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease;
            background: linear-gradient(135deg, #F5DEB3, #DEB887);
        }

        .bookshelf-drawer.expanded .bookshelf-content {
            max-height: 600px;
        }

        .bookshelf-shelves {
            padding: 20px;
        }

        .bookshelf-shelf {
            position: relative;
            margin-bottom: 25px;
            min-height: 120px;
        }

        .shelf-wood {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 15px;
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border-radius: 8px;
            box-shadow:
                0 2px 4px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.1);
        }

        .shelf-wood::before {
            content: '';
            position: absolute;
            top: -3px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #654321, #8B4513);
            border-radius: 8px 8px 0 0;
        }

        .books-container {
            display: flex;
            gap: 8px;
            padding: 0 10px 20px 10px;
            flex-wrap: wrap;
            align-items: flex-end;
            min-height: 100px;
        }

        .book-spine {
            width: 45px;
            height: 90px;
            border-radius: 3px 3px 0 0;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow:
                2px 0 4px rgba(0,0,0,0.2),
                inset 1px 0 0 rgba(255,255,255,0.1);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 8px 4px;
            color: white;
            font-size: 10px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            overflow: hidden;
        }

        .book-spine:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow:
                2px 5px 15px rgba(0,0,0,0.3),
                inset 1px 0 0 rgba(255,255,255,0.2);
        }

        .book-title {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            text-align: center;
            line-height: 1.2;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .book-progress {
            width: 100%;
            height: 3px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 4px;
        }

        .book-progress-bar {
            height: 100%;
            background: rgba(255,255,255,0.8);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .book-status {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 1px solid rgba(255,255,255,0.5);
        }

        .book-status.completed {
            background: #4CAF50;
        }

        .book-status.reading {
            background: #FF9500;
        }

        .book-status.planned {
            background: #9E9E9E;
        }

        .bookshelf-footer {
            background: linear-gradient(135deg, #8B4513, #A0522D);
            padding: 15px 20px;
            border-top: 2px solid #654321;
        }

        .bookshelf-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 20px;
            text-align: center;
        }

        .stat-item {
            color: white;
        }

        .stat-number {
            display: block;
            font-size: 1.5em;
            font-weight: bold;
            color: #FFD700;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 书籍颜色主题 */
        .book-theme-red { background: linear-gradient(135deg, #E53E3E, #C53030); }
        .book-theme-blue { background: linear-gradient(135deg, #3182CE, #2C5282); }
        .book-theme-green { background: linear-gradient(135deg, #38A169, #2F855A); }
        .book-theme-purple { background: linear-gradient(135deg, #805AD5, #6B46C1); }
        .book-theme-orange { background: linear-gradient(135deg, #DD6B20, #C05621); }
        .book-theme-teal { background: linear-gradient(135deg, #319795, #2C7A7B); }
        .book-theme-pink { background: linear-gradient(135deg, #D53F8C, #B83280); }
        .book-theme-indigo { background: linear-gradient(135deg, #5A67D8, #4C51BF); }

        /* 不良习惯矩阵热力图样式 */
        .habit-matrix-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
            width: 100%;
            max-width: none;
            overflow-x: auto;
        }

        .habit-matrix-legend {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .legend-label {
            font-weight: bold;
            color: #333;
        }

        .legend-scale {
            display: flex;
            gap: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
            color: #666;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            border: 1px solid #ddd;
        }

        .legend-color.level-0 { background: #ebedf0; }
        .legend-color.level-1 { background: #ffcccb; }
        .legend-color.level-2 { background: #ff9999; }
        .legend-color.level-3 { background: #ff6666; }
        .legend-color.level-4 { background: #ff3333; }

        .habit-matrix-grid {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .habit-matrix-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            width: 100%;
            min-width: 1000px;
        }

        .habit-matrix-header {
            display: grid;
            grid-template-columns: 200px 1fr 1fr 1fr 1fr 1fr 1fr 1fr 200px;
            gap: 2px;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            width: 100%;
        }

        .habit-name-header {
            padding: 8px 12px;
            font-weight: bold;
            color: #333;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .habit-matrix-dates {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            justify-content: flex-start;
            align-items: center;
        }

        .date-header {
            padding: 8px 4px;
            font-weight: bold;
            color: #333;
            background: #f8f9fa;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            border-right: 1px solid #dee2e6;
        }

        .date-header.weekend {
            background: #fff3e0;
            color: #f57c00;
        }

        .date-header:last-child {
            border-right: none;
        }

        .date-header.weekend {
            color: #ff6b6b;
        }

        .date-header.today {
            color: #ff9500;
            background: rgba(255, 149, 0, 0.1);
            border-radius: 2px;
        }

        .habit-data-row {
            display: grid;
            grid-template-columns: 200px 1fr 1fr 1fr 1fr 1fr 1fr 1fr 200px;
            gap: 2px;
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            width: 100%;
        }

        .habit-data-row:hover {
            background: #f8f9fa;
        }

        .habit-name-cell {
            padding: 8px;
            background: #fff3e0;
            border-left: 4px solid #ff6b6b;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            font-weight: bold;
            color: #333;
            border-right: 1px solid #dee2e6;
        }

        .habit-name-cell.empty {
            background: #f8f9fa;
            border-left: 4px solid transparent;
        }

        .habit-name-cell span {
            flex: 1;
            text-align: left;
        }

        .habit-icon {
            width: 16px;
            height: 16px;
            color: #ff6b6b;
        }

        .habit-days-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            align-items: center;
        }

        .habit-day-cell {
            padding: 4px 2px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #333;
            min-height: 24px;
            background: #fff;
            border-right: 1px solid #dee2e6;
            border-radius: 3px;
        }

        .habit-day-cell:hover {
            background: #e3f2fd;
        }

        .habit-day-cell.weekend {
            background: #fff8f0;
        }

        .habit-day-cell.weekend:hover {
            background: #ffecb3;
        }

        .habit-day-cell:hover {
            transform: scale(1.1);
            border-color: #333;
            z-index: 10;
        }

        .habit-day-cell.level-0 {
            background: #ebedf0;
            color: #666;
        }
        .habit-day-cell.level-1 {
            background: #ffcccb;
            color: #333;
        }
        .habit-day-cell.level-2 {
            background: #ff9999;
            color: #333;
        }
        .habit-day-cell.level-3 {
            background: #ff6666;
            color: white;
        }
        .habit-day-cell.level-4 {
            background: #ff3333;
            color: white;
        }

        .habit-day-cell.today {
            border: 2px solid #ff9500;
            border-radius: 4px;
        }

        .habit-day-cell.weekend {
            border-style: dashed;
        }

        .habit-matrix-footer {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .matrix-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-card {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .stat-card .stat-number {
            display: block;
            font-size: 1.5em;
            font-weight: bold;
            color: #ff6b6b;
        }

        .stat-card .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .habit-tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .habit-tooltip.show {
            opacity: 1;
        }

        .habit-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #333;
        }

        /* 习惯记录弹窗 */
        .habit-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .habit-modal.show {
            display: flex;
        }

        .habit-modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .habit-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .habit-modal-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .habit-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .habit-modal-close:hover {
            background: #f0f0f0;
        }

        .habit-records-list {
            margin-bottom: 20px;
        }

        .habit-record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 8px;
            border-left: 4px solid #ff6b6b;
        }

        .habit-record-info {
            flex: 1;
        }

        .habit-record-time {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .habit-record-duration {
            font-size: 0.9em;
            color: #666;
        }

        .habit-record-actions {
            display: flex;
            gap: 8px;
        }

        .habit-record-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .habit-record-edit {
            background: #007bff;
            color: white;
        }

        .habit-record-edit:hover {
            background: #0056b3;
        }

        .habit-record-delete {
            background: #dc3545;
            color: white;
        }

        .habit-record-delete:hover {
            background: #c82333;
        }

        .habit-add-form {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border: 2px dashed #ddd;
        }

        .habit-add-title {
            font-weight: bold;
            margin-bottom: 12px;
            color: #333;
        }

        .habit-form-row {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            align-items: center;
        }

        .habit-form-group {
            flex: 1;
        }

        .habit-form-label {
            display: block;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
        }

        .habit-form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .habit-form-input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
        }

        .habit-add-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.2s;
        }

        .habit-add-btn:hover {
            background: #ff5252;
        }

        .habit-summary {
            background: #e8f5e8;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .habit-summary-text {
            font-weight: bold;
            color: #2e7d32;
        }

        /* 周视图特殊样式 */
        .week-row {
            display: grid;
            grid-template-columns: repeat(7, 1fr) 120px;
            gap: 2px;
            align-items: center;
            margin-bottom: 4px;
        }

        .week-label-cell {
            padding: 8px;
            background: #f0f0f0;
            color: #666;
            font-size: 10px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #dee2e6;
            border-radius: 3px;
        }

        /* 移除不需要的周样式 */

        /* 响应式设计 */
        @media (max-width: 768px) {
            .finance-controls, .finance-charts, .guitar-main-content, .guitar-charts {
                grid-template-columns: 1fr;
            }

            .book-spine {
                width: 35px;
                height: 75px;
                font-size: 9px;
            }

            .bookshelf-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            /* 习惯矩阵响应式 */
            .habit-row {
                grid-template-columns: 120px 1fr;
                gap: 10px;
                padding: 10px;
            }

            .habit-matrix-header {
                grid-template-columns: 120px 1fr;
                gap: 10px;
            }

            .habit-day-cell {
                font-size: 8px;
                min-height: 20px;
            }

            .date-header {
                font-size: 9px;
                min-height: 20px;
            }

            .habit-matrix-container {
                padding: 15px;
            }

            .matrix-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="container">
        <h1><span id="main-title">成长计划</span>
            <button onclick="generateTestData()" style="margin-left: 20px; padding: 8px 16px; background: #ff9500; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">生成测试数据</button>
            <button onclick="forceUpdateAllStats()" style="margin-left: 10px; padding: 8px 16px; background: #e74c3c; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">强制更新统计</button>
        </h1>

        <!-- 快捷指令输入条 -->
        <div class="quick-command-bar" style="margin: 1rem 0 2rem 0; display: flex; gap: 0.5rem;">
            <input type="text" id="quick-command-input" placeholder="快捷指令: '习惯 刷抖音 30m' 或 '读书 原子习惯 20p'" style="flex-grow: 1; background-color: var(--input-bg); border: 1px solid var(--border-color); padding: 0.5rem; border-radius: var(--radius-md); font-size: 14px;">
            <button id="execute-command-btn" class="subtle-hover" style="padding: 0.5rem 1rem; white-space: nowrap;">▶ 执行</button>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" id="tab-asylum">Insanity Asylum</button>
            <button class="nav-tab" id="tab-lifestyle">生活与复盘</button>
            <button class="nav-tab" id="tab-english">英语学习</button>
            <button class="nav-tab" id="tab-habit">不良习惯</button>
            <button class="nav-tab" id="tab-meditation">冥想</button>
            <button class="nav-tab" id="tab-reading">阅读</button>
            <button class="nav-tab" id="tab-speech">演讲训练</button>
            <button class="nav-tab" id="tab-analysis">时间分析</button>
            <button class="nav-tab" id="tab-finance">记账</button>
            <button class="nav-tab" id="tab-guitar">吉他练习</button>
            <button class="nav-tab" id="tab-insights">数据洞察</button>
        </div>

        <!-- Insanity Asylum Content Pane -->
        <div id="asylum-content" class="tab-content active">

             <h2>Insanity Asylum 进度</h2>
             <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">完成天数</span>
                        <span class="value" id="asylum-completed-days">0 / 30</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.362 5.214A8.252 8.252 0 0112 21 8.25 8.25 0 016.038 7.048 8.287 8.287 0 009 9.6a8.983 8.983 0 013.362-1.44m0 0a4.503 4.503 0 01-1.002-3.116A4.503 4.503 0 0112 3c1.257 0 2.417.373 3.362 1.002z" /></svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本月总消耗(kcal)</span>
                        <span class="value" id="asylum-total-kcal">0</span>
                    </div>
                </div>
             </div>
             <div class="progress-container"><div class="progress-bar" id="asylumProgressBar"></div></div>
             <p id="asylum-prediction-text" style="text-align: center; margin-top: 0.5rem; color: var(--text-secondary);"></p>
             <h3>训练日志</h3>
             <table><thead><tr><th style="width:12%">天数</th><th style="width:35%">训练内容</th><th>用时(分钟)</th><th style="width:20%">进行时间</th><th>消耗(kcal)</th><th>完成</th></tr></thead><tbody id="asylum-table-body"></tbody></table>
        </div>

        <!-- Lifestyle Content Pane -->
        <div id="lifestyle-content" class="tab-content">
            <!-- 日期导航栏 -->
            <div class="date-navigation">
                <div class="date-nav-header">
                    <button id="prev-day-btn" class="date-nav-btn">◀</button>
                    <div class="current-date-display">
                        <div id="current-date-text">今天 6月30日 周一</div>
                        <div id="current-date-subtitle">2025年</div>
                    </div>
                    <button id="next-day-btn" class="date-nav-btn">▶</button>
                </div>
                <div class="date-quick-actions">
                    <button id="today-btn" class="quick-action-btn active" title="查看今天的数据">今天</button>
                    <button id="yesterday-btn" class="quick-action-btn" title="查看昨天的数据">昨天</button>
                    <button id="week-ago-btn" class="quick-action-btn" title="查看一周前的数据">一周前</button>
                    <input type="date" id="date-picker" class="date-picker-input" title="选择任意日期查看历史数据">
                </div>
            </div>

            <!-- 模板管理器 -->
            <div class="template-manager" style="margin: 2rem 0; padding: 1.5rem; background: var(--card-bg); border-radius: var(--radius-lg); box-shadow: var(--card-shadow); display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                <h3 style="margin: 0; font-size: 1.1em; color: var(--text-primary); flex-basis: 100%;">每日模板</h3>

                <div class="template-section" style="display: flex; gap: 0.5rem; align-items: center;">
                    <label for="template-select" style="font-weight: 500;">加载模板:</label>
                    <select id="template-select" class="data-input" style="min-width: 150px;"></select>
                    <button id="apply-template-btn" class="subtle-hover">应用</button>
                </div>

                <div class="template-section" style="display: flex; gap: 0.5rem; align-items: center;">
                    <label for="template-name-input" style="font-weight: 500;">另存为:</label>
                    <input type="text" id="template-name-input" class="data-input" placeholder="例如：工作日模式" style="min-width: 150px;">
                    <button id="save-template-btn" class="subtle-hover">保存</button>
                </div>
            </div>

            <h2>个人信息与设置</h2>
            <div class="profile-settings" id="profile-settings">
                 <div class="profile-field"><label for="challenge-start-date">挑战开始日期</label><input type="date" id="challenge-start-date" class="data-input profile-input"></div>
                 <div class="profile-field"><label for="profile-age">年龄</label><input type="number" id="profile-age" class="data-input profile-input"></div>
                 <div class="profile-field"><label for="profile-gender">性别</label><select id="profile-gender" class="data-input profile-input"><option value="male">男</option><option value="female">女</option></select></div>
                 <div class="profile-field"><label for="profile-height">身高 (cm)</label><input type="number" id="profile-height" class="data-input profile-input"></div>
            </div>

            <!-- 生活数据概览 -->
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">😴</div>
                    <div class="summary-content">
                        <span class="label">平均睡眠</span>
                        <span class="value" id="avg-sleep-duration-display">0小时</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">😊</div>
                    <div class="summary-content">
                        <span class="label">平均心情</span>
                        <span class="value" id="avg-mood-rating-display">0分</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">📝</div>
                    <div class="summary-content">
                        <span class="label">复盘天数</span>
                        <span class="value" id="reflection-completed-days">0天</span>
                    </div>
                </div>
            </div>

            <div class="chart-container" id="lifestyle-chart-container">
                <h3 id="toggle-charts-btn">身体数据趋势 <span class="toggle-arrow">▼</span></h3>
                <div id="charts-grid-container">
                    <div class="line-chart-wrapper"><canvas id="lifestyle-weight-chart"></canvas></div>
                    <div class="line-chart-wrapper"><canvas id="lifestyle-fat-chart"></canvas></div>
                    <div class="line-chart-wrapper"><canvas id="lifestyle-calorie-chart"></canvas></div>
                </div>
            </div>
            
            <!-- 智能数据录入/查看区域 -->
            <h2 id="smart-data-title">📅 今日数据录入 (Day <span id="reflection-day-n">1</span>)</h2>

            <div class="today-input-grid" id="smart-data-container">
                <!-- 睡眠与心情 -->
                <div class="input-card">
                    <div class="card-header">
                        <span class="card-icon">😴</span>
                        <span class="card-title">睡眠 & 心情</span>
                    </div>
                    <div class="card-content">
                        <div class="input-group">
                            <label>睡眠时间</label>
                            <div class="sleep-inputs">
                                <input type="time" id="life-sleep-start" class="data-input life-source">
                                <span class="arrow">→</span>
                                <input type="time" id="life-sleep-end" class="data-input life-source">
                            </div>
                            <div class="sleep-duration" id="life-sleep-duration">-- 小时 --</div>
                        </div>
                        <div class="input-group">
                            <label>心情评分</label>
                            <div class="mood-control">
                                <input type="range" id="mood-rating" class="data-input life-source" min="1" max="10" value="5">
                                <div class="mood-display">
                                    <span id="mood-value">5</span>
                                    <span class="mood-emoji" id="mood-emoji">😐</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 身体数据 -->
                <div class="input-card">
                    <div class="card-header">
                        <span class="card-icon">⚖️</span>
                        <span class="card-title">身体数据</span>
                    </div>
                    <div class="card-content">
                        <div class="input-group">
                            <label>体重 (kg)</label>
                            <input type="number" step="0.1" id="life-weight" class="data-input life-source" placeholder="kg">
                        </div>
                        <div class="input-group">
                            <label>体脂 (%)</label>
                            <input type="number" step="0.1" id="life-fat" class="data-input life-source" placeholder="%">
                        </div>
                        <div class="input-group">
                            <label>身体状态</label>
                            <select id="life-status" class="data-input life-source">
                                <option value="">选择状态</option>
                                <option value="优秀">优秀 💪</option>
                                <option value="良好">良好 😊</option>
                                <option value="疲惫">疲惫 😴</option>
                                <option value="生病">生病 🤒</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 饮食记录 -->
                <div class="input-card">
                    <div class="card-header">
                        <span class="card-icon">🍽️</span>
                        <span class="card-title">饮食记录</span>
                    </div>
                    <div class="card-content">
                        <div class="meal-inputs">
                            <div class="input-group">
                                <label>早餐</label>
                                <input type="number" id="life-breakfast" class="data-input life-source" placeholder="kcal">
                            </div>
                            <div class="input-group">
                                <label>午餐</label>
                                <input type="number" id="life-lunch" class="data-input life-source" placeholder="kcal">
                            </div>
                            <div class="input-group">
                                <label>晚餐</label>
                                <input type="number" id="life-dinner" class="data-input life-source" placeholder="kcal">
                            </div>
                        </div>
                        <div class="total-display">
                            <span>总摄入 </span>
                            <span class="total-value" id="life-total-intake">0 kcal</span>
                        </div>
                    </div>
                </div>

                <!-- 运动消耗 -->
                <div class="input-card">
                    <div class="card-header">
                        <span class="card-icon">🔥</span>
                        <span class="card-title">运动消耗</span>
                    </div>
                    <div class="card-content">
                        <div class="input-group">
                            <label>其他运动</label>
                            <input type="number" id="life-kcal-out" class="data-input life-source calorie-source" placeholder="kcal">
                        </div>
                        <div class="calc-display">
                            <div class="calc-item">
                                <span>Asylum: </span>
                                <span id="life-asylum-burn">0</span>
                            </div>
                            <div class="calc-item">
                                <span>基础代谢: </span>
                                <span id="life-bmr">0</span>
                                <small style="display: block; font-size: 0.8em; color: var(--text-secondary);">K-M=含体脂率，M-S=标准公式</small>
                            </div>
                            <div class="calc-item highlight">
                                <span>热量平衡 </span>
                                <span id="life-deficit">0</span>
                                <small style="display: block; font-size: 0.8em; color: var(--text-secondary);">正数=盈余，负数=缺口</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 复盘记录 -->
                <div class="input-card full-width">
                    <div class="card-header">
                        <span class="card-icon">📝</span>
                        <span class="card-title">复盘记录</span>
                    </div>
                    <div class="card-content">
                        <div class="reflection-inputs">
                            <div class="input-group">
                                <label>今日收获</label>
                                <textarea id="daily-wins" class="data-input life-source" rows="2" placeholder="今天最大的收获是什么？"></textarea>
                            </div>
                            <div class="input-group">
                                <label>感恩记录</label>
                                <input type="text" id="gratitude-1" class="data-input life-source" placeholder="今天最感恩的一件事...">
                            </div>
                            <div class="input-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="reflection-check" class="data-input life-source">
                                    <span class="checkmark">✓ 完成今日复盘</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 30天数据趋势 -->
            <h2>📊 30天数据趋势</h2>
            <div class="data-overview-grid">
                <!-- 数据概览卡片 -->
                <div class="overview-card" onclick="openDataModal('sleep')">
                    <div class="overview-header">
                        <span class="overview-icon">😴</span>
                        <span class="overview-title">睡眠数据</span>
                        <span class="overview-action">📋</span>
                    </div>
                    <div class="overview-chart">
                        <canvas id="sleep-mini-chart" width="250" height="100"></canvas>
                    </div>
                    <div class="overview-stats">
                        <span class="stat-item">平均: <span id="avg-sleep-duration">0小时</span></span>
                        <span class="stat-item">最近: <span id="recent-sleep">--</span></span>
                    </div>
                </div>

                <div class="overview-card" onclick="openDataModal('mood')">
                    <div class="overview-header">
                        <span class="overview-icon">😊</span>
                        <span class="overview-title">心情数据</span>
                        <span class="overview-action">📋</span>
                    </div>
                    <div class="overview-chart">
                        <canvas id="mood-mini-chart" width="200" height="80"></canvas>
                    </div>
                    <div class="overview-stats">
                        <span class="stat-item">平均: <span id="avg-mood-rating">0分</span></span>
                        <span class="stat-item">最近: <span id="recent-mood">--</span></span>
                    </div>
                </div>

                <div class="overview-card" onclick="openDataModal('body')">
                    <div class="overview-header">
                        <span class="overview-icon">⚖️</span>
                        <span class="overview-title">身体数据</span>
                        <span class="overview-action">📋</span>
                    </div>
                    <div class="overview-chart">
                        <canvas id="weight-mini-chart" width="200" height="80"></canvas>
                    </div>
                    <div class="overview-stats">
                        <span class="stat-item">体重: <span id="recent-weight">--kg</span></span>
                        <span class="stat-item">体脂: <span id="recent-fat">--%</span></span>
                    </div>
                </div>

                <div class="overview-card" onclick="openDataModal('nutrition')">
                    <div class="overview-header">
                        <span class="overview-icon">🍽️</span>
                        <span class="overview-title">营养数据</span>
                        <span class="overview-action">📋</span>
                    </div>
                    <div class="overview-chart">
                        <canvas id="calorie-mini-chart" width="200" height="80"></canvas>
                    </div>
                    <div class="overview-stats">
                        <span class="stat-item">平均摄入: <span id="avg-intake">0kcal</span></span>
                        <span class="stat-item">热量差: <span id="avg-deficit">0kcal</span></span>
                    </div>
                </div>
            </div>


            
            <div class="data-migration-section">
                <h3>数据备份与恢复</h3>
                <div class="migration-buttons">
                    <button id="export-data-btn" class="migration-btn">导出全部数据</button>

                    <label for="import-data-input" id="import-data-label" class="migration-btn">覆盖导入...</label>
                    <input type="file" id="import-data-input" accept=".json" style="display: none;">

                    <label for="merge-import-input" id="merge-import-label" class="migration-btn" style="background-color: var(--success-color);">合并导入...</label>
                    <input type="file" id="merge-import-input" accept=".json" style="display: none;">
                </div>
            </div>
        </div>
        
        <!-- English Content Pane -->
        <div id="english-content" class="tab-content">
            
            <h2>5000单词学习进度</h2>
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">已学单词</span>
                        <span class="value" id="english-words-count">0</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本月学习时长</span>
                        <span class="value" id="english-total-time">0 分钟</span>
                    </div>
                </div>
            </div>
            <div class="progress-container"><div class="progress-bar" id="englishProgressBar"></div></div>
            <p id="english-prediction-text" style="text-align: center; margin-top: 0.5rem; color: var(--text-secondary);"></p>
            <table><thead><tr><th style="width:12%">天数</th><th style="width:12%">新学单词数</th><th style="width:12%">用时(分钟)</th><th style="width:20%">进行时间</th><th style="width:24%">学习内容</th><th style="width:20%">备注</th></tr></thead><tbody id="english-table-body"></tbody></table>
        </div>
        
        <!-- Bad Habit Content Pane -->
        <div id="habit-content" class="tab-content">
            
            <h2>不良习惯管理与统计</h2>
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon failure"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg></div>
                    <div class="summary-content"><span class="label">本月总发生次数</span><span class="value failure" id="habit-total-occurrences">0</span></div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon failure"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" /></svg></div>
                    <div class="summary-content"><span class="label">本月总耗时(分钟)</span><span class="value failure" id="habit-total-time">0</span></div>
                </div>
            </div>
            <div id="habit-manager">
                <input type="text" id="new-habit-name" placeholder="输入不良习惯名称 (例如：刷短视频)">
                <button id="add-habit-btn">添加习惯</button>
            </div>
            <h3>本月追踪记录</h3>
            <div class="habit-matrix-container">
                <div class="habit-matrix-legend">
                    <span class="legend-label">发生频率:</span>
                    <div class="legend-scale">
                        <div class="legend-item">
                            <div class="legend-color level-0"></div>
                            <span>无</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color level-1"></div>
                            <span>1-2次</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color level-2"></div>
                            <span>3-5次</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color level-3"></div>
                            <span>6-10次</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color level-4"></div>
                            <span>10+次</span>
                        </div>
                    </div>
                </div>
                <div id="habit-tracker-grid-container"></div>
                <div class="habit-matrix-footer">
                    <div class="matrix-stats">
                        <div class="stat-card">
                            <span class="stat-number" id="matrix-total-days">0</span>
                            <span class="stat-label">有记录天数</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="matrix-worst-day">--</span>
                            <span class="stat-label">最严重日期</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="matrix-best-streak">0</span>
                            <span class="stat-label">最长无习惯天数</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 习惯记录弹窗 -->
        <div class="habit-modal" id="habit-modal">
            <div class="habit-modal-content">
                <div class="habit-modal-header">
                    <div class="habit-modal-title" id="habit-modal-title">习惯记录</div>
                    <button class="habit-modal-close" onclick="closeHabitModal()">&times;</button>
                </div>

                <div class="habit-summary" id="habit-summary">
                    <div class="habit-summary-text">今日总计: 0次</div>
                </div>

                <div class="habit-records-list" id="habit-records-list">
                    <!-- 记录列表将通过JavaScript动态生成 -->
                </div>

                <div class="habit-add-form">
                    <div class="habit-add-title">添加新记录</div>
                    <div class="habit-form-row">
                        <div class="habit-form-group">
                            <label class="habit-form-label">发生时间</label>
                            <input type="time" class="habit-form-input" id="habit-time-input">
                        </div>
                        <div class="habit-form-group">
                            <label class="habit-form-label">持续时长(分钟)</label>
                            <input type="number" class="habit-form-input" id="habit-duration-input" placeholder="例如: 30" min="1">
                        </div>
                    </div>
                    <div class="habit-form-row">
                        <div class="habit-form-group">
                            <label class="habit-form-label">备注(可选)</label>
                            <input type="text" class="habit-form-input" id="habit-note-input" placeholder="例如: 看抖音">
                        </div>
                        <button class="habit-add-btn" onclick="addHabitRecord()">添加记录</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Meditation Content Pane -->
        <div id="meditation-content" class="tab-content">
            
            <h2>每日冥想</h2>
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.75 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.75 0h.008v.015h-.008V9.75z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">总冥想天数</span>
                        <span class="value" id="meditation-total-days">0天</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">总冥想时长</span>
                        <span class="value" id="meditation-total-time">0 分钟</span>
                    </div>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th style="width:25%">天数</th>
                        <th style="width:25%">完成</th>
                        <th style="width:25%">时长(分钟)</th>
                        <th style="width:25%">进行时间</th>
                    </tr>
                </thead>
                <tbody id="meditation-table-body"></tbody>
            </table>
        </div>
        
        <!-- Reading Content Pane -->
        <div id="reading-content" class="tab-content">
            
            <h2>阅读目标与进度</h2>
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">读书目标</span>
                        <span class="value">
                            <span id="books-read-count">0</span> / 
                            <input type="number" id="book-goal" class="data-input" value="10" style="width: 50px; text-align: center; display: inline-block; vertical-align: baseline;">
                        </span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">总阅读时长</span>
                        <span class="value" id="reading-total-time">0 分钟</span>
                    </div>
                </div>
            </div>
            <div class="progress-container"><div class="progress-bar" id="readingProgressBar"></div></div>
            <h3>我的书架</h3>
            <div id="book-adder">
                <input type="text" id="new-book-title" placeholder="书籍名称">
                <input type="number" id="new-book-pages" placeholder="总页数">
                <button id="add-book-btn">添加书籍</button>
            </div>
            <div id="book-list"></div>
            <h3>每日阅读记录</h3>
            <table>
                <thead>
                    <tr>
                        <th>天数</th>
                        <th style="width:35%">阅读书籍</th>
                        <th>阅读页数</th>
                        <th>时长(分钟)</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody id="reading-log-body"></tbody>
            </table>

            <!-- 美观的书架抽屉 -->
            <div class="bookshelf-drawer">
                <div class="bookshelf-header" onclick="toggleBookshelf()">
                    <div class="bookshelf-title">
                        <svg class="bookshelf-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0012 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75z" />
                        </svg>
                        <span>我的书架</span>
                        <span class="book-count" id="bookshelf-count">(0本)</span>
                    </div>
                    <div class="bookshelf-toggle">
                        <svg class="toggle-arrow" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
                        </svg>
                    </div>
                </div>
                <div class="bookshelf-content" id="bookshelf-content">
                    <div class="bookshelf-shelves">
                        <div class="bookshelf-shelf">
                            <div class="shelf-wood"></div>
                            <div class="books-container" id="books-shelf-1">
                                <!-- 书籍将通过JavaScript动态添加 -->
                            </div>
                        </div>
                        <div class="bookshelf-shelf">
                            <div class="shelf-wood"></div>
                            <div class="books-container" id="books-shelf-2">
                                <!-- 书籍将通过JavaScript动态添加 -->
                            </div>
                        </div>
                        <div class="bookshelf-shelf">
                            <div class="shelf-wood"></div>
                            <div class="books-container" id="books-shelf-3">
                                <!-- 书籍将通过JavaScript动态添加 -->
                            </div>
                        </div>
                    </div>
                    <div class="bookshelf-footer">
                        <div class="bookshelf-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="total-books">0</span>
                                <span class="stat-label">总书籍</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="completed-books">0</span>
                                <span class="stat-label">已完成</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="reading-books">0</span>
                                <span class="stat-label">阅读中</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="total-pages-read">0</span>
                                <span class="stat-label">总页数</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Speech Content Pane -->
        <div id="speech-content" class="tab-content">
            
            <h2>训练数据统计 (Week <span id="speech-week-n">1</span>)</h2>
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0h18M-4.5 12h22.5" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本周</span>
                        <span class="value success" id="speech-stat-week">0次 / 0分钟</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0h18" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本月</span>
                        <span class="value" id="speech-stat-month">0次 / 0分钟</span>
                    </div>
                </div>
            </div>
            <div id="speech-weekly-tracker"></div>
        </div>
        
        <!-- Analysis Content Pane -->
        <div id="analysis-content" class="tab-content">
            <h2>今日24小时时间分配 (Day <span id="today-is-day-n">1</span>)</h2>
            <div id="today-chart-spotlight">
                <div id="spotlight-placeholder">请先在"生活与复盘"页面设置"挑战开始日期"</div>
                <div id="spotlight-canvas-container" style="display: none;">
                    <canvas id="spotlight-canvas"></canvas>
                </div>
            </div>

            <!-- 本周智能小结卡片 -->
            <div class="summary-box" id="weekly-analysis-container" style="grid-template-columns: 1fr; margin-top: 2rem;">
                <div class="summary-item" style="flex-direction: column; align-items: flex-start; padding: 2rem;">
                    <div class="summary-content" style="width: 100%;">
                        <span class="label" style="font-size: 1.3em; font-weight: 600; color: var(--text-primary); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.75rem;">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width: 28px; height: 28px; color: var(--primary-accent);">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                            </svg>
                            本周智能小结
                        </span>
                        <div id="weekly-analysis-content">
                            <p>正在分析您的本周数据...</p>
                        </div>
                    </div>
                </div>
            </div>

            <h2>时间总览 (分钟)</h2>
            <table>
                <thead>
                    <tr>
                        <th style="width:8%">天数</th>
                        <th>睡眠</th>
                        <th>Asylum</th>
                        <th>英语</th>
                        <th>习惯</th>
                        <th>冥想</th>
                        <th>阅读</th>
                        <th>演讲</th>
                        <th style="color:var(--primary-accent)">已记录总计</th>
                        <th style="color:var(--text-secondary)">其他时间</th>
                        <th style="width:20%">24小时分配</th>
                    </tr>
                </thead>
                <tbody id="analysis-table-body"></tbody>
            </table>
        </div>
        
        <!-- Finance Content Pane -->
        <div id="finance-content" class="tab-content">
            <h2>记账与财务分析</h2>
            
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本月收入</span>
                        <span class="value success" id="finance-income">¥0</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon failure">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本月支出</span>
                        <span class="value failure" id="finance-expense">¥0</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">结余</span>
                        <span class="value" id="finance-balance">¥0</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">预算使用率</span>
                        <span class="value" id="finance-budget-usage">0%</span>
                    </div>
                </div>
            </div>
            
            <div class="finance-controls">
                <div class="finance-add-transaction">
                    <h3>添加交易记录</h3>
                    <div class="finance-form">
                        <div class="form-group">
                            <label for="transaction-date">日期</label>
                            <input type="date" id="transaction-date" class="data-input finance-input">
                        </div>
                        <div class="form-group">
                            <label for="transaction-type">类型</label>
                            <select id="transaction-type" class="data-input finance-input">
                                <option value="expense">支出</option>
                                <option value="income">收入</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="transaction-category">分类</label>
                            <select id="transaction-category" class="data-input finance-input">
                                <option value="">选择分类</option>
                                <optgroup label="支出">
                                    <option value="food">餐饮</option>
                                    <option value="shopping">购物</option>
                                    <option value="transportation">交通</option>
                                    <option value="entertainment">娱乐</option>
                                    <option value="housing">住房</option>
                                    <option value="utilities">水电费</option>
                                    <option value="health">医疗健康</option>
                                    <option value="education">教育</option>
                                    <option value="other-expense">其他支出</option>
                                </optgroup>
                                <optgroup label="收入">
                                    <option value="salary">工资</option>
                                    <option value="bonus">奖金</option>
                                    <option value="investment">投资收益</option>
                                    <option value="gift">礼金</option>
                                    <option value="other-income">其他收入</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="transaction-amount">金额</label>
                            <input type="number" id="transaction-amount" class="data-input finance-input" step="0.01" placeholder="¥0.00">
                        </div>
                        <div class="form-group">
                            <label for="transaction-note">备注</label>
                            <input type="text" id="transaction-note" class="data-input finance-input" placeholder="可选备注">
                        </div>
                        <button id="add-transaction-btn" class="finance-btn">添加记录</button>
                    </div>
                </div>
                
                <div class="finance-budget-settings">
                    <h3>预算设置</h3>
                    <div class="finance-form">
                        <div class="form-group">
                            <label for="monthly-budget">月度预算</label>
                            <input type="number" id="monthly-budget" class="data-input finance-input" step="100" placeholder="¥0">
                        </div>
                        <button id="save-budget-btn" class="finance-btn">保存预算</button>
                    </div>
                </div>
            </div>
            
            <div class="finance-charts">
                <div class="chart-container finance-chart-container">
                    <h3>💰 支出分类占比</h3>
                    <div class="finance-chart-wrapper">
                        <canvas id="expense-category-chart"></canvas>
                    </div>
                </div>

                <div class="chart-container finance-chart-container">
                    <h3>📊 月度收支对比</h3>
                    <div class="finance-chart-wrapper">
                        <canvas id="monthly-comparison-chart"></canvas>
                    </div>
                </div>

                <div class="chart-container finance-chart-container">
                    <h3>📈 每日收支趋势</h3>
                    <div class="finance-chart-wrapper">
                        <canvas id="daily-trend-chart"></canvas>
                    </div>
                </div>

                <div class="chart-container finance-chart-container">
                    <h3>🏷️ 分类支出趋势</h3>
                    <div class="finance-chart-wrapper">
                        <canvas id="category-trend-chart"></canvas>
                    </div>
                </div>
            </div>
            
            <h3>交易记录</h3>
            <div class="finance-filters">
                <select id="filter-transaction-type" class="data-input finance-filter">
                    <option value="all">全部交易</option>
                    <option value="expense">仅支出</option>
                    <option value="income">仅收入</option>
                </select>
                <select id="filter-transaction-category" class="data-input finance-filter">
                    <option value="all">全部分类</option>
                </select>
                <button id="clear-filters-btn" class="finance-btn finance-btn-small">清除筛选</button>
            </div>
            <div class="finance-transactions-container">
                <table class="finance-table">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>类型</th>
                            <th>分类</th>
                            <th>金额</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="transactions-table-body">
                        <!-- 交易记录将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Guitar Content Pane -->
        <div id="guitar-content" class="tab-content">
            <h2>吉他练习记录</h2>
            
            <div class="summary-box">
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">本周练习时长</span>
                        <span class="value" id="guitar-weekly-time">0分钟</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">连续练习天数</span>
                        <span class="value" id="guitar-streak-days">0天</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 9l10.5-3m0 6.553v3.75a2.25 2.25 0 01-1.632 2.163l-1.32.377a1.803 1.803 0 11-.99-3.467l2.31.66a2.25 2.25 0 001.632-4.2l-5.25-1.5" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">掌握曲目数</span>
                        <span class="value" id="guitar-mastered-songs">0首</span>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 01-.982-3.172M9.497 14.25a7.454 7.454 0 00.981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 007.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 002.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 012.916.52 6.003 6.003 0 01-5.395 4.972m0 0a6.726 6.726 0 01-2.749 1.35m0 0a6.772 6.772 0 01-3.044 0" />
                        </svg>
                    </div>
                    <div class="summary-content">
                        <span class="label">当前等级</span>
                        <span class="value" id="guitar-current-level">新手</span>
                    </div>
                </div>
            </div>
            
            <div class="guitar-main-content">
                <div class="guitar-practice-section">
                    <h3>记录练习</h3>
                    <div class="guitar-form">
                        <div class="form-group">
                            <label for="practice-date">日期</label>
                            <input type="date" id="practice-date" class="data-input guitar-input">
                        </div>
                        <div class="form-group">
                            <label for="practice-duration">练习时长 (分钟)</label>
                            <input type="number" id="practice-duration" class="data-input guitar-input" min="1" step="1">
                        </div>
                        <div class="form-group">
                            <label for="practice-type">练习类型</label>
                            <select id="practice-type" class="data-input guitar-input">
                                <option value="scales">音阶练习</option>
                                <option value="chords">和弦练习</option>
                                <option value="songs">曲目练习</option>
                                <option value="technique">技巧练习</option>
                                <option value="ear-training">听力训练</option>
                                <option value="theory">乐理学习</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="practice-focus">重点内容</label>
                            <input type="text" id="practice-focus" class="data-input guitar-input" placeholder="例如：G大调音阶、切换F和C和弦、《小星星》第二段...">
                        </div>
                        <div class="form-group">
                            <label for="practice-notes">笔记</label>
                            <textarea id="practice-notes" class="data-input guitar-input" rows="3" placeholder="记录练习感受、遇到的问题或突破..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="practice-rating">自评 (1-5星)</label>
                            <div class="rating-input">
                                <span class="rating-star" data-value="1">★</span>
                                <span class="rating-star" data-value="2">★</span>
                                <span class="rating-star" data-value="3">★</span>
                                <span class="rating-star" data-value="4">★</span>
                                <span class="rating-star" data-value="5">★</span>
                                <input type="hidden" id="practice-rating" value="0">
                            </div>
                        </div>
                        <button id="add-practice-btn" class="guitar-btn">保存练习记录</button>
                    </div>
                </div>
                
                <div class="guitar-repertoire-section">
                    <h3>曲目管理</h3>
                    <div class="guitar-form">
                        <div class="form-group">
                            <label for="song-title">曲目名称</label>
                            <input type="text" id="song-title" class="data-input guitar-input">
                        </div>
                        <div class="form-group">
                            <label for="song-artist">艺术家</label>
                            <input type="text" id="song-artist" class="data-input guitar-input">
                        </div>
                        <div class="form-group">
                            <label for="song-difficulty">难度</label>
                            <select id="song-difficulty" class="data-input guitar-input">
                                <option value="1">简单 (1星)</option>
                                <option value="2">初级 (2星)</option>
                                <option value="3">中级 (3星)</option>
                                <option value="4">进阶 (4星)</option>
                                <option value="5">高级 (5星)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="song-status">状态</label>
                            <select id="song-status" class="data-input guitar-input">
                                <option value="learning">学习中</option>
                                <option value="practicing">练习中</option>
                                <option value="polishing">完善中</option>
                                <option value="mastered">已掌握</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="song-notes">备注</label>
                            <input type="text" id="song-notes" class="data-input guitar-input" placeholder="例如：谱子链接、视频教程URL等">
                        </div>
                        <button id="add-song-btn" class="guitar-btn">添加曲目</button>
                    </div>
                    
                    <div class="song-list-container">
                        <table class="guitar-table">
                            <thead>
                                <tr>
                                    <th>曲目</th>
                                    <th>艺术家</th>
                                    <th>难度</th>
                                    <th>状态</th>
                                    <th>最后练习</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="song-list-body">
                                <!-- 曲目列表将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="guitar-charts">
                <div class="chart-container guitar-chart-container">
                    <h3>练习时间分布</h3>
                    <div class="guitar-chart-wrapper">
                        <canvas id="practice-type-chart"></canvas>
                    </div>
                </div>
                
                <div class="chart-container guitar-chart-container">
                    <h3>每周练习时长</h3>
                    <div class="guitar-chart-wrapper">
                        <canvas id="weekly-practice-chart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="guitar-practice-log">
                <h3>练习日志</h3>
                <div class="practice-log-container">
                    <table class="guitar-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>时长</th>
                                <th>类型</th>
                                <th>重点内容</th>
                                <th>自评</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="practice-log-body">
                            <!-- 练习记录将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Insights Content Pane (Revised) -->
        <div id="insights-content" class="tab-content">
            <h2>自动数据洞察报告</h2>
            <p class="instructions">应用已自动分析您过去30天的数据，并发现了以下有趣的关联。这些洞察将随着您的数据积累而变得更加精准。</p>

            <div id="insights-report-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; margin-top: 2rem;">
                <!-- 洞察卡片将通过JavaScript动态生成于此 -->
                <div class="loading-overlay" id="insights-loading" style="position: static; grid-column: 1 / -1; text-align: center; padding: 3rem; background: transparent;">
                    <div class="loading-spinner" style="margin: auto;"></div>
                    <p style="margin-top: 1rem;">正在挖掘您的数据宝藏...</p>
                </div>
            </div>
        </div>
    </div>

<div id="data-modal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-title">数据详情</h3>
            <button class="modal-close" onclick="closeDataModal()">×</button>
        </div>
        <div class="modal-body">
            <div id="modal-chart-container">
                <canvas id="modal-chart"></canvas>
            </div>
            <div id="modal-table-container">
                <!-- 动态生成的表格内容 -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mainContainer = document.querySelector('.container');
    
    // 添加页面加载动画
    setTimeout(() => {
        document.body.classList.add('loaded');
        initTechEffects();
    }, 300);

    const asylumWorkoutDetails = {
        // Insanity Asylum训练
        'Speed & Agility': { description: '速度与敏捷性训练，提升运动表现和反应能力', equipment: '敏捷梯，标志锥', focus: '速度，敏捷性，协调性' },
        'Strength': { description: '功能性力量训练，结合体重和阻力训练', equipment: '哑铃，阻力带', focus: '全身力量，肌肉耐力' },
        'Relief': { description: '恢复性训练，拉伸和放松肌肉', equipment: '瑜伽垫', focus: '肌肉恢复，柔韧性' },
        'Game Day': { description: '综合运动挑战，模拟比赛强度', equipment: '多种器械', focus: '综合体能，竞技表现' },
        // P90X肌肉训练 (优先填充，适合大体重)
        '胸部+背部+腹肌撕裂者X': { description: '胸部和背部肌肉群训练，配合核心腹肌强化', equipment: '哑铃或阻力带，引体向上杆', focus: '上身力量，核心稳定' },
        '肩膀+手臂+腹肌撕裂者X': { description: '肩部和手臂肌肉训练，配合腹肌强化', equipment: '哑铃或阻力带', focus: '上肢力量，肩部稳定' },
        '腿部+背部+腹肌撕裂者X': { description: '下肢和背部肌肉训练，配合核心强化', equipment: '哑铃或阻力带，引体向上杆', focus: '下肢力量，背部肌群' },
        '胸部+肩膀+三头+腹肌撕裂者X': { description: '胸部、肩部和三头肌训练，配合腹肌强化', equipment: '哑铃或阻力带', focus: '推举肌群，核心力量' },
        '背部+二头+腹肌撕裂者X': { description: '背部和二头肌训练，配合腹肌强化', equipment: '哑铃或阻力带，引体向上杆', focus: '拉力肌群，核心力量' },
        // Insanity经典训练 (填补空隙)
        'Pure Cardio': { description: '纯有氧高强度训练，无跳跃动作版本，专注心肺功能提升', equipment: '无需器械', focus: '心肺耐力，脂肪燃烧' },
        'Cardio Power & Resistance': { description: '有氧与阻力结合训练，体重训练为主，适合大体重人群', equipment: '无需器械', focus: '心肺耐力，肌肉耐力' },
        'Max Interval Circuit': { description: '最大间歇循环训练，高低强度交替，燃脂效果极佳', equipment: '无需器械', focus: '间歇训练，代谢提升' }
    };

    function initTechEffects() {
        document.querySelectorAll('.summary-item, .chart-container, .overview-card').forEach(el => {
            el.addEventListener('mouseenter', function() { this.style.transform = 'translateY(-3px)'; });
            el.addEventListener('mouseleave', function() { this.style.transform = ''; });
        });
        addAsylumTooltips();
    }

    function addAsylumTooltips() {
        setTimeout(() => {
            document.querySelectorAll('#asylum-table-body td:nth-child(2) div').forEach(cell => {
                const workoutName = cell.textContent.trim();
                const details = asylumWorkoutDetails[workoutName];
                if (details) {
                    cell.style.cursor = 'help';
                    cell.title = `训练描述: ${details.description}\n所需器械: ${details.equipment}\n训练重点: ${details.focus}`;
                }
            });
        }, 1500);
    }
    
    // --- UTILITY & TIME HELPERS ---
    const getEl = id => document.getElementById(id);
    const getVal = id => getEl(id)?.value || '';
    const getNum = id => parseFloat(getVal(id)) || 0;
    const getChecked = id => getEl(id)?.checked || false;
    const getDayIndexFromId = (id) => (id.match(/-d(\d+)/) || id.match(/-day(\d+)/) || [])[1];

    function getTodayDateString() {
        return new Date().toISOString().split('T')[0];
    }
    
    function getDateStringFromDaysAgo(daysAgo) {
        const date = new Date();
        date.setDate(date.getDate() - daysAgo);
        return date.toISOString().split('T')[0];
    }

    function getDateForDay(dayIndex, startDateStr) {
        if (!startDateStr || !dayIndex) return null;
        const startDate = new Date(startDateStr);
        startDate.setDate(startDate.getDate() + dayIndex - 1);
        return startDate;
    }

    function getDayIndexFromDate(dateStr, startDateStr) {
        if (!startDateStr || !dateStr) return null;
        const startDate = new Date(startDateStr);
        const targetDate = new Date(dateStr);
        const diffTime = targetDate - startDate;
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        return diffDays + 1;
    }

    function getPlanId() { 
        const startDateStr = getVal('challenge-start-date'); 
        if (!startDateStr) return null; 
        const date = new Date(startDateStr); 
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`; 
    }
    
    // --- UI GENERATION ---
    const generateWeeklyGroupedRows = (containerId, days, colspan, rowGenerator) => { 
        const container = getEl(containerId); 
        if (!container) return; 
        let html = ''; 
        let dayCounter = 1; 
        for (let week = 1; week <= Math.ceil(days / 7); week++) { 
            html += `<tr class="week-header"><td colspan="${colspan}">Week ${week}</td></tr>`; 
            for (let i = 0; i < 7; i++) { 
                if (dayCounter > days) break; 
                html += rowGenerator(dayCounter); 
                dayCounter++; 
            } 
        } 
        container.innerHTML = html; 
    };

    function generateAsylumTable() {
        const container = getEl('asylum-table-body');
        if (!container) return;
        let html = '';
        let dayCounter = 1;
        for (const weekData of asylumSchedule) {
            html += `<tr class="week-header"><td colspan="6"><strong>Week ${weekData.week}</strong><span style="margin-left: 10px; color: var(--tertiary-accent); font-size: 0.9em;">${weekData.phase}</span></td></tr>`;
            for (const workout of weekData.workouts) {
                if (dayCounter > ASYLUM_DAYS) break;
                const workoutInfo = asylumWorkouts[workout];
                html += `<tr id="asylum-row-day${dayCounter}">
                    <td>Day ${dayCounter}<span class="date-display" id="asylum-date-day${dayCounter}"></span></td>
                    <td><div>${workout}</div><small style="color: var(--text-secondary);">${workoutInfo.duration}分钟 | ${workoutInfo.calories}卡路里</small></td>
                    <td><input type="number" id="asylum-time-day${dayCounter}" class="data-input time-source" placeholder="${workoutInfo.duration}"></td>
                    <td><input type="datetime-local" id="asylum-donetime-day${dayCounter}" class="data-input"></td>
                    <td><input type="number" id="asylum-kcal-day${dayCounter}" class="data-input calorie-source" placeholder="${workoutInfo.calories}"></td>
                    <td><input type="checkbox" id="asylum-check-day${dayCounter}" class="data-input asylum-source"></td>
                </tr>`;
                dayCounter++;
            }
        }
        container.innerHTML = html;
    }
    function generateEnglishTable() { generateWeeklyGroupedRows('english-table-body', MONTHLY_DAYS, 6, day => `<tr id="eng-row-day${day}"><td>Day ${day}<span class="date-display" id="eng-date-day${day}"></span></td><td><input type="number" id="eng-new-day${day}" class="data-input eng-source"></td><td><input type="number" id="eng-time-day${day}" class="data-input time-source eng-source"></td><td><input type="datetime-local" id="eng-donetime-day${day}" class="data-input"></td><td><input type="text" id="eng-content-day${day}" class="data-input"></td><td><input type="text" id="eng-notes-day${day}" class="data-input"></td></tr>`); }
    function generateMeditationTable() { generateWeeklyGroupedRows('meditation-table-body', MONTHLY_DAYS, 4, day => `<tr id="med-row-day${day}"><td>Day ${day}<span class="date-display" id="med-date-day${day}"></span></td><td><input type="checkbox" id="med-check-day${day}" class="data-input med-source"></td><td><input type="number" id="med-time-day${day}" class="data-input time-source med-source"></td><td><input type="datetime-local" id="med-donetime-day${day}" class="data-input"></td></tr>`); }
    function generateReadingTable() { generateWeeklyGroupedRows('reading-log-body', MONTHLY_DAYS, 5, day => `<tr id="read-row-day${day}"><td>Day ${day}<span class="date-display" id="read-date-day${day}"></span></td><td><select id="read-book-day${day}" class="data-input read-log-source"></select></td><td><input type="number" id="read-pages-day${day}" class="data-input read-log-source"></td><td><input type="number" id="read-time-day${day}" class="data-input time-source read-log-source"></td><td><input type="text" id="read-notes-day${day}" class="data-input read-log-source"></td></tr>`); }
    function generateAnalysisTable() {
        generateWeeklyGroupedRows('analysis-table-body', MONTHLY_DAYS, 11, day =>
            `<tr id="analysis-row-day${day}">
                <td>Day ${day}<span class="date-display" id="analysis-date-day${day}"></span></td>
                <td><span id="analysis-睡眠-time-day${day}">0</span><small class="comparison" id="analysis-睡眠-comp-day${day}"></small></td>
                <td><span id="analysis-Asylum-time-day${day}">0</span><small class="comparison" id="analysis-Asylum-comp-day${day}"></small></td>
                <td><span id="analysis-英语-time-day${day}">0</span><small class="comparison" id="analysis-英语-comp-day${day}"></small></td>
                <td><span id="analysis-习惯-time-day${day}">0</span><small class="comparison" id="analysis-习惯-comp-day${day}"></small></td>
                <td><span id="analysis-冥想-time-day${day}">0</span><small class="comparison" id="analysis-冥想-comp-day${day}"></small></td>
                <td><span id="analysis-阅读-time-day${day}">0</span><small class="comparison" id="analysis-阅读-comp-day${day}"></small></td>
                <td><span id="analysis-演讲-time-day${day}">0</span><small class="comparison" id="analysis-演讲-comp-day${day}"></small></td>
                <td><strong id="analysis-tracked-total-day${day}">0</strong><small class="comparison" id="analysis-tracked-total-comp-day${day}"></small></td>
                <td><span id="analysis-untracked-time-day${day}">1440</span></td>
                <td><div class="pie-chart-container" style="height:60px;"><canvas id="pie-chart-day${day}"></canvas></div></td>
            </tr>`
        );
    }
    
    function renderHabitTracker() {
        const container = getEl('habit-tracker-grid-container');
        const planId = getPlanId();
        if (!planId) {
            container.innerHTML = '<p style="text-align:center; color: var(--text-secondary);">请先设置挑战开始日期以管理习惯</p>';
            return;
        }

        if (habitList.length === 0) {
            container.innerHTML = '<p style="text-align:center; color: var(--text-secondary);">暂无不良习惯记录，请先添加习惯</p>';
            return;
        }

        console.log('渲染习惯矩阵热力图(周视图)...');

        // 计算要显示的周数据 - 基于挑战开始日期
        const challengeStartStr = getVal('challenge-start-date');
        if (!challengeStartStr) {
            container.innerHTML = '<p style="text-align:center; color: var(--text-secondary);">请先设置挑战开始日期</p>';
            return;
        }

        const challengeStart = new Date(challengeStartStr);
        const today = new Date();
        const weeks = [];

        // 从挑战开始日期开始，显示连续的周
        for (let weekOffset = 0; weekOffset < WEEKS_TO_SHOW; weekOffset++) {
            const weekStart = new Date(challengeStart);
            // 找到挑战开始日期所在周的周一
            const dayOfWeek = challengeStart.getDay();
            const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 周日是0，需要特殊处理
            weekStart.setDate(challengeStart.getDate() - daysToMonday + (weekOffset * 7));

            const weekDays = [];
            for (let dayOffset = 0; dayOffset < DAYS_PER_WEEK; dayOffset++) {
                const date = new Date(weekStart);
                date.setDate(weekStart.getDate() + dayOffset);
                weekDays.push({
                    date: date,
                    dateStr: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
                    dayName: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][dayOffset],
                    isWeekend: dayOffset >= 5, // 周六周日
                    dateKey: date.toISOString().split('T')[0], // YYYY-MM-DD格式
                    isToday: date.toDateString() === today.toDateString()
                });
            }

            weeks.push({
                weekStart: weekStart,
                weekLabel: `${weekStart.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - ${weekDays[6].dateStr}`,
                days: weekDays
            });
        }

        // Generate table-style header
        let matrixHTML = `
            <div class="habit-matrix-table">
                <div class="habit-matrix-header">
                    <div class="habit-name-header">习惯名称</div>
        `;

        // Generate weekday headers
        const weekdayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        weekdayNames.forEach((dayName, index) => {
            const isWeekend = index >= 5; // 周六周日
            matrixHTML += `<div class="date-header ${isWeekend ? 'weekend' : ''}">${dayName}</div>`;
        });

        // Add date ranges column header
        matrixHTML += `<div class="date-header">日期范围</div>`;
        matrixHTML += `</div>`;

        habitList.forEach(habit => {
            // Generate weeks for this habit
            weeks.forEach((week, weekIndex) => {
                matrixHTML += `<div class="habit-data-row" data-habit-id="${habit.id}">`;

                // First column: habit name (only show on first week)
                if (weekIndex === 0) {
                    matrixHTML += `<div class="habit-name-cell">`;
                    matrixHTML += `<svg class="habit-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">`;
                    matrixHTML += `<path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />`;
                    matrixHTML += `</svg>`;
                    matrixHTML += `<span>${habit.name}</span>`;
                    matrixHTML += `<button class="habit-delete-btn" data-habit-id="${habit.id}" style="margin-left: auto; padding: 2px 6px; font-size: 10px; background: #ff6b6b; color: white; border: none; border-radius: 3px; cursor: pointer;">删除</button>`;
                    matrixHTML += `</div>`;
                } else {
                    matrixHTML += `<div class="habit-name-cell empty"></div>`;
                }

                // Generate day cells for this week
                week.days.forEach(dayInfo => {
                    // 使用日期作为key来存储数据
                    const recordsKey = `${planId}-habit-h${habit.id}-date-${dayInfo.dateKey}-records`;
                    const records = JSON.parse(localStorage.getItem(recordsKey) || '[]');

                    let count, time;
                    if (records.length > 0) {
                        count = records.length;
                        time = records.reduce((sum, record) => sum + (record.duration || 0), 0);
                    } else {
                        count = 0;
                        time = 0;
                    }

                    // Determine level based on count
                    let level = 0;
                    if (count > 0) {
                        if (count <= 2) level = 1;
                        else if (count <= 5) level = 2;
                        else if (count <= 10) level = 3;
                        else level = 4;
                    }

                    const cellClasses = [
                        'habit-day-cell',
                        `level-${level}`,
                        dayInfo.isWeekend ? 'weekend' : '',
                        dayInfo.isToday ? 'today' : ''
                    ].filter(Boolean).join(' ');

                    const displayDate = `${dayInfo.dateStr} (${dayInfo.dayName})`;
                    const displayText = count > 0 ? count : '';

                    matrixHTML += `
                        <div class="${cellClasses}"
                             data-habit-id="${habit.id}"
                             data-date="${dayInfo.dateKey}"
                             data-count="${count}"
                             data-time="${time}"
                             title="${habit.name} - ${displayDate}\n次数: ${count}\n时长: ${time}分钟"
                             onclick="editHabitDay('${habit.id}', '${dayInfo.dateKey}', '${displayDate}')">
                            ${displayText}
                        </div>
                    `;
                });

                // Add week label at the end
                matrixHTML += `<div class="week-label-cell">${week.weekLabel}</div>`;
                matrixHTML += `</div>`;
            });
        });

        matrixHTML += '</div>';

        // Add tooltip element
        matrixHTML += '<div class="habit-tooltip" id="habit-tooltip"></div>';

        container.innerHTML = matrixHTML;

        // Update matrix stats
        updateHabitMatrixStats();

        // Add event listeners for delete buttons
        container.querySelectorAll('.habit-delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const habitId = parseInt(btn.dataset.habitId);
                deleteHabit(habitId);
            });
        });

        // Add hover effects for tooltips
        addHabitTooltipEvents();
    }

    function generateSpeechUI(weekIndex) {
        const container = getEl('speech-weekly-tracker'); 
        if (!container || !weekIndex) return; 
        let dayItemsWarmup = '', dayItemsStructure = ''; 
        for (let i = 1; i <= 7; i++) { 
            const dayOfWeek = WEEKDAYS_ZH[i === 7 ? 0 : i]; 
            const warmupIdPrefix = `speech-warmup-w${weekIndex}-d${i}`, 
                  structureIdPrefix = `speech-structure-w${weekIndex}-d${i}`; 
            dayItemsWarmup += `<div class="speech-day-item" id="speech-item-w${weekIndex}-d${i}-warmup"><div><input type="checkbox" id="${warmupIdPrefix}-check" class="data-input speech-source"><label for="${warmupIdPrefix}-check">${dayOfWeek}</label></div><div></div><div><input type="number" id="${warmupIdPrefix}-time" class="data-input time-source speech-source" placeholder="用时( ?"></div></div>`; 
            dayItemsStructure += `<div class="speech-day-item" id="speech-item-w${weekIndex}-d${i}-structure"><div><input type="checkbox" id="${structureIdPrefix}-check" class="data-input speech-source"><label for="${structureIdPrefix}-check">${dayOfWeek}</label></div><div><input type="text" id="${structureIdPrefix}-theme" class="data-input speech-source" placeholder="主题"></div><div><input type="number" id="${structureIdPrefix}-time" class="data-input time-source speech-source" placeholder="用时( ?"></div></div>`; 
        } 
        container.innerHTML = `<div class="speech-challenge-box"><h3>声音热身</h3><div class="speech-day-container">${dayItemsWarmup}</div></div><div class="speech-challenge-box"><h3>结构化表 ?/h3><div class="speech-day-container">${dayItemsStructure}</div></div>`;
    }

    // --- DATE, HIGHLIGHTING & DYNAMIC UI ---
    let currentViewDate = getTodayDateString(); // State for the currently viewed date

    // --- INSIGHTS ANALYSIS ---
    let insightChart = null; // 为图表实例声明全局变量

    const INSIGHTS_METRICS = {
        // --- 生活数据 ---
        'sleep_duration': { label: '睡眠时长 (小时)', getter: (planId, day) => {
            const start = localStorage.getItem(`${planId}-life-sleep-start-day${day}`);
            const end = localStorage.getItem(`${planId}-life-sleep-end-day${day}`);
            const duration = calculateSleepDurationFromTimes(start, end);
            return duration > 0 ? (duration / 60).toFixed(1) : null;
        }},
        'mood_rating': { label: '心情评分 (1-10)', getter: (planId, day) => localStorage.getItem(`${planId}-mood-rating-day${day}`) },
        'next_day_mood_rating': { label: '次日心情评分', getter: (planId, day) => localStorage.getItem(`${planId}-mood-rating-day${day + 1}`) },
        'body_weight': { label: '体重 (kg)', getter: (planId, day) => localStorage.getItem(`${planId}-life-weight-day${day}`) },
        'calorie_intake': { label: '卡路里摄入 (kcal)', getter: (planId, day) => localStorage.getItem(`${planId}-life-total-intake-day${day}`) },
        'calorie_deficit': { label: '热量差 (kcal)', getter: (planId, day) => localStorage.getItem(`${planId}-life-deficit-day${day}`) },

        // --- 训练与学习 ---
        'asylum_time': { label: 'Asylum时长 (分钟)', getter: (planId, day) => localStorage.getItem(`${planId}-asylum-time-day${day}`) },
        'english_time': { label: '英语学习时长 (分钟)', getter: (planId, day) => localStorage.getItem(`${planId}-eng-time-day${day}`) },
        'english_words': { label: '英语单词数 (个)', getter: (planId, day) => localStorage.getItem(`${planId}-eng-words-day${day}`) },
        'reading_time': { label: '阅读时长 (分钟)', getter: (planId, day) => localStorage.getItem(`${planId}-read-time-day${day}`) },
        'reading_pages': { label: '阅读页数 (页)', getter: (planId, day) => localStorage.getItem(`${planId}-read-pages-day${day}`) },
        'meditation_time': { label: '冥想时长 (分钟)', getter: (planId, day) => localStorage.getItem(`${planId}-med-time-day${day}`) },
        'speech_time': { label: '演讲练习时长 (分钟)', getter: (planId, day) => localStorage.getItem(`${planId}-speech-time-day${day}`) },

        // --- 习惯 (动态生成) ---
        // 我们将在填充函数中动态添加习惯指标
        'total_habit_time': { label: '总不良习惯时长', getter: (planId, day) => {
            return habitList.reduce((total, habit) => {
                return total + parseInt(localStorage.getItem(`${planId}-habit-h${habit.id}-d${day}-time`) || '0');
            }, 0);
        }}
    };

    function updateMainTitle() { 
        const startDateStr = getVal('challenge-start-date'); 
        let titleText = "成长计划"; 
        if (startDateStr) { 
            const date = new Date(startDateStr); 
            const year = date.getFullYear(); 
            const month = date.getMonth() + 1; 
            if(year && month) titleText = `成长计划 (${year}年${month}月)`;
        } 
        getEl('main-title').textContent = titleText; 
        getEl('page-title').textContent = titleText; 
    }

    function updateDynamicUI() {
        document.querySelectorAll('.today-highlight').forEach(el => el.classList.remove('today-highlight'));
        const startDateStr = getVal('challenge-start-date');
        updateMainTitle();
        if (!startDateStr) { 
            getEl('spotlight-placeholder').textContent = '请先在"生活与复盘"页面设置"挑战开始日期"'; 
            return; 
        }
        
        loadAllData(); // Load data for the current month
        
        const formatOptions = { month: '2-digit', day: '2-digit' };
        const updateDateSpans = (day, prefix) => { 
            const date = getDateForDay(day, startDateStr); 
            if (date) { 
                const el = getEl(`${prefix}-date-day${day}`); 
                if (el) el.textContent = `${date.toLocaleDateString('sv-SE', formatOptions).replace('-', '/')}`; 
            } 
        };
        
        for (let i = 1; i <= ASYLUM_DAYS; i++) updateDateSpans(i, 'asylum');
        for (let i = 1; i <= MONTHLY_DAYS; i++) ['eng', 'habit', 'med', 'read', 'analysis'].forEach(prefix => updateDateSpans(i, prefix));
        
        const currentDayIndex = getDayIndexFromDate(getTodayDateString(), startDateStr);
        const currentWeekIndex = currentDayIndex ? Math.ceil(currentDayIndex / 7) : null;
        
        getEl('speech-week-n').textContent = currentWeekIndex || '?';
        if (currentWeekIndex && !getEl(`speech-item-w${currentWeekIndex}-d1-warmup`)) {
            generateSpeechUI(currentWeekIndex);
        }

        if (!currentDayIndex || currentDayIndex < 1) {
            getEl('spotlight-placeholder').textContent = "今天不在挑战期内";
            getEl('spotlight-canvas-container').style.display = 'none';
        } else {
            getEl('today-is-day-n').textContent = currentDayIndex;
            document.querySelectorAll(`#asylum-row-day${currentDayIndex}, #eng-row-day${currentDayIndex}, #habit-header-day${currentDayIndex}, #med-row-day${currentDayIndex}, #read-row-day${currentDayIndex}, #analysis-row-day${currentDayIndex}`).forEach(el => el && el.classList.add('today-highlight'));
            if(currentWeekIndex) {
                 const dayInWeek = (currentDayIndex - 1) % 7 + 1;
                 document.querySelectorAll(`#speech-item-w${currentWeekIndex}-d${dayInWeek}-warmup, #speech-item-w${currentWeekIndex}-d${dayInWeek}-structure`).forEach(el => el.classList.add('today-highlight'));
            }
            drawSpotlightChart(currentDayIndex);
        }
        updateAllStats();
    }
    
    // --- FINANCE FUNCTIONS ---
    function loadFinanceData() {
        console.log('加载记账数据...');

        // Load budget
        const planId = getPlanId();
        const budget = localStorage.getItem(`${planId}-monthly-budget`) || '0';
        const budgetEl = getEl('monthly-budget');
        if (budgetEl) budgetEl.value = budget;

        // Set default date to today
        const dateInput = getEl('transaction-date');
        if (dateInput && !dateInput.value) {
            dateInput.value = getTodayDateString();
        }

        // Add event listener for add transaction button
        const addBtn = getEl('add-transaction-btn');
        if (addBtn && !addBtn.hasAttribute('data-listener-added')) {
            addBtn.addEventListener('click', addTransaction);
            addBtn.setAttribute('data-listener-added', 'true');
        }



        // Load and display transactions
        loadTransactions();
        updateFinanceStats();

        // Render charts after a short delay to ensure DOM is ready
        setTimeout(() => {
            renderFinanceCharts();
        }, 100);
    }

    function getAllTransactions() {
        const allKeys = Object.keys(localStorage);
        const transactionKeys = allKeys.filter(key => key.startsWith('finance-transaction-'));
        const transactions = [];

        transactionKeys.forEach(key => {
            try {
                const transaction = JSON.parse(localStorage.getItem(key));
                if (transaction) transactions.push(transaction);
            } catch (e) {
                console.error('解析交易记录失败:', key, e);
            }
        });

        return transactions;
    }

    function loadTransactions() {
        const tbody = getEl('transactions-table-body');
        if (!tbody) return;

        // Get all transaction keys
        const allKeys = Object.keys(localStorage);
        const transactionKeys = allKeys.filter(key => key.startsWith('finance-transaction-'));

        if (transactionKeys.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">暂无交易记录</td></tr>';
            return;
        }

        // Load transactions
        const transactions = [];
        transactionKeys.forEach(key => {
            try {
                const transaction = JSON.parse(localStorage.getItem(key));
                if (transaction) transactions.push(transaction);
            } catch (e) {
                console.error('解析交易记录失败:', key, e);
            }
        });

        // Sort by date (newest first)
        transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Generate table rows
        tbody.innerHTML = transactions.map(transaction => {
            const typeText = transaction.type === 'income' ? '收入' : '支出';
            const typeClass = transaction.type === 'income' ? 'success' : 'failure';
            const amountText = transaction.type === 'income' ? `+¥${transaction.amount}` : `-¥${transaction.amount}`;

            const categoryNames = {
                'food': '餐饮', 'shopping': '购物', 'transportation': '交通', 'entertainment': '娱乐',
                'housing': '住房', 'utilities': '水电费', 'health': '医疗健康', 'education': '教育',
                'income': '收入'
            };

            const categoryText = categoryNames[transaction.category] || transaction.category;

            return `
                <tr>
                    <td>${transaction.date}</td>
                    <td><span class="transaction-type ${typeClass}">${typeText}</span></td>
                    <td>${categoryText}</td>
                    <td class="${typeClass}">${amountText}</td>
                    <td>${transaction.note || '--'}</td>
                    <td><button onclick="deleteTransaction('${transaction.id}')" class="finance-btn-small finance-btn-danger">删除</button></td>
                </tr>
            `;
        }).join('');
    }

    function updateFinanceStats() {
        console.log('更新记账统计...');

        // Get all transactions
        const allKeys = Object.keys(localStorage);
        const transactionKeys = allKeys.filter(key => key.startsWith('finance-transaction-'));

        let totalIncome = 0;
        let totalExpense = 0;

        transactionKeys.forEach(key => {
            try {
                const transaction = JSON.parse(localStorage.getItem(key));
                if (transaction) {
                    if (transaction.type === 'income') {
                        totalIncome += transaction.amount;
                    } else {
                        totalExpense += transaction.amount;
                    }
                }
            } catch (e) {
                console.error('解析交易记录失败:', key, e);
            }
        });

        const balance = totalIncome - totalExpense;
        const planId = getPlanId();
        const budget = parseFloat(localStorage.getItem(`${planId}-monthly-budget`) || '0');
        const budgetUsage = budget > 0 ? ((totalExpense / budget) * 100).toFixed(1) : '0';

        // Update display
        const incomeEl = getEl('finance-income');
        if (incomeEl) incomeEl.textContent = `¥${totalIncome}`;

        const expenseEl = getEl('finance-expense');
        if (expenseEl) expenseEl.textContent = `¥${totalExpense}`;

        const balanceEl = getEl('finance-balance');
        if (balanceEl) {
            balanceEl.textContent = `¥${balance}`;
            balanceEl.className = balance >= 0 ? 'value success' : 'value failure';
        }

        const budgetUsageEl = getEl('finance-budget-usage');
        if (budgetUsageEl) {
            budgetUsageEl.textContent = `${budgetUsage}%`;
            budgetUsageEl.className = parseFloat(budgetUsage) > 100 ? 'value failure' : 'value';
        }

        console.log(`记账统计: 收入¥${totalIncome}, 支出¥${totalExpense}, 结余¥${balance}, 预算使用率${budgetUsage}%`);

        // Update charts
        renderFinanceCharts();
    }

    function renderFinanceCharts() {
        console.log('渲染记账图表...');

        // Get all transactions
        const allKeys = Object.keys(localStorage);
        const transactionKeys = allKeys.filter(key => key.startsWith('finance-transaction-'));

        const transactions = [];
        transactionKeys.forEach(key => {
            try {
                const transaction = JSON.parse(localStorage.getItem(key));
                if (transaction) transactions.push(transaction);
            } catch (e) {
                console.error('解析交易记录失败:', key, e);
            }
        });

        // Render all 4 professional finance charts
        renderExpenseCategoryChart(transactions);      // 饼图：支出分类占比
        renderMonthlyComparisonChart(transactions);    // 柱状图：月度收支对比
        renderDailyTrendChart(transactions);           // 折线图：每日收支趋势
        renderCategoryTrendChart(transactions);        // 堆叠柱状图：分类支出趋势
    }

    function renderExpenseCategoryChart(transactions) {
        const canvas = getEl('expense-category-chart');
        if (!canvas) return;

        // Calculate expense by category
        const categoryExpenses = {};
        const categoryNames = {
            'food': '餐饮', 'shopping': '购物', 'transportation': '交通', 'entertainment': '娱乐',
            'housing': '住房', 'utilities': '水电费', 'health': '医疗健康', 'education': '教育'
        };

        transactions.forEach(transaction => {
            if (transaction.type === 'expense') {
                const categoryName = categoryNames[transaction.category] || transaction.category;
                categoryExpenses[categoryName] = (categoryExpenses[categoryName] || 0) + transaction.amount;
            }
        });

        const labels = Object.keys(categoryExpenses);
        const data = Object.values(categoryExpenses);
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];

        // Destroy existing chart if it exists
        if (window.expenseCategoryChart) {
            window.expenseCategoryChart.destroy();
        }

        if (labels.length === 0) {
            canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
            const ctx = canvas.getContext('2d');
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('暂无支出数据', canvas.width / 2, canvas.height / 2);
            return;
        }

        window.expenseCategoryChart = new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ¥${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 月度收支对比图表（柱状图）
    function renderMonthlyComparisonChart(transactions) {
        const canvas = getEl('monthly-comparison-chart');
        if (!canvas) return;

        const monthlyData = {};
        const currentDate = new Date();

        // 初始化最近6个月数据
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthLabel = `${date.getMonth() + 1}月`;
            monthlyData[monthKey] = { income: 0, expense: 0, label: monthLabel };
        }

        // 聚合交易数据
        transactions.forEach(transaction => {
            const transactionDate = new Date(transaction.date);
            const monthKey = `${transactionDate.getFullYear()}-${String(transactionDate.getMonth() + 1).padStart(2, '0')}`;

            if (monthlyData[monthKey]) {
                if (transaction.type === 'income') {
                    monthlyData[monthKey].income += transaction.amount;
                } else {
                    monthlyData[monthKey].expense += transaction.amount;
                }
            }
        });

        const labels = Object.keys(monthlyData).map(key => monthlyData[key].label);
        const incomeData = Object.keys(monthlyData).map(key => monthlyData[key].income);
        const expenseData = Object.keys(monthlyData).map(key => monthlyData[key].expense);

        // 销毁现有图表
        if (window.monthlyComparisonChart) {
            window.monthlyComparisonChart.destroy();
        }

        window.monthlyComparisonChart = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '收入',
                    data: incomeData,
                    backgroundColor: '#4CAF50',
                    borderColor: '#388E3C',
                    borderWidth: 1
                }, {
                    label: '支出',
                    data: expenseData,
                    backgroundColor: '#F44336',
                    borderColor: '#D32F2F',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ¥${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '月份'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '金额 (¥)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // 每日收支趋势图表（折线图）
    function renderDailyTrendChart(transactions) {
        const canvas = getEl('daily-trend-chart');
        if (!canvas) return;

        const dailyData = {};
        const today = new Date();

        // 初始化最近30天数据
        for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(today.getDate() - i);
            const dateKey = date.toISOString().split('T')[0];
            dailyData[dateKey] = { income: 0, expense: 0 };
        }

        // 聚合交易数据
        transactions.forEach(transaction => {
            if (dailyData[transaction.date]) {
                if (transaction.type === 'income') {
                    dailyData[transaction.date].income += transaction.amount;
                } else {
                    dailyData[transaction.date].expense += transaction.amount;
                }
            }
        });

        const dates = Object.keys(dailyData).sort();
        const labels = dates.map(date => {
            const d = new Date(date);
            return `${d.getMonth() + 1}/${d.getDate()}`;
        });
        const incomeData = dates.map(date => dailyData[date].income);
        const expenseData = dates.map(date => dailyData[date].expense);

        // 销毁现有图表
        if (window.dailyTrendChart) {
            window.dailyTrendChart.destroy();
        }

        window.dailyTrendChart = new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '收入',
                    data: incomeData,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }, {
                    label: '支出',
                    data: expenseData,
                    borderColor: '#F44336',
                    backgroundColor: 'rgba(244, 67, 54, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ¥${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '金额 (¥)'
                        },
                        beginAtZero: true
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    function renderTrendFinanceChart(transactions, viewType = 'weekly') {
        const canvas = getEl('daily-finance-chart');
        if (!canvas) return;

        let data, labels;

        if (viewType === 'weekly') {
            ({ data, labels } = getWeeklyTrendData(transactions));
        } else {
            ({ data, labels } = getMonthlyTrendData(transactions));
        }

        // Destroy existing chart if it exists
        if (window.dailyFinanceChart) {
            window.dailyFinanceChart.destroy();
        }

        window.dailyFinanceChart = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '收入',
                    data: data.income,
                    backgroundColor: 'rgba(76, 175, 80, 0.8)',
                    borderColor: '#4CAF50',
                    borderWidth: 1
                }, {
                    label: '支出',
                    data: data.expense,
                    backgroundColor: 'rgba(244, 67, 54, 0.8)',
                    borderColor: '#F44336',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ¥${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: viewType === 'weekly' ? '周期' : '月份'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '金额 (¥)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function getWeeklyTrendData(transactions) {
        const startDate = new Date(localStorage.getItem('challenge-start-date') || new Date());
        const weeklyData = {};

        // 初始化4周数据
        for (let week = 0; week < 4; week++) {
            const weekStart = new Date(startDate);
            weekStart.setDate(startDate.getDate() + week * 7);
            const weekKey = `第${week + 1}周`;
            weeklyData[weekKey] = { income: 0, expense: 0 };
        }

        // 聚合交易数据
        transactions.forEach(transaction => {
            const transactionDate = new Date(transaction.date);
            const daysDiff = Math.floor((transactionDate - startDate) / (1000 * 60 * 60 * 24));
            const weekIndex = Math.floor(daysDiff / 7);

            if (weekIndex >= 0 && weekIndex < 4) {
                const weekKey = `第${weekIndex + 1}周`;
                if (transaction.type === 'income') {
                    weeklyData[weekKey].income += transaction.amount;
                } else {
                    weeklyData[weekKey].expense += transaction.amount;
                }
            }
        });

        const labels = Object.keys(weeklyData);
        const data = {
            income: labels.map(week => weeklyData[week].income),
            expense: labels.map(week => weeklyData[week].expense)
        };

        return { data, labels };
    }

    function getMonthlyTrendData(transactions) {
        const monthlyData = {};
        const currentDate = new Date();

        // 初始化最近6个月数据
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthLabel = `${date.getMonth() + 1}月`;
            monthlyData[monthKey] = {
                income: 0,
                expense: 0,
                label: monthLabel
            };
        }

        // 聚合交易数据
        transactions.forEach(transaction => {
            const transactionDate = new Date(transaction.date);
            const monthKey = `${transactionDate.getFullYear()}-${String(transactionDate.getMonth() + 1).padStart(2, '0')}`;

            if (monthlyData[monthKey]) {
                if (transaction.type === 'income') {
                    monthlyData[monthKey].income += transaction.amount;
                } else {
                    monthlyData[monthKey].expense += transaction.amount;
                }
            }
        });

        const labels = Object.keys(monthlyData).map(key => monthlyData[key].label);
        const data = {
            income: Object.keys(monthlyData).map(key => monthlyData[key].income),
            expense: Object.keys(monthlyData).map(key => monthlyData[key].expense)
        };

        return { data, labels };
    }

    // 分类支出趋势图表（堆叠柱状图）
    function renderCategoryTrendChart(transactions) {
        const canvas = getEl('category-trend-chart');
        if (!canvas) return;

        const categoryNames = {
            'food': '餐饮', 'shopping': '购物', 'transportation': '交通',
            'entertainment': '娱乐', 'housing': '住房', 'utilities': '水电费',
            'health': '医疗健康', 'education': '教育'
        };

        const monthlyData = {};
        const currentDate = new Date();

        // 初始化最近6个月数据
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthLabel = `${date.getMonth() + 1}月`;
            monthlyData[monthKey] = { label: monthLabel };

            // 初始化各分类数据
            Object.keys(categoryNames).forEach(category => {
                monthlyData[monthKey][category] = 0;
            });
        }

        // 聚合支出数据（只统计支出）
        transactions.filter(t => t.type === 'expense').forEach(transaction => {
            const transactionDate = new Date(transaction.date);
            const monthKey = `${transactionDate.getFullYear()}-${String(transactionDate.getMonth() + 1).padStart(2, '0')}`;

            if (monthlyData[monthKey] && transaction.category) {
                monthlyData[monthKey][transaction.category] = (monthlyData[monthKey][transaction.category] || 0) + transaction.amount;
            }
        });

        const labels = Object.keys(monthlyData).map(key => monthlyData[key].label);
        const datasets = [];

        // 为每个分类创建数据集
        const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'];
        Object.keys(categoryNames).forEach((category, index) => {
            const data = Object.keys(monthlyData).map(key => monthlyData[key][category] || 0);
            const hasData = data.some(value => value > 0);

            if (hasData) {
                datasets.push({
                    label: categoryNames[category],
                    data: data,
                    backgroundColor: colors[index % colors.length],
                    borderColor: colors[index % colors.length],
                    borderWidth: 1
                });
            }
        });

        // 销毁现有图表
        if (window.categoryTrendChart) {
            window.categoryTrendChart.destroy();
        }

        window.categoryTrendChart = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ¥${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        title: {
                            display: true,
                            text: '月份'
                        }
                    },
                    y: {
                        stacked: true,
                        title: {
                            display: true,
                            text: '支出金额 (¥)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function addTransaction() {
        const date = getVal('transaction-date');
        const type = getVal('transaction-type');
        const category = getVal('transaction-category');
        const amount = getVal('transaction-amount');
        const note = getVal('transaction-note');

        // Validation
        if (!date || !type || !category || !amount) {
            alert('请填写所有必填字段');
            return;
        }

        if (isNaN(amount) || parseFloat(amount) <= 0) {
            alert('请输入有效的金额');
            return;
        }

        // Generate unique ID
        const transactionId = `finance-transaction-${Date.now()}`;

        // Create transaction object
        const transaction = {
            id: transactionId,
            date: date,
            type: type,
            category: category,
            amount: parseFloat(amount),
            note: note || ''
        };

        // Save to localStorage
        localStorage.setItem(transactionId, JSON.stringify(transaction));

        // Clear form
        getEl('transaction-date').value = '';
        getEl('transaction-type').value = '';
        getEl('transaction-category').value = '';
        getEl('transaction-amount').value = '';
        getEl('transaction-note').value = '';

        // Refresh display
        loadTransactions();
        updateFinanceStats();
        renderFinanceCharts();

        console.log('添加交易记录:', transaction);
    }

    function deleteTransaction(transactionId) {
        if (confirm('确定要删除这条交易记录吗？')) {
            localStorage.removeItem(transactionId);
            loadTransactions();
            updateFinanceStats();
            renderFinanceCharts();
        }
    }

    // Make functions available globally
    window.addTransaction = addTransaction;
    window.deleteTransaction = deleteTransaction;

    // --- BOOKSHELF FUNCTIONS ---
    function toggleBookshelf() {
        const drawer = document.querySelector('.bookshelf-drawer');
        drawer.classList.toggle('expanded');

        // Update bookshelf when opened
        if (drawer.classList.contains('expanded')) {
            updateBookshelf();
        }
    }

    function updateBookshelf() {
        console.log('更新书架显示...');

        const planId = getPlanId();
        if (!planId) return;

        // Get all books
        const books = [];
        for (let i = 1; i <= 10; i++) {
            const title = localStorage.getItem(`${planId}-book-goal-${i}`);
            const pages = parseInt(localStorage.getItem(`${planId}-book-goal-${i}-pages`)) || 0;
            if (title && pages > 0) {
                books.push({
                    id: i,
                    title: title,
                    totalPages: pages,
                    readPages: calculateBookProgress(planId, i)
                });
            }
        }

        // Update book count
        const bookCountEl = getEl('bookshelf-count');
        if (bookCountEl) {
            bookCountEl.textContent = `(${books.length}本)`;
        }

        // Render books on shelves
        renderBooksOnShelves(books);

        // Update stats
        updateBookshelfStats(books);
    }

    function calculateBookProgress(planId, bookId) {
        let totalPages = 0;
        for (let day = 1; day <= 30; day++) {
            const selectedBook = localStorage.getItem(`${planId}-read-book-day${day}`);
            const pages = parseInt(localStorage.getItem(`${planId}-read-pages-day${day}`)) || 0;
            if (selectedBook == bookId) {
                totalPages += pages;
            }
        }
        return totalPages;
    }

    function renderBooksOnShelves(books) {
        // Clear all shelves
        for (let i = 1; i <= 3; i++) {
            const shelf = getEl(`books-shelf-${i}`);
            if (shelf) shelf.innerHTML = '';
        }

        // Color themes for books
        const themes = ['red', 'blue', 'green', 'purple', 'orange', 'teal', 'pink', 'indigo'];

        books.forEach((book, index) => {
            const shelfIndex = (index % 3) + 1;
            const shelf = getEl(`books-shelf-${shelfIndex}`);
            if (!shelf) return;

            const progress = book.totalPages > 0 ? (book.readPages / book.totalPages) * 100 : 0;
            const theme = themes[index % themes.length];

            let status = 'planned';
            if (progress >= 100) status = 'completed';
            else if (progress > 0) status = 'reading';

            const bookElement = document.createElement('div');
            bookElement.className = `book-spine book-theme-${theme}`;
            bookElement.title = `${book.title}\n进度: ${book.readPages}/${book.totalPages}页 (${progress.toFixed(1)}%)`;

            bookElement.innerHTML = `
                <div class="book-status ${status}"></div>
                <div class="book-title">${book.title.length > 8 ? book.title.substring(0, 8) + '...' : book.title}</div>
                <div class="book-progress">
                    <div class="book-progress-bar" style="width: ${Math.min(progress, 100)}%"></div>
                </div>
            `;

            // Add click event to show book details
            bookElement.addEventListener('click', () => showBookDetails(book, progress));

            shelf.appendChild(bookElement);
        });
    }

    function showBookDetails(book, progress) {
        const status = progress >= 100 ? '已完成' : progress > 0 ? '阅读中' : '计划中';
        const statusColor = progress >= 100 ? '#4CAF50' : progress > 0 ? '#FF9500' : '#9E9E9E';

        alert(`📖 ${book.title}\n\n📄 总页数: ${book.totalPages}页\n📖 已读: ${book.readPages}页\n📊 进度: ${progress.toFixed(1)}%\n📌 状态: ${status}`);
    }

    function updateBookshelfStats(books) {
        const totalBooks = books.length;
        let completedBooks = 0;
        let readingBooks = 0;
        let totalPagesRead = 0;

        books.forEach(book => {
            const progress = book.totalPages > 0 ? (book.readPages / book.totalPages) * 100 : 0;
            if (progress >= 100) {
                completedBooks++;
            } else if (progress > 0) {
                readingBooks++;
            }
            totalPagesRead += book.readPages;
        });

        // Update stats display
        const totalBooksEl = getEl('total-books');
        if (totalBooksEl) totalBooksEl.textContent = totalBooks;

        const completedBooksEl = getEl('completed-books');
        if (completedBooksEl) completedBooksEl.textContent = completedBooks;

        const readingBooksEl = getEl('reading-books');
        if (readingBooksEl) readingBooksEl.textContent = readingBooks;

        const totalPagesEl = getEl('total-pages-read');
        if (totalPagesEl) totalPagesEl.textContent = totalPagesRead;
    }

    // Make bookshelf functions available globally
    window.toggleBookshelf = toggleBookshelf;

    // --- HABIT MATRIX FUNCTIONS ---
    let currentHabitModal = { habitId: null, day: null, dateStr: null };

    function editHabitDay(habitId, dateKey, displayDate) {
        // 保存当前编辑的习惯信息
        currentHabitModal = { habitId, dateKey, displayDate };

        const habit = habitList.find(h => h.id == habitId);
        const habitName = habit ? habit.name : '未知习惯';

        // 设置弹窗标题
        const modalTitle = document.getElementById('habit-modal-title');
        if (modalTitle) {
            modalTitle.textContent = `${habitName} - ${displayDate}`;
        }

        // 加载并显示记录
        loadHabitRecords(habitId, dateKey, displayDate);

        // 显示弹窗
        const modal = document.getElementById('habit-modal');
        if (modal) {
            modal.classList.add('show');
        }

        // 设置默认时间为当前时间
        const timeInput = document.getElementById('habit-time-input');
        if (timeInput) {
            const now = new Date();
            const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
            timeInput.value = timeStr;
        }
    }

    function closeHabitModal() {
        const modal = document.getElementById('habit-modal');
        if (modal) {
            modal.classList.remove('show');
        }

        // 清空表单
        document.getElementById('habit-time-input').value = '';
        document.getElementById('habit-duration-input').value = '';
        document.getElementById('habit-note-input').value = '';

        // 重新渲染矩阵以更新显示
        renderHabitTracker();
        updateHabitStats();
    }

    function loadHabitRecords(habitId, dateKey, displayDate) {
        const planId = getPlanId();
        const recordsKey = `${planId}-habit-h${habitId}-date-${dateKey}-records`;
        const records = JSON.parse(localStorage.getItem(recordsKey) || '[]');

        // 更新总计显示
        const summary = document.getElementById('habit-summary');
        if (summary) {
            const totalCount = records.length;
            const totalDuration = records.reduce((sum, record) => sum + (record.duration || 0), 0);
            summary.innerHTML = `<div class="habit-summary-text">今日总计: ${totalCount}次，共${totalDuration}分钟</div>`;
        }

        // 渲染记录列表
        const recordsList = document.getElementById('habit-records-list');
        if (!recordsList) return;

        if (records.length === 0) {
            recordsList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无记录</p>';
            return;
        }

        let html = '';
        records.forEach((record, index) => {
            html += `
                <div class="habit-record-item">
                    <div class="habit-record-info">
                        <div class="habit-record-time">${record.time}</div>
                        <div class="habit-record-duration">持续 ${record.duration || 0} 分钟${record.note ? ` - ${record.note}` : ''}</div>
                    </div>
                    <div class="habit-record-actions">
                        <button class="habit-record-btn habit-record-edit" onclick="editHabitRecord(${index})">编辑</button>
                        <button class="habit-record-btn habit-record-delete" onclick="deleteHabitRecord(${index})">删除</button>
                    </div>
                </div>
            `;
        });

        recordsList.innerHTML = html;
    }

    function addHabitRecord() {
        const timeInput = document.getElementById('habit-time-input');
        const durationInput = document.getElementById('habit-duration-input');
        const noteInput = document.getElementById('habit-note-input');

        const time = timeInput.value;
        const duration = parseInt(durationInput.value) || 0;
        const note = noteInput.value.trim();

        if (!time) {
            alert('请选择发生时间');
            return;
        }

        if (duration <= 0) {
            alert('请输入有效的持续时长');
            return;
        }

        const { habitId, dateKey, displayDate } = currentHabitModal;
        const planId = getPlanId();
        const recordsKey = `${planId}-habit-h${habitId}-date-${dateKey}-records`;
        const records = JSON.parse(localStorage.getItem(recordsKey) || '[]');

        // 添加新记录
        const newRecord = {
            id: Date.now(), // 使用时间戳作为ID
            time: time,
            duration: duration,
            note: note,
            timestamp: new Date().toISOString()
        };

        records.push(newRecord);

        // 按时间排序
        records.sort((a, b) => a.time.localeCompare(b.time));

        // 保存记录
        localStorage.setItem(recordsKey, JSON.stringify(records));

        // 更新汇总数据
        updateHabitSummary(habitId, dateKey);

        // 重新加载记录显示
        loadHabitRecords(habitId, dateKey, displayDate);

        // 清空表单
        timeInput.value = '';
        durationInput.value = '';
        noteInput.value = '';

        // 设置下次默认时间
        const now = new Date();
        const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        timeInput.value = timeStr;
    }

    function editHabitRecord(index) {
        const { habitId, dateKey, displayDate } = currentHabitModal;
        const planId = getPlanId();
        const recordsKey = `${planId}-habit-h${habitId}-date-${dateKey}-records`;
        const records = JSON.parse(localStorage.getItem(recordsKey) || '[]');

        if (index < 0 || index >= records.length) return;

        const record = records[index];

        const newTime = prompt('修改发生时间 (HH:MM):', record.time);
        if (newTime === null) return;

        const newDuration = prompt('修改持续时长 (分钟):', record.duration);
        if (newDuration === null) return;

        const newNote = prompt('修改备注:', record.note || '');
        if (newNote === null) return;

        // 验证时间格式
        if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(newTime)) {
            alert('时间格式不正确，请使用 HH:MM 格式');
            return;
        }

        const duration = parseInt(newDuration);
        if (isNaN(duration) || duration <= 0) {
            alert('持续时长必须是正整数');
            return;
        }

        // 更新记录
        records[index] = {
            ...record,
            time: newTime,
            duration: duration,
            note: newNote.trim()
        };

        // 重新排序
        records.sort((a, b) => a.time.localeCompare(b.time));

        // 保存
        localStorage.setItem(recordsKey, JSON.stringify(records));

        // 更新汇总
        updateHabitSummary(habitId, dateKey);

        // 重新加载显示
        loadHabitRecords(habitId, dateKey, displayDate);
    }

    function deleteHabitRecord(index) {
        if (!confirm('确定要删除这条记录吗？')) return;

        const { habitId, dateKey, displayDate } = currentHabitModal;
        const planId = getPlanId();
        const recordsKey = `${planId}-habit-h${habitId}-date-${dateKey}-records`;
        const records = JSON.parse(localStorage.getItem(recordsKey) || '[]');

        if (index < 0 || index >= records.length) return;

        // 删除记录
        records.splice(index, 1);

        // 保存
        localStorage.setItem(recordsKey, JSON.stringify(records));

        // 更新汇总
        updateHabitSummary(habitId, dateKey);

        // 重新加载显示
        loadHabitRecords(habitId, dateKey, displayDate);
    }

    function updateHabitSummary(habitId, dateKey) {
        const planId = getPlanId();
        const recordsKey = `${planId}-habit-h${habitId}-date-${dateKey}-records`;
        const records = JSON.parse(localStorage.getItem(recordsKey) || '[]');

        const totalCount = records.length;
        const totalTime = records.reduce((sum, record) => sum + (record.duration || 0), 0);

        // 新的存储格式不需要兼容旧格式，因为我们已经改为按日期存储
    }

    function updateHabitMatrixStats() {
        const planId = getPlanId();
        if (!planId) return;

        let totalDaysWithData = 0;
        let worstDay = { date: '', count: 0 };
        let bestStreak = 0;
        let currentStreak = 0;

        // 分析每一天的数据
        for (let day = 1; day <= MONTHLY_DAYS; day++) {
            let dayTotalCount = 0;

            // 计算这一天所有习惯的总次数
            habitList.forEach(habit => {
                const count = parseInt(localStorage.getItem(`${planId}-habit-h${habit.id}-d${day}-count`)) || 0;
                dayTotalCount += count;
            });

            if (dayTotalCount > 0) {
                totalDaysWithData++;
                currentStreak = 0; // 重置连续无习惯天数

                // 检查是否是最严重的一天
                if (dayTotalCount > worstDay.count) {
                    const startDate = new Date(localStorage.getItem('challenge-start-date'));
                    const currentDate = new Date(startDate);
                    currentDate.setDate(startDate.getDate() + day - 1);
                    worstDay = {
                        date: `${currentDate.getMonth() + 1}/${currentDate.getDate()}`,
                        count: dayTotalCount
                    };
                }
            } else {
                currentStreak++;
                bestStreak = Math.max(bestStreak, currentStreak);
            }
        }

        // 更新统计显示
        const totalDaysEl = getEl('matrix-total-days');
        if (totalDaysEl) totalDaysEl.textContent = totalDaysWithData;

        const worstDayEl = getEl('matrix-worst-day');
        if (worstDayEl) worstDayEl.textContent = worstDay.date || '--';

        const bestStreakEl = getEl('matrix-best-streak');
        if (bestStreakEl) bestStreakEl.textContent = bestStreak;
    }

    function addHabitTooltipEvents() {
        const tooltip = getEl('habit-tooltip');
        if (!tooltip) return;

        const cells = document.querySelectorAll('.habit-day-cell');
        cells.forEach(cell => {
            cell.addEventListener('mouseenter', (e) => {
                const habitId = e.target.dataset.habitId;
                const day = e.target.dataset.day;
                const date = e.target.dataset.date;
                const count = e.target.dataset.count;
                const time = e.target.dataset.time;

                const habit = habitList.find(h => h.id == habitId);
                const habitName = habit ? habit.name : '未知习惯';

                tooltip.innerHTML = `
                    <strong>${habitName}</strong><br>
                    日期: ${date}<br>
                    次数: ${count}次<br>
                    时长: ${time}分钟
                `;

                const rect = e.target.getBoundingClientRect();
                tooltip.style.left = `${rect.left + rect.width / 2}px`;
                tooltip.style.top = `${rect.top - 10}px`;
                tooltip.style.transform = 'translate(-50%, -100%)';
                tooltip.classList.add('show');
            });

            cell.addEventListener('mouseleave', () => {
                tooltip.classList.remove('show');
            });
        });
    }

    // --- HABIT MANAGEMENT FUNCTIONS ---
    function addHabit() {
        const nameInput = getEl('new-habit-name');
        if (!nameInput) return;

        const habitName = nameInput.value.trim();
        if (!habitName) {
            alert('请输入习惯名称');
            return;
        }

        // Check if habit already exists
        if (habitList.some(habit => habit.name === habitName)) {
            alert('该习惯已存在');
            return;
        }

        // Generate new habit ID
        const newId = habitList.length > 0 ? Math.max(...habitList.map(h => h.id)) + 1 : 1;

        // Add new habit
        const newHabit = {
            id: newId,
            name: habitName
        };

        habitList.push(newHabit);

        // Save to localStorage
        const planId = getPlanId();
        if (planId) {
            localStorage.setItem(`${planId}-habits`, JSON.stringify(habitList));
        }

        // Clear input
        nameInput.value = '';

        // Re-render the tracker
        renderHabitTracker();

        // Update statistics
        updateHabitStats();

        console.log(`添加习惯: ${habitName} (ID: ${newId})`);
    }

    function deleteHabit(habitId) {
        const habit = habitList.find(h => h.id === habitId);
        if (!habit) return;

        if (!confirm(`确定要删除习惯"${habit.name}"吗？这将删除所有相关数据。`)) {
            return;
        }

        // Remove from habitList
        habitList = habitList.filter(h => h.id !== habitId);

        // Save updated list
        const planId = getPlanId();
        if (planId) {
            localStorage.setItem(`${planId}-habits`, JSON.stringify(habitList));

            // Remove all data for this habit
            for (let day = 1; day <= MONTHLY_DAYS; day++) {
                localStorage.removeItem(`${planId}-habit-h${habitId}-d${day}-count`);
                localStorage.removeItem(`${planId}-habit-h${habitId}-d${day}-time`);
            }
        }

        // Re-render the tracker
        renderHabitTracker();

        // Update statistics
        updateHabitStats();

        console.log(`删除习惯: ${habit.name} (ID: ${habitId})`);
    }

    function initializeHabitManager() {
        // Add event listener for add habit button
        const addBtn = getEl('add-habit-btn');
        if (addBtn) {
            addBtn.addEventListener('click', addHabit);
        }

        // Add event listener for Enter key in input
        const nameInput = getEl('new-habit-name');
        if (nameInput) {
            nameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    addHabit();
                }
            });
        }

        console.log('习惯管理器初始化完成');

        // 添加弹窗点击外部关闭功能
        const modal = document.getElementById('habit-modal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeHabitModal();
                }
            });
        }

        // 添加ESC键关闭功能
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modal = document.getElementById('habit-modal');
                if (modal && modal.classList.contains('show')) {
                    closeHabitModal();
                }
            }
        });
    }

    // Make habit management functions available globally
    window.addHabit = addHabit;
    window.deleteHabit = deleteHabit;
    window.editHabitDay = editHabitDay;
    window.closeHabitModal = closeHabitModal;
    window.addHabitRecord = addHabitRecord;
    window.editHabitRecord = editHabitRecord;
    window.deleteHabitRecord = deleteHabitRecord;

    // --- DATA CALCULATIONS & UPDATES ---
    function updateAllStats() {
        for (let i = 1; i <= MONTHLY_DAYS; i++) {
            updateLifestyleCalcs(i);
            updateDailyTimeAnalysis(i);
        }
        updateAsylumProgress();
        updateEnglishProgress();
        updateHabitStats();
        updateMeditationStats();
        updateReadingStats();
        updateSpeechStats();
        updateFinanceStats();
        updateLifestyleOverview();
        renderLifestyleCharts();
        updateMiniCharts(currentViewDate);
    }

    // Force update all statistics with detailed logging
    window.forceUpdateAllStats = function() {
        console.log('🔧 强制更新所有统计...');

        const planId = getPlanId();
        console.log(`计划ID: ${planId}`);

        if (!planId) {
            alert('❌ 无法获取计划ID，请先设置挑战开始日期');
            return;
        }

        // Check if we have any data
        const allKeys = Object.keys(localStorage);
        const planKeys = allKeys.filter(key => key.includes(planId));
        console.log(`找到 ${planKeys.length} 个相关数据键`);

        if (planKeys.length === 0) {
            alert('❌ 没有找到任何数据，请先生成测试数据');
            return;
        }

        // Force update each statistic individually
        console.log('更新Asylum统计...');
        updateAsylumProgress();

        console.log('更新英语统计...');
        updateEnglishProgress();

        console.log('更新冥想统计...');
        updateMeditationStats();

        console.log('更新阅读统计...');
        updateReadingStats();

        console.log('更新习惯统计...');
        updateHabitStats();

        console.log('更新演讲统计...');
        updateSpeechStats();

        console.log('更新记账统计...');
        updateFinanceStats();

        console.log('更新生活统计...');
        updateLifestyleOverview();

        console.log('更新图表...');
        renderLifestyleCharts();
        updateMiniCharts(currentViewDate);

        alert('✅ 统计更新完成！请检查各个页面的统计数据。');
        console.log('✅ 所有统计更新完成');
    };
    
    function calculateBMR(dayIndex) {
        const age = getNum('profile-age');
        const gender = getVal('profile-gender');
        const height = getNum('profile-height');
        const planId = getPlanId();
        // Load weight and body fat specifically for the given day from localStorage
        const weight = parseFloat(localStorage.getItem(`${planId}-life-weight-day${dayIndex}`)) || 0;
        const bodyFat = parseFloat(localStorage.getItem(`${planId}-life-fat-day${dayIndex}`)) || 0;

        if (!age || !gender || !height || !weight) return 0;

        // 如果有体脂率数据，使用更精确的Katch-McArdle公式
        if (bodyFat > 0 && bodyFat < 50) {
            const leanBodyMass = weight * (1 - bodyFat / 100);
            return Math.round(370 + (21.6 * leanBodyMass));
        }

        // 否则使用Mifflin-St Jeor公式作为备选
        return (gender === 'male')
            ? Math.round(10 * weight + 6.25 * height - 5 * age + 5)
            : Math.round(10 * weight + 6.25 * height - 5 * age - 161);
    }

    function updateLifestyleCalcs(dayIndex) {
        if (!dayIndex || dayIndex < 1) return;
        const planId = getPlanId();
        if(!planId) return;

        const breakfast = parseFloat(localStorage.getItem(`${planId}-life-breakfast-day${dayIndex}`)) || 0;
        const lunch = parseFloat(localStorage.getItem(`${planId}-life-lunch-day${dayIndex}`)) || 0;
        const dinner = parseFloat(localStorage.getItem(`${planId}-life-dinner-day${dayIndex}`)) || 0;
        const totalIntake = breakfast + lunch + dinner;
        localStorage.setItem(`${planId}-life-total-intake-day${dayIndex}`, totalIntake);
        
        const asylumKcal = parseFloat(localStorage.getItem(`${planId}-asylum-kcal-day${dayIndex}`)) || 0;
        const bmr = calculateBMR(dayIndex);
        const otherBurn = parseFloat(localStorage.getItem(`${planId}-life-kcal-out-day${dayIndex}`)) || 0;
        const totalBurn = bmr + asylumKcal + otherBurn;
        const deficit = Math.round(totalIntake - totalBurn);
        localStorage.setItem(`${planId}-life-deficit-day${dayIndex}`, deficit);

        // Update UI only if viewing this day
        if (getDayIndexFromDate(currentViewDate, getVal('challenge-start-date')) === dayIndex) {
            getEl('life-total-intake').textContent = `${totalIntake} kcal`;
            getEl('life-asylum-burn').textContent = asylumKcal;

            // 显示基础代谢和计算方法
            const planId = getPlanId();
            const bodyFat = parseFloat(localStorage.getItem(`${planId}-life-fat-day${dayIndex}`)) || 0;
            const bmrMethod = (bodyFat > 0 && bodyFat < 50) ? 'K-M' : 'M-S';
            getEl('life-bmr').textContent = `${bmr} (${bmrMethod})`;

            getEl('life-deficit').textContent = deficit;
        }
    }
    
    function calculateSleepDurationFromTimes(start, end) {
        if (!start || !end) return 0;
        const [startH, startM] = start.split(':').map(Number);
        const [endH, endM] = end.split(':').map(Number);
        let startDate = new Date(2000, 0, 1, startH, startM);
        let endDate = new Date(2000, 0, 1, endH, endM);
        if (endDate <= startDate) {
            endDate.setDate(endDate.getDate() + 1);
        }
        return Math.round((endDate - startDate) / (1000 * 60));
    }

    function getTrackedTimeData(dayIndex) { 
        let habitTime = 0; 
        const planId = getPlanId();
        if(!planId) return {};

        habitList.forEach(habit => { 
            habitTime += parseFloat(localStorage.getItem(`${planId}-habit-h${habit.id}-d${dayIndex}-time`)) || 0;
        });
        
        let speechTime = 0;
        if(dayIndex > 0) { 
            const weekIndex = Math.ceil(dayIndex / 7); 
            const dayOfWeekIndex = (dayIndex - 1) % 7 + 1; 
            speechTime += parseFloat(localStorage.getItem(`${planId}-speech-warmup-w${weekIndex}-d${dayOfWeekIndex}-time`)) || 0;
            speechTime += parseFloat(localStorage.getItem(`${planId}-speech-structure-w${weekIndex}-d${dayOfWeekIndex}-time`)) || 0;
        }

        const sleepStart = localStorage.getItem(`${planId}-life-sleep-start-day${dayIndex}`);
        const sleepEnd = localStorage.getItem(`${planId}-life-sleep-end-day${dayIndex}`);

        return { 
            '睡眠': calculateSleepDurationFromTimes(sleepStart, sleepEnd), 
            'Asylum': parseFloat(localStorage.getItem(`${planId}-asylum-time-day${dayIndex}`)) || 0,
            '英语': parseFloat(localStorage.getItem(`${planId}-eng-time-day${dayIndex}`)) || 0, 
            '习惯': habitTime, 
            '冥想': parseFloat(localStorage.getItem(`${planId}-med-time-day${dayIndex}`)) || 0, 
            '阅读': parseFloat(localStorage.getItem(`${planId}-read-time-day${dayIndex}`)) || 0, 
            '演讲': speechTime, 
        }; 
    }
    
    function get24HourChartConfig(dayIndex, legendDisplay = false, titleText = '') {
        const trackedData = getTrackedTimeData(dayIndex);
        let labels = [], values = [], colors = [];
        const baseColors = { '睡眠': '#6366f1', 'Asylum': '#f97316', '英语': '#10b981', '习惯': '#ef4444', '冥想': '#64748b', '阅读': '#ca8a04', '演讲': '#db2777' };
        let trackedTotal = Object.values(trackedData).reduce((sum, val) => sum + val, 0);
        
        for (const [key, value] of Object.entries(trackedData)) {
            if (value > 0) { labels.push(key); values.push(value); colors.push(baseColors[key] || '#cbd5e0'); }
        }
        
        const untrackedTime = Math.max(0, DAY_IN_MINUTES - trackedTotal);
        if (untrackedTime > 0) { labels.push('其他时间'); values.push(untrackedTime); colors.push('#e2e8f0'); }
        
        return {
            config: { 
                type: 'doughnut', 
                data: { labels, datasets: [{ data: values, backgroundColor: colors, borderColor: 'white', borderWidth: 2 }] }, 
                options: { responsive: true, maintainAspectRatio: false, cutout: '60%', plugins: { legend: { display: legendDisplay, position: 'right' }, title: { display: !!titleText, text: titleText }, tooltip: { callbacks: { label: (c) => `${c.label}: ${Math.floor(c.parsed / 60)}h ${c.parsed % 60}m` } } } }
            },
            trackedTotal,
            untrackedTime
        };
    }

    function drawSpotlightChart(dayIndex) { 
        const { config, trackedTotal } = get24HourChartConfig(dayIndex, true, `Day ${dayIndex} 时间分配`); 
        const placeholder = getEl('spotlight-placeholder'); 
        const canvasContainer = getEl('spotlight-canvas-container'); 
        if (trackedTotal > 0) { 
            placeholder.style.display = 'none'; 
            canvasContainer.style.display = 'block'; 
            const ctx = getEl('spotlight-canvas').getContext('2d'); 
            if (spotlightChart) spotlightChart.destroy(); 
            spotlightChart = new Chart(ctx, config); 
        } else { 
            placeholder.textContent = `Day ${dayIndex} 暂无时间记录`; 
            placeholder.style.display = 'block'; 
            canvasContainer.style.display = 'none'; 
        } 
    }

    function updateDailyTimeAnalysis(dayIndex) {
        if (!dayIndex || dayIndex > MONTHLY_DAYS) return;

        const trackedDataCurrent = getTrackedTimeData(dayIndex);
        const dayIndexPreviousWeek = dayIndex - 7;
        const trackedDataPrevious = dayIndexPreviousWeek > 0 ? getTrackedTimeData(dayIndexPreviousWeek) : null;

        let trackedTotalCurrent = 0;
        let trackedTotalPrevious = 0;

        for (const key in trackedDataCurrent) {
            const currentValue = trackedDataCurrent[key] || 0;
            trackedTotalCurrent += currentValue;

            // 更新主数值
            const valueEl = getEl(`analysis-${key}-time-day${dayIndex}`);
            if(valueEl) valueEl.textContent = currentValue;

            // 更新对比数值
            const compEl = getEl(`analysis-${key}-comp-day${dayIndex}`);
            if (compEl) {
                if (trackedDataPrevious) {
                    const previousValue = trackedDataPrevious[key] || 0;
                    trackedTotalPrevious += previousValue;
                    const diff = currentValue - previousValue;

                    if (diff !== 0) {
                        compEl.textContent = (diff > 0 ? '+' : '') + diff + 'm';
                        compEl.className = 'comparison ' + (key === '习惯' ? (diff > 0 ? 'negative' : 'positive') : (diff > 0 ? 'positive' : 'negative'));
                    } else {
                        compEl.textContent = '';
                    }
                } else {
                    compEl.textContent = ''; // 没有上周数据
                }
            }
        }

        // 更新总计
        getEl(`analysis-tracked-total-day${dayIndex}`).textContent = trackedTotalCurrent;
        getEl(`analysis-untracked-time-day${dayIndex}`).textContent = Math.max(0, DAY_IN_MINUTES - trackedTotalCurrent);

        // 更新总计的对比
        const totalCompEl = getEl(`analysis-tracked-total-comp-day${dayIndex}`);
        if(totalCompEl && trackedDataPrevious) {
            const totalDiff = trackedTotalCurrent - trackedTotalPrevious;
            if (totalDiff !== 0) {
                totalCompEl.textContent = (totalDiff > 0 ? '+' : '') + totalDiff + 'm';
                totalCompEl.className = 'comparison ' + (totalDiff > 0 ? 'positive' : 'negative');
            } else {
                totalCompEl.textContent = '';
            }
        }

        // 绘制饼图的逻辑保持不变
        const { config } = get24HourChartConfig(dayIndex, false);
        const chartCanvasId = `pie-chart-day${dayIndex}`;
        const ctx = getEl(chartCanvasId)?.getContext('2d');
        if (!ctx) return;
        if (Chart.getChart(chartCanvasId)) Chart.getChart(chartCanvasId).destroy();
        if (trackedTotalCurrent > 0) {
            new Chart(ctx, { type: 'pie', data: config.data, options: { responsive: true, maintainAspectRatio: true, plugins: { legend: { display: false }, tooltip: { enabled: false } }, animation: { duration: 500 } }});
        }
    }

    function renderLifestyleCharts() {
        const labels = [], weightData = [], fatData = [], calorieData = [];
        const planId = getPlanId();
        if(!planId) return;

        for (let day = 1; day <= MONTHLY_DAYS; day++) {
            const weight = parseFloat(localStorage.getItem(`${planId}-life-weight-day${day}`)) || null;
            const fat = parseFloat(localStorage.getItem(`${planId}-life-fat-day${day}`)) || null;
            const deficit = parseFloat(localStorage.getItem(`${planId}-life-deficit-day${day}`)) || null;
            if (weight || fat || deficit) {
                labels.push(`D${day}`);
                weightData.push(weight);
                fatData.push(fat);
                calorieData.push(deficit);
            }
        }
        
        const createLineChart = (chartInstance, canvasId, label, data, color) => {
            if (chartInstance) chartInstance.destroy();
            const ctx = getEl(canvasId)?.getContext('2d');
            if (!ctx) return null;
            return new Chart(ctx, {
                type: 'line',
                data: { labels, datasets: [{ label, data, borderColor: color, backgroundColor: `${color}30`, fill: true, tension: 0.4, spanGaps: true }] },
                options: { responsive: true, maintainAspectRatio: false }
            });
        };

        const orange = getComputedStyle(document.documentElement).getPropertyValue('--primary-accent').trim();
        const green = getComputedStyle(document.documentElement).getPropertyValue('--success-color').trim();
        const red = getComputedStyle(document.documentElement).getPropertyValue('--failure-color').trim();

        lifestyleWeightChart = createLineChart(lifestyleWeightChart, 'lifestyle-weight-chart', '体重 (kg)', weightData, orange);
        lifestyleFatChart = createLineChart(lifestyleFatChart, 'lifestyle-fat-chart', '体脂 ?(%)', fatData, green);
        lifestyleCalorieChart = createLineChart(lifestyleCalorieChart, 'lifestyle-calorie-chart', '热量平衡 (kcal)', calorieData, red);
    }

    function updateAsylumProgress() {
        const planId = getPlanId();
        if (!planId) return;

        let completedDays = 0;
        let totalTime = 0;
        let totalCalories = 0;

        for (let day = 1; day <= ASYLUM_DAYS; day++) {
            const isCompleted = localStorage.getItem(`${planId}-asylum-check-day${day}`) === 'true';
            const time = parseInt(localStorage.getItem(`${planId}-asylum-time-day${day}`)) || 0;
            const calories = parseInt(localStorage.getItem(`${planId}-asylum-kcal-day${day}`)) || 0;

            if (isCompleted) {
                completedDays++;
                totalTime += time;
                totalCalories += calories;
            }
        }

        console.log(`Asylum统计: 完成${completedDays}天, 总时间${totalTime}分钟, 总卡路里${totalCalories}`);

        // Update completed days display (correct ID)
        const completedDaysEl = getEl('asylum-completed-days');
        if (completedDaysEl) {
            completedDaysEl.textContent = `${completedDays} / ${ASYLUM_DAYS}`;
        }

        // Update progress bar
        const progressBar = getEl('asylumProgressBar');
        if (progressBar) {
            const percentage = (completedDays / ASYLUM_DAYS) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        // Update total calories (correct ID)
        const totalCaloriesEl = getEl('asylum-total-kcal');
        if (totalCaloriesEl) {
            totalCaloriesEl.textContent = `${totalCalories}`;
        }

        // --- Asylum 预测逻辑 ---
        const asylumPredictionEl = getEl('asylum-prediction-text');
        if (asylumPredictionEl) {
            const startDateStr = getVal('challenge-start-date');
            // Asylum 的 'currentProgress' 就是已完成天数
            const prediction = calculatePrediction(ASYLUM_DAYS, completedDays, startDateStr);
            if (prediction) {
                asylumPredictionEl.textContent = `按当前进度，预计将在 ${prediction.predictedCompletionDate} 完成挑战。`;
            } else {
                asylumPredictionEl.textContent = '开始记录后，将为您生成进度预测。';
            }
        }
    }
    function updateEnglishProgress() {
        const planId = getPlanId();
        if (!planId) return;

        let totalWords = 0;
        let totalTime = 0;
        let studyDays = 0;

        for (let day = 1; day <= MONTHLY_DAYS; day++) {
            const words = parseInt(localStorage.getItem(`${planId}-eng-new-day${day}`)) || 0;
            const time = parseInt(localStorage.getItem(`${planId}-eng-time-day${day}`)) || 0;

            if (words > 0 || time > 0) {
                studyDays++;
                totalWords += words;
                totalTime += time;
            }
        }

        console.log(`英语统计: 总单词${totalWords}, 总时间${totalTime}分钟, 学习${studyDays}天`);

        // Update total words learned (correct ID)
        const totalWordsEl = getEl('english-words-count');
        if (totalWordsEl) {
            totalWordsEl.textContent = totalWords;
        }

        // Update progress bar
        const progressBar = getEl('englishProgressBar');
        if (progressBar) {
            const percentage = Math.min((totalWords / 5000) * 100, 100);
            progressBar.style.width = `${percentage}%`;
        }

        // Update total study time (correct ID)
        const totalTimeEl = getEl('english-total-time');
        if (totalTimeEl) {
            totalTimeEl.textContent = `${totalTime} 分钟`;
        }

        // --- 英语学习预测逻辑 ---
        const englishPredictionEl = getEl('english-prediction-text');
        if (englishPredictionEl) {
            const startDateStr = getVal('challenge-start-date');
            const WORD_GOAL = 5000;
            const prediction = calculatePrediction(WORD_GOAL, totalWords, startDateStr);
            if (prediction) {
                englishPredictionEl.textContent = `要达到 ${WORD_GOAL} 单词目标，按当前速度（日均 ${prediction.avgDailyProgress} 词）还需约 ${prediction.remainingDaysNeeded} 天。`;
            } else {
                englishPredictionEl.textContent = '开始学习后，将为您生成进度预测。';
            }
        }
    }
    function updateHabitStats() {
        const planId = getPlanId();
        if (!planId) return;

        // Get habits from habitList (global variable)
        if (!habitList || habitList.length === 0) {
            console.log('习惯统计: 没有习惯数据');
            return;
        }

        let totalOccurrences = 0;
        let totalTime = 0;

        habitList.forEach(habit => {
            for (let day = 1; day <= MONTHLY_DAYS; day++) {
                const count = parseInt(localStorage.getItem(`${planId}-habit-h${habit.id}-d${day}-count`)) || 0;
                const time = parseInt(localStorage.getItem(`${planId}-habit-h${habit.id}-d${day}-time`)) || 0;

                totalOccurrences += count;
                totalTime += time;
            }
        });

        console.log(`习惯统计: 总次数${totalOccurrences}, 总时间${totalTime}分钟`);

        // Update total occurrences (correct ID)
        const totalOccurrencesEl = getEl('habit-total-occurrences');
        if (totalOccurrencesEl) {
            totalOccurrencesEl.textContent = totalOccurrences;
        }

        // Update total time (correct ID)
        const totalTimeEl = getEl('habit-total-time');
        if (totalTimeEl) {
            totalTimeEl.textContent = totalTime;
        }
    }
    function updateMeditationStats() {
        const planId = getPlanId();
        if (!planId) return;

        let totalDays = 0;
        let totalTime = 0;

        for (let day = 1; day <= MONTHLY_DAYS; day++) {
            const isCompleted = localStorage.getItem(`${planId}-med-check-day${day}`) === 'true';
            const time = parseInt(localStorage.getItem(`${planId}-med-time-day${day}`)) || 0;

            if (isCompleted) {
                totalDays++;
                totalTime += time;
            }
        }

        console.log(`冥想统计: 完成${totalDays}天, 总时间${totalTime}分钟`);

        // Update total meditation days (correct ID)
        const totalDaysEl = getEl('meditation-total-days');
        if (totalDaysEl) {
            totalDaysEl.textContent = `${totalDays}天`;
        }

        // Update total meditation time (correct ID)
        const totalTimeEl = getEl('meditation-total-time');
        if (totalTimeEl) {
            totalTimeEl.textContent = `${totalTime} 分钟`;
        }
    }
    function updateReadingStats() {
        const planId = getPlanId();
        if (!planId) return;

        let totalPages = 0;
        let totalTime = 0;
        let readingDays = 0;

        for (let day = 1; day <= MONTHLY_DAYS; day++) {
            const pages = parseInt(localStorage.getItem(`${planId}-read-pages-day${day}`)) || 0;
            const time = parseInt(localStorage.getItem(`${planId}-read-time-day${day}`)) || 0;

            if (pages > 0 || time > 0) {
                readingDays++;
                totalPages += pages;
                totalTime += time;
            }
        }

        console.log(`阅读统计: 总页数${totalPages}, 总时间${totalTime}分钟, 阅读${readingDays}天`);

        // Update total reading time (correct ID)
        const totalTimeEl = getEl('reading-total-time');
        if (totalTimeEl) {
            totalTimeEl.textContent = `${totalTime} 分钟`;
        }

        // Calculate books completion progress
        let totalBookPages = 0;
        for (let i = 1; i <= 5; i++) {
            const bookPages = parseInt(localStorage.getItem(`${planId}-book-goal-${i}-pages`)) || 0;
            totalBookPages += bookPages;
        }

        // Update reading progress bar
        const progressBar = getEl('readingProgressBar');
        if (progressBar && totalBookPages > 0) {
            const percentage = Math.min((totalPages / totalBookPages) * 100, 100);
            progressBar.style.width = `${percentage}%`;
        }

        // Update bookshelf if it's expanded
        const bookshelfDrawer = document.querySelector('.bookshelf-drawer');
        if (bookshelfDrawer && bookshelfDrawer.classList.contains('expanded')) {
            updateBookshelf();
        }
    }
    function updateSpeechStats() {
        const planId = getPlanId();
        if (!planId) return;

        let weeklyWarmupCount = 0;
        let weeklyWarmupTime = 0;
        let weeklyStructureCount = 0;
        let weeklyStructureTime = 0;

        let monthlyWarmupCount = 0;
        let monthlyWarmupTime = 0;
        let monthlyStructureCount = 0;
        let monthlyStructureTime = 0;

        // Get current week
        const currentDayIndex = getDayIndexFromDate(getTodayDateString(), getVal('challenge-start-date'));
        const currentWeekIndex = currentDayIndex ? Math.ceil(currentDayIndex / 7) : 1;

        // Calculate weekly stats (current week)
        for (let day = 1; day <= 7; day++) {
            const warmupCheck = localStorage.getItem(`${planId}-speech-warmup-w${currentWeekIndex}-d${day}-check`) === 'true';
            const warmupTime = parseInt(localStorage.getItem(`${planId}-speech-warmup-w${currentWeekIndex}-d${day}-time`)) || 0;
            const structureCheck = localStorage.getItem(`${planId}-speech-structure-w${currentWeekIndex}-d${day}-check`) === 'true';
            const structureTime = parseInt(localStorage.getItem(`${planId}-speech-structure-w${currentWeekIndex}-d${day}-time`)) || 0;

            if (warmupCheck) {
                weeklyWarmupCount++;
                weeklyWarmupTime += warmupTime;
            }
            if (structureCheck) {
                weeklyStructureCount++;
                weeklyStructureTime += structureTime;
            }
        }

        // Calculate monthly stats (all weeks)
        for (let week = 1; week <= 4; week++) {
            for (let day = 1; day <= 7; day++) {
                const warmupCheck = localStorage.getItem(`${planId}-speech-warmup-w${week}-d${day}-check`) === 'true';
                const warmupTime = parseInt(localStorage.getItem(`${planId}-speech-warmup-w${week}-d${day}-time`)) || 0;
                const structureCheck = localStorage.getItem(`${planId}-speech-structure-w${week}-d${day}-check`) === 'true';
                const structureTime = parseInt(localStorage.getItem(`${planId}-speech-structure-w${week}-d${day}-time`)) || 0;

                if (warmupCheck) {
                    monthlyWarmupCount++;
                    monthlyWarmupTime += warmupTime;
                }
                if (structureCheck) {
                    monthlyStructureCount++;
                    monthlyStructureTime += structureTime;
                }
            }
        }

        // Update weekly stats
        const weeklyStatsEl = getEl('speech-stat-week');
        if (weeklyStatsEl) {
            const totalWeeklyCount = weeklyWarmupCount + weeklyStructureCount;
            const totalWeeklyTime = weeklyWarmupTime + weeklyStructureTime;
            weeklyStatsEl.textContent = `${totalWeeklyCount}次 / ${totalWeeklyTime}分钟`;
        }

        // Update monthly stats
        const monthlyStatsEl = getEl('speech-stat-month');
        if (monthlyStatsEl) {
            const totalMonthlyCount = monthlyWarmupCount + monthlyStructureCount;
            const totalMonthlyTime = monthlyWarmupTime + monthlyStructureTime;
            monthlyStatsEl.textContent = `${totalMonthlyCount}次 / ${totalMonthlyTime}分钟`;
        }
    }
    
    function updateLifestyleOverview() {
        let totalSleepMinutes = 0, sleepDays = 0, totalMood = 0, moodDays = 0;
        let reflectionDays = 0, totalIntake = 0, intakeDays = 0, totalDeficit = 0, deficitDays = 0;
        let recentWeight = '--', recentFat = '--';
        const planId = getPlanId();
        if(!planId) return;
        
        for (let day = 1; day <= MONTHLY_DAYS; day++) {
            const sleepStart = localStorage.getItem(`${planId}-life-sleep-start-day${day}`);
            const sleepEnd = localStorage.getItem(`${planId}-life-sleep-end-day${day}`);
            const duration = calculateSleepDurationFromTimes(sleepStart, sleepEnd);
            if(duration > 0) { totalSleepMinutes += duration; sleepDays++; }

            const mood = parseFloat(localStorage.getItem(`${planId}-mood-rating-day${day}`));
            if(mood > 0) { totalMood += mood; moodDays++; }
            
            if(localStorage.getItem(`${planId}-reflection-check-day${day}`) === 'true') reflectionDays++;

            const intake = parseFloat(localStorage.getItem(`${planId}-life-total-intake-day${day}`));
            if (intake > 0) { totalIntake += intake; intakeDays++; }

            const deficit = parseFloat(localStorage.getItem(`${planId}-life-deficit-day${day}`));
            if (deficit) { totalDeficit += deficit; deficitDays++; }

            const weight = localStorage.getItem(`${planId}-life-weight-day${day}`);
            if (weight) recentWeight = weight;
            const fat = localStorage.getItem(`${planId}-life-fat-day${day}`);
            if (fat) recentFat = fat;
        }

        // Update main summary statistics
        getEl('avg-sleep-duration-display').textContent = `${sleepDays > 0 ? (totalSleepMinutes / sleepDays / 60).toFixed(1) : 0}小时`;
        getEl('avg-mood-rating-display').textContent = `${moodDays > 0 ? (totalMood / moodDays).toFixed(1) : 0}分`;
        getEl('reflection-completed-days').textContent = `${reflectionDays}天`;

        // Update overview cards statistics
        const avgSleepHours = sleepDays > 0 ? (totalSleepMinutes / sleepDays / 60).toFixed(1) : 0;
        const avgMoodRating = moodDays > 0 ? (totalMood / moodDays).toFixed(1) : 0;
        const avgIntake = intakeDays > 0 ? Math.round(totalIntake / intakeDays) : 0;
        const avgDeficit = deficitDays > 0 ? Math.round(totalDeficit / deficitDays) : 0;

        // Update overview card elements
        const avgSleepEl = getEl('avg-sleep-duration');
        if (avgSleepEl) avgSleepEl.textContent = `${avgSleepHours}小时`;

        const avgMoodEl = getEl('avg-mood-rating');
        if (avgMoodEl) avgMoodEl.textContent = `${avgMoodRating}分`;

        const avgIntakeEl = getEl('avg-intake');
        if (avgIntakeEl) avgIntakeEl.textContent = `${avgIntake}kcal`;

        const avgDeficitEl = getEl('avg-deficit');
        if (avgDeficitEl) avgDeficitEl.textContent = `${avgDeficit}kcal`;

        const recentWeightEl = getEl('recent-weight');
        if (recentWeightEl) recentWeightEl.textContent = `${recentWeight}kg`;

        const recentFatEl = getEl('recent-fat');
        if (recentFatEl) recentFatEl.textContent = `${recentFat}%`;

        // Update recent sleep and mood
        const recentSleepEl = getEl('recent-sleep');
        if (recentSleepEl) {
            const lastSleepDay = sleepDays > 0 ? MONTHLY_DAYS : 0;
            if (lastSleepDay > 0) {
                const lastSleepStart = localStorage.getItem(`${planId}-life-sleep-start-day${lastSleepDay}`);
                const lastSleepEnd = localStorage.getItem(`${planId}-life-sleep-end-day${lastSleepDay}`);
                const lastDuration = calculateSleepDurationFromTimes(lastSleepStart, lastSleepEnd);
                recentSleepEl.textContent = lastDuration > 0 ? `${(lastDuration/60).toFixed(1)}小时` : '--';
            } else {
                recentSleepEl.textContent = '--';
            }
        }

        const recentMoodEl = getEl('recent-mood');
        if (recentMoodEl) {
            const lastMoodDay = moodDays > 0 ? MONTHLY_DAYS : 0;
            if (lastMoodDay > 0) {
                const lastMood = localStorage.getItem(`${planId}-mood-rating-day${lastMoodDay}`);
                recentMoodEl.textContent = lastMood ? `${lastMood}分` : '--';
            } else {
                recentMoodEl.textContent = '--';
            }
        }

        console.log(`生活统计更新: 睡眠${sleepDays}天, 心情${moodDays}天, 复盘${reflectionDays}天`);
    }

    // --- DATA HANDLING & EVENT LISTENERS ---
    function loadAllData() {
        const planId = getPlanId(); 
        if (!planId) return; 

        habitList = JSON.parse(localStorage.getItem(`${planId}-habits`) || '[]');
        bookData = JSON.parse(localStorage.getItem(`${planId}-bookData`) || '[]');
        
        document.querySelectorAll('.data-input').forEach(input => { 
            if (!input.id) return;
            const key = input.id.includes('-day') || input.classList.contains('profile-input') || input.id.startsWith('book-goal') ? `${planId}-${input.id}` : input.id;
            const value = localStorage.getItem(key); 
            if (value !== null) { 
                input.type === 'checkbox' ? (input.checked = value === 'true') : (input.value = value);
            } else {
                if (input.type === 'checkbox') input.checked = false;
                else if (input.type !== 'submit' && input.type !== 'button') input.value = '';
            }
        }); 

        renderBookList(); 
        updateBookDropdowns(); 
    }

    mainContainer.addEventListener('input', e => { 
        const target = e.target; 
        if (!target.classList.contains('data-input') || !target.id) return; 
        
        const isToday = currentViewDate === getTodayDateString();
        // Allow saving profile info and book goal regardless of date view
        const canSave = isToday || target.classList.contains('profile-input') || target.id.startsWith('book-goal');
        if (!canSave) return;

        const planId = getPlanId(); 
        if (!planId) { 
            if (!getEl('challenge-start-date').dataset.alerted) { 
                alert("请先设置挑战开始日期才能保存数据！"); 
                getEl('challenge-start-date').dataset.alerted = "true"; 
            } 
            return; 
        } 
        
        const valueToSave = target.type === 'checkbox' ? target.checked : target.value;

        // Special handling for life-source data - needs day suffix
        let key;
        if (target.classList.contains('life-source')) {
            const currentDayIndex = getDayIndexFromDate(currentViewDate, getVal('challenge-start-date'));
            if (currentDayIndex) {
                key = `${planId}-${target.id}-day${currentDayIndex}`;
            } else {
                return; // Can't save without valid day index
            }
        } else {
            key = `${planId}-${target.id}`;
        }

        localStorage.setItem(key, valueToSave);

        const dayIndexStr = getDayIndexFromId(target.id);
        const dayIndex = dayIndexStr ? parseInt(dayIndexStr) : null;

        if (target.id === 'challenge-start-date') { initializeApp(); return; }
        if (target.classList.contains('life-source')) {
            const currentDayIndex = getDayIndexFromDate(currentViewDate, getVal('challenge-start-date'));
            if (currentDayIndex) updateLifestyleCalcs(currentDayIndex);
        }
        if (target.id.startsWith('mood-rating')) updateMoodDisplay(target.value);
        if (target.id.startsWith('life-sleep')) {
            const dayIdx = getDayIndexFromDate(currentViewDate, getVal('challenge-start-date'));
            if(dayIdx) {
                const sleepStart = getVal('life-sleep-start');
                const sleepEnd = getVal('life-sleep-end');
                const duration = calculateSleepDurationFromTimes(sleepStart, sleepEnd);
                getEl('life-sleep-duration').textContent = duration > 0 ? `${Math.floor(duration/60)} 小时 ${duration%60} 分钟` : '-- 小时 --';
            }
        }
        
        // Trigger relevant updates
        updateAllStats();
    });

    function updateMoodDisplay(value) {
        const moodSlider = getEl('mood-rating');
        const moodValue = getEl('mood-value');
        const moodEmoji = getEl('mood-emoji');
        if (!moodSlider || !moodValue || !moodEmoji) return;
        
        const v = value || moodSlider.value;
        moodValue.textContent = v;
        const emojis = ['😢', '😞', '😐', '😐', '🙂', '🙂', '😊', '😊', '😄', '🤩'];
        moodEmoji.textContent = emojis[v - 1] || '😐';
        const percentage = (v - 1) / 9 * 100;
        moodSlider.style.background = `linear-gradient(to right, var(--primary-accent) ${percentage}%, var(--bg-secondary) ${percentage}%)`;
    }

    function renderBookList() { /* ... unchanged ... */ }
    function updateBookDropdowns() { /* ... unchanged ... */ }
    function updateReadingProgress() { /* ... unchanged ... */ }

    // --- DATA MIGRATION ---
    function exportAllData() { /* ... unchanged ... */ }
    function importAllData(event) { /* ... unchanged ... */ }

    // --- DATE NAVIGATION & VIEWING LOGIC ---
    function initializeDateNavigation() {
        updateDateDisplay();
        loadDataForDate(currentViewDate);
    
        getEl('prev-day-btn').addEventListener('click', () => {
            const date = new Date(currentViewDate);
            date.setDate(date.getDate() - 1);
            currentViewDate = date.toISOString().split('T')[0];
            loadDataForDate(currentViewDate);
        });
    
        getEl('next-day-btn').addEventListener('click', () => {
            const date = new Date(currentViewDate);
            date.setDate(date.getDate() + 1);
            currentViewDate = date.toISOString().split('T')[0];
            loadDataForDate(currentViewDate);
        });
    
        getEl('today-btn').addEventListener('click', () => { currentViewDate = getTodayDateString(); loadDataForDate(currentViewDate); });
        getEl('yesterday-btn').addEventListener('click', () => { currentViewDate = getDateStringFromDaysAgo(1); loadDataForDate(currentViewDate); });
        getEl('week-ago-btn').addEventListener('click', () => { currentViewDate = getDateStringFromDaysAgo(7); loadDataForDate(currentViewDate); });
        getEl('date-picker').addEventListener('change', (e) => { currentViewDate = e.target.value; loadDataForDate(currentViewDate); });
    }

    function updateDateDisplay() {
        const dateText = getEl('current-date-text');
        const dateSubtitle = getEl('current-date-subtitle');
        const datePicker = getEl('date-picker');
        const today = getTodayDateString();
        const isToday = currentViewDate === today;

        const date = new Date(currentViewDate + 'T00:00:00'); // Avoid timezone issues
        const dateDisplay = `${date.getMonth() + 1}月${date.getDate()}日 ${WEEKDAYS_ZH[date.getDay()]}`;
        
        if (isToday) {
            dateText.textContent = `今天 ${dateDisplay}`;
        } else {
            const diffDays = Math.round((new Date(today) - date) / (1000 * 60 * 60 * 24));
            if (diffDays === 1) dateText.textContent = `昨天 ${dateDisplay}`;
            else if (diffDays > 0) dateText.textContent = `${diffDays}天前 ${dateDisplay}`;
            else dateText.textContent = dateDisplay;
        }

        dateSubtitle.textContent = `${date.getFullYear()}年`;
        datePicker.value = currentViewDate;

        // Update quick action buttons
        document.querySelectorAll('.quick-action-btn').forEach(btn => btn.classList.remove('active'));
        if (isToday) getEl('today-btn').classList.add('active');
        else if (currentViewDate === getDateStringFromDaysAgo(1)) getEl('yesterday-btn').classList.add('active');
        else if (currentViewDate === getDateStringFromDaysAgo(7)) getEl('week-ago-btn').classList.add('active');
    }

    function loadDataForDate(dateStr) {
        updateDateDisplay();
        const isToday = dateStr === getTodayDateString();
        const planId = getPlanId();
        const startDateStr = getVal('challenge-start-date');
        const dayIndex = getDayIndexFromDate(dateStr, startDateStr);

        getEl('smart-data-title').innerHTML = `${isToday ? '📅 今日数据录入' : `📅 查看数据`} (Day <span id="reflection-day-n">${dayIndex || '?'}</span>)`;
        const container = getEl('smart-data-container');
        container.classList.toggle('historical-mode', !isToday);
        container.classList.toggle('today-mode', isToday);

        const inputs = container.querySelectorAll('.data-input.life-source');
        inputs.forEach(input => {
            const key = `${planId}-${input.id}-day${dayIndex}`;
            const value = localStorage.getItem(key);
            
            if (input.type === 'checkbox') {
                input.checked = value === 'true';
            } else {
                input.value = value || '';
            }

            input.readOnly = !isToday;
            input.classList.toggle('readonly-mode', !isToday);
        });

        // Special handling for elements without -day suffix in their ID (the main view)
        const mainViewInputs = getEl('smart-data-container').querySelectorAll('.data-input.life-source');
        mainViewInputs.forEach(input => {
            const key = `${planId}-${input.id}-day${dayIndex}`;
            // If we are in the main view, we need to load data into the elements that don't have dayIndex in their ID
             if (dayIndex) {
                 const value = localStorage.getItem(`${planId}-${input.id}-day${dayIndex}`);
                 if (input.type === 'checkbox') {
                    input.checked = (value === 'true');
                 } else {
                    input.value = value || (input.id === 'mood-rating' ? '5' : '');
                 }
             }

            input.readOnly = !isToday;
            input.classList.toggle('readonly-mode', !isToday);
            if(input.tagName === 'SELECT' || input.type === 'range') {
                input.disabled = !isToday;
            }
        });


        // Update calculated displays
        if (dayIndex) {
            updateLifestyleCalcs(dayIndex);
            const sleepStart = localStorage.getItem(`${planId}-life-sleep-start-day${dayIndex}`);
            const sleepEnd = localStorage.getItem(`${planId}-life-sleep-end-day${dayIndex}`);
            const duration = calculateSleepDurationFromTimes(sleepStart, sleepEnd);
            getEl('life-sleep-duration').textContent = duration > 0 ? `${Math.floor(duration/60)} 小时 ${duration%60} 分` : '-- 小时 --  ?';
            updateMoodDisplay(localStorage.getItem(`${planId}-mood-rating-day${dayIndex}`));
        } else {
             // Clear displays if not in challenge period
            getEl('life-sleep-duration').textContent = '-- 小时 --  ?';
            getEl('life-total-intake').textContent = '0 kcal';
            getEl('life-asylum-burn').textContent = '0';
            getEl('life-bmr').textContent = '0';
            getEl('life-deficit').textContent = '0';
            updateMoodDisplay('5');
        }

        updateMiniCharts(dateStr);
    }
    
    // --- MINI CHARTS for OVERVIEW ---
    function updateMiniCharts(dateStr) {
        const planId = getPlanId();
        if(!planId) return;

        console.log('更新小图表...');

        // Generate data for the last 30 days
        const sleepData = [];
        const moodData = [];
        const weightData = [];
        const calorieData = [];

        for (let day = 1; day <= 30; day++) {
            // Sleep data
            const sleepStart = localStorage.getItem(`${planId}-life-sleep-start-day${day}`);
            const sleepEnd = localStorage.getItem(`${planId}-life-sleep-end-day${day}`);
            const sleepDuration = calculateSleepDurationFromTimes(sleepStart, sleepEnd);
            sleepData.push(sleepDuration > 0 ? sleepDuration / 60 : null); // Convert to hours

            // Mood data
            const mood = parseFloat(localStorage.getItem(`${planId}-mood-rating-day${day}`));
            moodData.push(mood > 0 ? mood : null);

            // Weight data
            const weight = parseFloat(localStorage.getItem(`${planId}-life-weight-day${day}`));
            weightData.push(weight > 0 ? weight : null);

            // Calorie data - calculate total intake
            const breakfast = parseFloat(localStorage.getItem(`${planId}-life-breakfast-day${day}`)) || 0;
            const lunch = parseFloat(localStorage.getItem(`${planId}-life-lunch-day${day}`)) || 0;
            const dinner = parseFloat(localStorage.getItem(`${planId}-life-dinner-day${day}`)) || 0;
            const totalIntake = breakfast + lunch + dinner;
            calorieData.push(totalIntake > 0 ? totalIntake : null);
        }

        console.log(`图表数据: 睡眠${sleepData.filter(d => d !== null).length}天, 心情${moodData.filter(d => d !== null).length}天, 体重${weightData.filter(d => d !== null).length}天, 卡路里${calorieData.filter(d => d !== null).length}天`);

        // Use fallback colors if CSS variables are not available
        const sleepColor = getComputedStyle(document.documentElement).getPropertyValue('--sleep-color').trim() || '#4CAF50';
        const moodColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-accent').trim() || '#ff9500';
        const weightColor = getComputedStyle(document.documentElement).getPropertyValue('--success-color').trim() || '#2196F3';
        const calorieColor = getComputedStyle(document.documentElement).getPropertyValue('--failure-color').trim() || '#FF5722';

        drawMiniChart('sleep-mini-chart', '睡眠', sleepData, sleepColor);
        drawMiniChart('mood-mini-chart', '心情', moodData, moodColor);
        drawMiniChart('weight-mini-chart', '体重', weightData, weightColor);
        drawMiniChart('calorie-mini-chart', '摄入', calorieData, calorieColor);
    }

    function drawMiniChart(canvasId, label, data, color) {
        const ctx = getEl(canvasId)?.getContext('2d');
        if(!ctx) return;

        const chartInstance = Chart.getChart(canvasId);
        if (chartInstance) chartInstance.destroy();
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map((_, i) => i),
                datasets: [{ label, data, borderColor: color, borderWidth: 2, pointRadius: 0, tension: 0.4, fill: { target: 'origin', above: `${color}20` } }]
            },
            options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: false }, y: { display: false } }, plugins: { legend: { display: false }, tooltip: { enabled: false } } }
        });
    }

    // --- MODAL LOGIC ---
    window.openDataModal = function(dataType) {
        const modal = getEl('data-modal');
        modal.style.display = 'flex';
        const modalTitle = getEl('modal-title');
        const tableContainer = getEl('modal-table-container');
        const titles = { sleep: '😴 睡眠数据详情', mood: '😊 心情数据详情', body: '⚖️ 身体数据详情', nutrition: '🍽️ 营养数据详情' };
        modalTitle.textContent = titles[dataType] || '数据详情';
        
        tableContainer.innerHTML = generateModalTable(dataType);
        drawModalChart(dataType);
    };

    window.closeDataModal = function() { getEl('data-modal').style.display = 'none'; };
    
    getEl('data-modal').addEventListener('click', (e) => { if (e.target.classList.contains('modal-overlay')) closeDataModal(); });
    
    function generateModalTable(dataType) {
        const headers = {
            sleep: ['日期', '睡觉时间', '起床时间', '睡眠时长', '心情'],
            mood: ['日期', '心情评分', '今日反思', '感恩记录'],
            body: ['日期', '体重(kg)', '体脂(%)', '身体状态'],
            nutrition: ['日期', '早餐(kcal)', '午餐(kcal)', '晚餐(kcal)', '总摄入(kcal)']
        };

        const planId = getPlanId();
        let bodyHtml = '';
        let hasData = false;

        // Generate data for 30 days (day 1 to 30)
        for (let day = 1; day <= 30; day++) {
            let rowData = [];

            // Create date display
            const startDate = new Date(localStorage.getItem('challenge-start-date'));
            const currentDate = new Date(startDate);
            currentDate.setDate(startDate.getDate() + day - 1);
            const dateStr = currentDate.toISOString().split('T')[0];
            const displayDate = `${dateStr.substring(5)} (${WEEKDAYS_ZH[currentDate.getDay()]})`;

            if (dataType === 'sleep') {
                const start = localStorage.getItem(`${planId}-life-sleep-start-day${day}`) || '--';
                const end = localStorage.getItem(`${planId}-life-sleep-end-day${day}`) || '--';
                const duration = calculateSleepDurationFromTimes(start, end);
                const mood = localStorage.getItem(`${planId}-mood-rating-day${day}`) || '--';
                if(start !== '--' || end !== '--' || mood !== '--') {
                    hasData = true;
                    rowData = [displayDate, start, end, duration > 0 ? `${Math.floor(duration/60)}小时${duration%60}分钟` : '--', mood + '分'];
                }
            } else if (dataType === 'mood') {
                const mood = localStorage.getItem(`${planId}-mood-rating-day${day}`) || '--';
                const reflection = localStorage.getItem(`${planId}-life-reflection-day${day}`) || '--';
                const gratitude = localStorage.getItem(`${planId}-life-gratitude-day${day}`) || '--';
                if(mood !== '--' || reflection !== '--' || gratitude !== '--') {
                    hasData = true;
                    rowData = [displayDate, mood !== '--' ? mood + '分' : '--', reflection, gratitude];
                }
            } else if (dataType === 'body') {
                const weight = localStorage.getItem(`${planId}-life-weight-day${day}`) || '--';
                const fat = localStorage.getItem(`${planId}-life-fat-day${day}`) || '--';
                const status = localStorage.getItem(`${planId}-life-body-status-day${day}`) || '--';
                if(weight !== '--' || fat !== '--' || status !== '--') {
                    hasData = true;
                    rowData = [displayDate, weight !== '--' ? weight + 'kg' : '--', fat !== '--' ? fat + '%' : '--', status];
                }
            } else if (dataType === 'nutrition') {
                const breakfast = localStorage.getItem(`${planId}-life-breakfast-day${day}`) || '--';
                const lunch = localStorage.getItem(`${planId}-life-lunch-day${day}`) || '--';
                const dinner = localStorage.getItem(`${planId}-life-dinner-day${day}`) || '--';
                if(breakfast !== '--' || lunch !== '--' || dinner !== '--') {
                    hasData = true;
                    const total = (parseInt(breakfast) || 0) + (parseInt(lunch) || 0) + (parseInt(dinner) || 0);
                    rowData = [displayDate, breakfast, lunch, dinner, total > 0 ? total : '--'];
                }
            }

            if(rowData.length > 0) {
                bodyHtml += `<tr>${rowData.map(td => `<td>${td}</td>`).join('')}</tr>`;
            }
        }

        if(!hasData) return '<p style="text-align:center; padding: 2rem; color: #666;">最近30天内没有相关数据</p>';

        return `<table class="modal-table"><thead><tr>${headers[dataType].map(th => `<th>${th}</th>`).join('')}</tr></thead><tbody>${bodyHtml}</tbody></table>`;
    }

    function drawModalChart(dataType) {
        const canvas = getEl('modal-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const planId = getPlanId();
        if (!planId) return;

        // 销毁现有图表
        if (window.modalChart) {
            window.modalChart.destroy();
        }

        // 收集数据
        const data = [];
        const labels = [];

        for (let day = 1; day <= 30; day++) {
            const dateStr = `第${day}天`;
            labels.push(dateStr);

            let value = null;
            switch(dataType) {
                case 'sleep':
                    const sleepDuration = localStorage.getItem(`${planId}-sleep-duration-day${day}`);
                    value = sleepDuration ? parseInt(sleepDuration) / 60 : null; // 转换为小时
                    break;
                case 'mood':
                    const moodRating = localStorage.getItem(`${planId}-mood-rating-day${day}`);
                    value = moodRating ? parseInt(moodRating) : null;
                    break;
                case 'body':
                    const weight = localStorage.getItem(`${planId}-weight-day${day}`);
                    value = weight ? parseFloat(weight) : null;
                    break;
                case 'nutrition':
                    const calories = localStorage.getItem(`${planId}-calories-day${day}`);
                    value = calories ? parseInt(calories) : null;
                    break;
            }
            data.push(value);
        }

        // 过滤掉空值，只显示有数据的点
        const filteredData = [];
        const filteredLabels = [];
        data.forEach((value, index) => {
            if (value !== null) {
                filteredData.push(value);
                filteredLabels.push(labels[index]);
            }
        });

        if (filteredData.length === 0) {
            // 如果没有数据，显示提示信息
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }

        // 创建图表
        const chartConfig = {
            type: 'line',
            data: {
                labels: filteredLabels,
                datasets: [{
                    label: getChartLabel(dataType),
                    data: filteredData,
                    borderColor: '#ff9500',
                    backgroundColor: 'rgba(255, 149, 0, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: getYAxisLabel(dataType)
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    }
                }
            }
        };

        window.modalChart = new Chart(ctx, chartConfig);
    }

    function getChartLabel(dataType) {
        const labels = {
            sleep: '睡眠时长',
            mood: '心情评分',
            body: '体重',
            nutrition: '卡路里摄入'
        };
        return labels[dataType] || '数据';
    }

    function getYAxisLabel(dataType) {
        const labels = {
            sleep: '小时',
            mood: '评分',
            body: 'kg',
            nutrition: 'kcal'
        };
        return labels[dataType] || '';
    }

    // --- TEMPLATE MANAGEMENT ---
    const TEMPLATES_STORAGE_KEY = 'growth-plan-templates';

    // 获取所有需要保存到模板的数据源输入框
    function getTemplateDataSourceInputs() {
        // 我们需要收集所有模块的数据，而不仅仅是生活与复盘页
        return document.querySelectorAll('.asylum-source, .life-source, .eng-source, .med-source, .read-log-source, .speech-source');
    }

    function saveDayAsTemplate() {
        const templateName = getVal('template-name-input').trim();
        if (!templateName) {
            alert('请输入模板名称！');
            return;
        }

        const dayIndex = getDayIndexFromDate(currentViewDate, getVal('challenge-start-date'));
        if (!dayIndex) {
            alert('无法获取当前日期索引，请确保已设置挑战开始日期。');
            return;
        }

        const planId = getPlanId();
        const templateData = {};
        const allInputs = getTemplateDataSourceInputs();

        allInputs.forEach(input => {
            const daySpecificId = input.id.replace(/-day\d+/, ''); // 移除-dayX后缀，得到通用ID
            const key = `${planId}-${daySpecificId}-day${dayIndex}`;
            const value = localStorage.getItem(key);
            if (value !== null) {
                templateData[daySpecificId] = value;
            }
        });

        const templates = JSON.parse(localStorage.getItem(TEMPLATES_STORAGE_KEY) || '{}');
        templates[templateName] = templateData;
        localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(templates));

        alert(`模板 "${templateName}" 已保存！`);
        getEl('template-name-input').value = '';
        loadTemplateDropdown(); // 刷新下拉菜单
    }

    function loadTemplateDropdown() {
        const selectEl = getEl('template-select');
        if (!selectEl) return;

        const templates = JSON.parse(localStorage.getItem(TEMPLATES_STORAGE_KEY) || '{}');
        const templateNames = Object.keys(templates);

        selectEl.innerHTML = '<option value="">选择一个模板...</option>'; // 默认选项

        templateNames.forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            selectEl.appendChild(option);
        });
    }

    function applyTemplate() {
        const templateName = getVal('template-select');
        if (!templateName) {
            alert('请先选择一个模板！');
            return;
        }

        const dayIndex = getDayIndexFromDate(currentViewDate, getVal('challenge-start-date'));
        if (!dayIndex) {
            alert('无法应用模板，无效的日期。');
            return;
        }

        const planId = getPlanId();
        const templates = JSON.parse(localStorage.getItem(TEMPLATES_STORAGE_KEY) || '{}');
        const templateData = templates[templateName];

        if (!templateData) {
            alert('找不到该模板的数据！');
            return;
        }

        if (!confirm(`确定要将模板 "${templateName}" 的数据应用到 ${currentViewDate} 吗？这会覆盖当天的数据。`)) {
            return;
        }

        for (const genericId in templateData) {
            const value = templateData[genericId];
            const key = `${planId}-${genericId}-day${dayIndex}`;
            localStorage.setItem(key, value);
        }

        // 刷新当前视图以显示新数据
        loadDataForDate(currentViewDate);
        // 重新计算所有统计数据
        updateAllStats();

        alert(`模板 "${templateName}" 已成功应用！`);
    }

    // --- QUICK COMMAND SYSTEM ---
    function parseCommand(commandStr) {
        const command = commandStr.trim().toLowerCase();
        const parts = command.split(/\s+/); // 按空格分割
        const action = parts[0];

        const data = { action: action, params: {} };
        let restParts = parts.slice(1);

        // 提取带单位的参数 (e.g., 30m, 20p, 50c)
        const extractParam = (unit) => {
            const index = restParts.findIndex(p => p.endsWith(unit));
            if (index > -1) {
                const value = parseInt(restParts[index].replace(unit, ''));
                if (!isNaN(value)) {
                    restParts.splice(index, 1); // 从数组中移除已解析的参数
                    return value;
                }
            }
            return null;
        };

        data.params.time = extractParam('m');
        data.params.pages = extractParam('p');
        data.params.words = extractParam('c');

        // 根据action处理剩余部分
        switch (action) {
            case '习惯':
            case 'habit':
                data.params.name = restParts.join(' ');
                break;
            case '读书':
            case 'read':
                // 剩余部分是书名
                data.params.bookTitle = restParts.join(' ');
                break;
            case '演讲':
            case 'speech':
                data.params.theme = restParts.join(' ');
                break;
            case '英语':
            case 'english':
                // 单词数可能不带单位，作为第一个数字
                if (data.params.words === null && !isNaN(parseInt(restParts[0]))) {
                    data.params.words = parseInt(restParts[0]);
                }
                break;
            case '冥想':
            case 'meditation':
                // 无需额外参数
                break;
            case 'asylum':
                // 无需额外参数
                break;
            default:
                return { error: '无法识别的指令动作' };
        }

        return data;
    }

    async function executeCommand() {
        const inputEl = getEl('quick-command-input');
        const commandStr = inputEl.value;
        if (!commandStr) return;

        const result = parseCommand(commandStr);

        if (result.error) {
            alert(`错误: ${result.error}`);
            return;
        }

        const planId = getPlanId();
        const todayIndex = getDayIndexFromDate(getTodayDateString(), getVal('challenge-start-date'));

        if (!planId || !todayIndex) {
            alert('错误：无法定位今天的数据，请检查挑战开始日期。');
            return;
        }

        let successMessage = '';

        // ---- 执行逻辑 ----
        switch (result.action) {
            case '习惯':
            case 'habit':
                // 这是一个简化版，直接增加总次数和总时长。
                // 注意：这与 M3 的精细化记录是不同的，我们先实现简单的。
                const habit = habitList.find(h => h.name.toLowerCase() === result.params.name.toLowerCase());
                if (!habit) {
                    alert(`错误：找不到名为 "${result.params.name}" 的习惯。请先在习惯模块添加。`);
                    return;
                }
                const countKey = `${planId}-habit-h${habit.id}-d${todayIndex}-count`;
                const timeKey = `${planId}-habit-h${habit.id}-d${todayIndex}-time`;
                const oldCount = parseInt(localStorage.getItem(countKey) || '0');
                const oldTime = parseInt(localStorage.getItem(timeKey) || '0');
                localStorage.setItem(countKey, oldCount + 1);
                localStorage.setItem(timeKey, oldTime + (result.params.time || 0));
                successMessage = `已记录习惯 '${habit.name}' 1次，时长 ${result.params.time || 0}分钟。`;
                break;

            case '读书':
            case 'read':
                // 简化：我们不通过书名查找，而是直接累加到当天的记录中。
                // 提示用户后续手动选择书名。
                const pagesKey = `${planId}-read-pages-day${todayIndex}`;
                const readTimeKey = `${planId}-read-time-day${todayIndex}`;
                const oldPages = parseInt(localStorage.getItem(pagesKey) || '0');
                const oldReadTime = parseInt(localStorage.getItem(readTimeKey) || '0');
                localStorage.setItem(pagesKey, oldPages + (result.params.pages || 0));
                localStorage.setItem(readTimeKey, oldReadTime + (result.params.time || 0));
                successMessage = `已记录阅读 ${result.params.pages || 0}页，时长 ${result.params.time || 0}分钟。请记得在阅读模块选择书名。`;
                break;

            case 'asylum':
                localStorage.setItem(`${planId}-asylum-check-day${todayIndex}`, 'true');
                // 可以在此自动填充默认值
                successMessage = `已将今天的Asylum标记为完成！`;
                break;

            case '英语':
            case 'english':
                const engWordsKey = `${planId}-eng-words-day${todayIndex}`;
                const engTimeKey = `${planId}-eng-time-day${todayIndex}`;
                const oldEngWords = parseInt(localStorage.getItem(engWordsKey) || '0');
                const oldEngTime = parseInt(localStorage.getItem(engTimeKey) || '0');
                localStorage.setItem(engWordsKey, oldEngWords + (result.params.words || 0));
                localStorage.setItem(engTimeKey, oldEngTime + (result.params.time || 0));
                successMessage = `已记录英语学习 ${result.params.words || 0}个单词，时长 ${result.params.time || 0}分钟。`;
                break;

            case '冥想':
            case 'meditation':
                const medTimeKey = `${planId}-med-time-day${todayIndex}`;
                const oldMedTime = parseInt(localStorage.getItem(medTimeKey) || '0');
                localStorage.setItem(medTimeKey, oldMedTime + (result.params.time || 0));
                successMessage = `已记录冥想时长 ${result.params.time || 0}分钟。`;
                break;

            case '演讲':
            case 'speech':
                const speechTimeKey = `${planId}-speech-time-day${todayIndex}`;
                const speechThemeKey = `${planId}-speech-theme-day${todayIndex}`;
                const oldSpeechTime = parseInt(localStorage.getItem(speechTimeKey) || '0');
                localStorage.setItem(speechTimeKey, oldSpeechTime + (result.params.time || 0));
                if (result.params.theme) {
                    localStorage.setItem(speechThemeKey, result.params.theme);
                }
                successMessage = `已记录演讲练习时长 ${result.params.time || 0}分钟${result.params.theme ? '，主题：' + result.params.theme : ''}。`;
                break;

            default:
                alert(`指令 "${result.action}" 的执行逻辑尚未实现。`);
                return;
        }

        // 清空输入框并给出反馈
        inputEl.value = '';
        alert(successMessage);

        // 刷新数据
        if (currentViewDate === getTodayDateString()) {
            loadDataForDate(currentViewDate);
        }
        updateAllStats();
    }

    // --- AUTOMATIC INSIGHTS ANALYSIS FUNCTIONS ---

    // 1. 定义洞察任务列表
    const INSIGHT_TASKS = [
        {
            title: '睡眠时长 vs. 次日心情',
            xKey: 'sleep_duration',
            yKey: 'next_day_mood_rating',
            insightGenerator: (data, correlation) => {
                if (correlation > 0.4) {
                    const goodSleepThreshold = 7.5; // 假设7.5小时是良好睡眠
                    const moodWithGoodSleep = data.filter(p => p.x >= goodSleepThreshold).map(p => p.y);
                    const moodWithBadSleep = data.filter(p => p.x < goodSleepThreshold).map(p => p.y);
                    if (moodWithGoodSleep.length > 2 && moodWithBadSleep.length > 2) {
                        const avgMoodGood = (moodWithGoodSleep.reduce((a, b) => a + b, 0) / moodWithGoodSleep.length).toFixed(1);
                        const avgMoodBad = (moodWithBadSleep.reduce((a, b) => a + b, 0) / moodWithBadSleep.length).toFixed(1);
                        return `数据显示，当睡眠超过 <strong>${goodSleepThreshold}</strong> 小时，您的次日平均心情从 <strong>${avgMoodBad}</strong> 分提升至 <strong>${avgMoodGood}</strong> 分。`;
                    }
                }
                return '充足的睡眠似乎能改善您的心情。';
            }
        },
        {
            title: 'Asylum训练 vs. 不良习惯时长',
            xKey: 'asylum_time',
            yKey: 'total_habit_time',
            insightGenerator: (data, correlation) => {
                if (correlation < -0.4) {
                    return '高强度的Asylum训练日，您花在不良习惯上的时间显著减少。这可能是精疲力竭或自律性增强的结果。';
                }
                return '运动与不良习惯之间的时间占用存在一定的替代关系。';
            }
        },
        {
            title: '英语学习 vs. 心情评分',
            xKey: 'english_time',
            yKey: 'mood_rating',
            insightGenerator: (data, correlation) => {
                if (correlation > 0.3) {
                    return '学习英语似乎能提升您的心情，知识的获得带来了满足感。';
                } else if (correlation < -0.3) {
                    return '英语学习可能在某些时候给您带来压力，建议调整学习方式。';
                }
                return '英语学习与心情之间存在微妙的关系。';
            }
        }
    ];

    function getCorrelationData(metricXKey, metricYKey, days = 30) {
        const planId = getPlanId();
        if (!planId) return [];

        const dataPoints = [];

        // 获取指标定义
        let metricX = INSIGHTS_METRICS[metricXKey];
        let metricY = INSIGHTS_METRICS[metricYKey];

        // 处理习惯指标
        if (!metricX && metricXKey.startsWith('habit_')) {
            const parts = metricXKey.split('_');
            const habitId = parts[2];
            const type = parts[1]; // 'time' or 'count'
            metricX = { getter: (pId, day) => localStorage.getItem(`${pId}-habit-h${habitId}-d${day}-${type}`) };
        }

        if (!metricY && metricYKey.startsWith('habit_')) {
            const parts = metricYKey.split('_');
            const habitId = parts[2];
            const type = parts[1]; // 'time' or 'count'
            metricY = { getter: (pId, day) => localStorage.getItem(`${pId}-habit-h${habitId}-d${day}-${type}`) };
        }

        if (!metricX || !metricY) return [];

        for (let day = 1; day <= days; day++) {
            const xVal = metricX.getter(planId, day);
            const yVal = metricY.getter(planId, day);

            // 确保两个值都存在且是有效数字
            if (xVal !== null && yVal !== null && xVal !== '' && yVal !== '') {
                const x = parseFloat(xVal);
                const y = parseFloat(yVal);
                if (!isNaN(x) && !isNaN(y)) {
                    dataPoints.push({ x, y });
                }
            }
        }
        return dataPoints;
    }

    // 2. 增强指标Getters - 保持 getCorrelationData 函数不变，它仍然有用

    // 3. 编写主执行函数
    async function runInsightsAnalysis() {
        console.log('🔍 开始运行洞察分析...');
        const container = getEl('insights-report-container');
        const loadingEl = getEl('insights-loading');
        if (!container) {
            console.error('❌ 找不到洞察容器元素');
            return;
        }

        // 显示加载动画
        loadingEl.style.display = 'block';
        // 清空旧报告
        container.querySelectorAll('.insight-card').forEach(card => card.remove());

        let generatedInsightsCount = 0;

        for (const task of INSIGHT_TASKS) {
            console.log(`📊 分析任务: ${task.title}`);
            // 注意：getCorrelationData 函数保持不变
            const data = getCorrelationData(task.xKey, task.yKey, 30);
            const n = data.length;
            console.log(`📈 数据点数量: ${n}`);

            if (n < 5) {
                console.log(`⚠️ 数据不足，跳过 ${task.title}`);
                continue; // 数据太少，跳过此项分析
            }

            // 注意：calculateCorrelation 函数现在只返回correlation值
            const correlation = calculateCorrelation(data);
            console.log(`🔗 相关系数: ${correlation.toFixed(3)}`);

            // 只显示相关性较强的洞察（临时降低阈值用于调试）
            if (Math.abs(correlation) > 0.1) {
                console.log(`✅ 发现强关联，生成洞察卡片: ${task.title}`);
                generatedInsightsCount++;
                const insightText = task.insightGenerator(data, correlation);
                const card = createInsightCard(task.title, `chart-${task.xKey}-${task.yKey}`, insightText);
                container.appendChild(card);
                // 异步绘制图表
                setTimeout(() => {
                    // 特殊处理 "睡眠 vs 心情" 的图表数据
                    if (task.xKey === 'sleep_duration') {
                        const goodSleepThreshold = 7.5;
                        const goodSleepData = data.filter(p => p.x >= goodSleepThreshold);
                        const badSleepData = data.filter(p => p.x < goodSleepThreshold);

                        const datasets = [
                            {
                                label: `睡眠不足 (< ${goodSleepThreshold}h)`,
                                data: badSleepData,
                                backgroundColor: 'rgba(239, 68, 68, 0.6)', // 红色
                                pointRadius: 6,
                                pointHoverRadius: 8
                            },
                            {
                                label: `睡眠充足 (>= ${goodSleepThreshold}h)`,
                                data: goodSleepData,
                                backgroundColor: 'rgba(34, 197, 94, 0.6)', // 绿色
                                pointRadius: 6,
                                pointHoverRadius: 8
                            }
                        ];
                        renderInsightChart(datasets, `chart-${task.xKey}-${task.yKey}`, task.xKey, task.yKey);

                    } else {
                        // 对于其他图表，保持原来的单数据集+趋势线逻辑
                        const datasetWithTrendline = [{
                            label: '数据点',
                            data: data,
                            backgroundColor: 'rgba(255, 149, 0, 0.6)',
                            pointRadius: 6,
                            pointHoverRadius: 8,
                            regression: {
                                type: 'linear',
                                color: 'rgba(239, 68, 68, 0.8)',
                                width: 2,
                                label: '趋势线'
                            }
                        }];
                        renderInsightChart(datasetWithTrendline, `chart-${task.xKey}-${task.yKey}`, task.xKey, task.yKey);
                    }
                }, 0);
            } else {
                console.log(`❌ 相关性不足，跳过 ${task.title}`);
            }
        }

        // 隐藏加载动画
        loadingEl.style.display = 'none';

        console.log(`🎯 总共生成了 ${generatedInsightsCount} 个洞察`);
        if (generatedInsightsCount === 0) {
            // 添加调试信息
            console.log('🔍 调试信息：');
            console.log('- INSIGHT_TASKS 长度:', INSIGHT_TASKS.length);
            console.log('- habitList 长度:', habitList.length);
            console.log('- 当前计划ID:', getPlanId());

            container.innerHTML += '<p style="grid-column: 1 / -1; text-align: center;">暂未发现显著的数据关联。随着数据增多，洞察会自动出现。</p>';
        }
    }

    // 4. 辅助函数
    function createInsightCard(title, canvasId, insightText) {
        const card = document.createElement('div');
        card.className = 'insight-card chart-container'; // 复用样式
        card.innerHTML = `
            <h3 style="margin-bottom: 1.5rem;">${title}</h3>
            <div style="height: 250px; margin-bottom: 1.5rem;"><canvas id="${canvasId}"></canvas></div>
            <p class="instructions" style="margin:0;">${insightText}</p>
        `;
        return card;
    }

    function renderInsightChart(datasets, canvasId, xKey, yKey) { // 参数从 data 变为 datasets
        const ctx = getEl(canvasId)?.getContext('2d');
        if (!ctx) return;

        const xLabel = INSIGHTS_METRICS[xKey]?.label || xKey;
        const yLabel = INSIGHTS_METRICS[yKey]?.label || yKey;

        // 销毁可能存在的旧图表实例
        const existingChart = Chart.getChart(ctx);
        if (existingChart) {
            existingChart.destroy();
        }

        new Chart(ctx, {
            type: 'scatter',
            data: { datasets: datasets }, // 直接使用传入的 datasets
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    x: { title: { display: true, text: xLabel } },
                    y: { title: { display: true, text: yLabel } }
                }
            }
        });
    }

    // 重构 analyzeAndDisplayInsight 为一个纯计算函数
    function calculateCorrelation(data) {
        const n = data.length;
        if (n === 0) return 0;

        const sumX = data.reduce((a, b) => a + b.x, 0);
        const sumY = data.reduce((a, b) => a + b.y, 0);
        const sumXY = data.reduce((a, b) => a + b.x * b.y, 0);
        const sumX2 = data.reduce((a, b) => a + b.x * b.x, 0);
        const sumY2 = data.reduce((a, b) => a + b.y * b.y, 0);

        const numerator = n * sumXY - sumX * sumY;
        const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
        return denominator === 0 ? 0 : numerator / denominator;
    }

    function generateWeeklyAnalysis() {
        const contentEl = getEl('weekly-analysis-content');
        if (!contentEl) return;

        const today = new Date();
        const dayOfWeek = today.getDay(); // 0 for Sunday, 1 for Monday, etc.
        const todayIndex = getDayIndexFromDate(getTodayDateString(), getVal('challenge-start-date'));

        if (!todayIndex || todayIndex < 8) { // 需要至少完整一周的数据
            contentEl.innerHTML = '<p>数据不足，完成第一个完整的星期后将为您生成周度分析。</p>';
            return;
        }

        // --- 1. 数据聚合 ---
        const positiveActivities = ['Asylum', '英语', '冥想', '阅读', '演讲'];
        const thisWeekData = { totalPositiveTime: 0, sleepTimes: [] };
        const lastWeekData = { totalPositiveTime: 0, sleepTimes: [] };
        const changes = {};

        for (let i = 0; i < 7; i++) {
            const thisWeekDayIndex = todayIndex - dayOfWeek + i;
            const lastWeekDayIndex = thisWeekDayIndex - 7;

            if (thisWeekDayIndex > 0) {
                const thisWeekDayData = getTrackedTimeData(thisWeekDayIndex);
                positiveActivities.forEach(act => {
                    thisWeekData.totalPositiveTime += thisWeekDayData[act] || 0;
                });
                thisWeekData.sleepTimes.push(localStorage.getItem(`${getPlanId()}-life-sleep-start-day${thisWeekDayIndex}`));

                if (lastWeekDayIndex > 0) {
                    const lastWeekDayData = getTrackedTimeData(lastWeekDayIndex);
                    positiveActivities.forEach(act => {
                        lastWeekData.totalPositiveTime += lastWeekDayData[act] || 0;
                    });
                    lastWeekData.sleepTimes.push(localStorage.getItem(`${getPlanId()}-life-sleep-start-day${lastWeekDayIndex}`));

                    // 计算每项的变化
                    Object.keys(thisWeekDayData).forEach(key => {
                        if (!changes[key]) changes[key] = 0;
                        changes[key] += (thisWeekDayData[key] || 0) - (lastWeekDayData[key] || 0);
                    });
                }
            }
        }

        // --- 2. 生成分析洞察 ---
        let insights = [];

        // Insight 1: 总体趋势
        const totalChange = thisWeekData.totalPositiveTime - lastWeekData.totalPositiveTime;
        if (Math.abs(totalChange) > 30) { // 变化超过半小时才有意义
            const trend = totalChange > 0 ? '增加' : '减少';
            const color = totalChange > 0 ? 'var(--success-color)' : 'var(--failure-color)';
            insights.push(`本周您在积极活动上的总投入时间为 <strong>${(thisWeekData.totalPositiveTime/60).toFixed(1)}</strong> 小时，比上周<span style="color:${color}; font-weight:700;">${trend}了 ${(Math.abs(totalChange)/60).toFixed(1)} 小时</span>。`);
        }

        // Insight 2: 关键进步
        let maxPositiveChange = { key: '', value: 0 };
        positiveActivities.forEach(key => {
            if (changes[key] > maxPositiveChange.value) {
                maxPositiveChange = { key, value: changes[key] };
            }
        });
        if (maxPositiveChange.value > 20) {
            insights.push(`👏 最大的进步来自于 <strong>${maxPositiveChange.key}</strong>，本周投入时间增加了 <strong>${maxPositiveChange.value}</strong> 分钟。`);
        }

        // Insight 3: 关键退步 (不良习惯)
        let maxNegativeChange = { key: '', value: 0 };
        const habitKey = '习惯';
        if (changes[habitKey] > 20) { // 不良习惯时间增加是坏事
            insights.push(`⚠️ 需要注意！本周在 <strong>不良习惯</strong> 上的时间消耗增加了 <strong>${changes[habitKey]}</strong> 分钟。`);
        }

        // Insight 4: 睡眠规律性
        const calculateSleepStdDev = (times) => {
            const validTimes = times.filter(Boolean).map(t => {
                const [h, m] = t.split(':').map(Number);
                return h * 60 + m;
            });
            if (validTimes.length < 3) return null;
            const mean = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
            const stdDev = Math.sqrt(validTimes.map(t => (t - mean) ** 2).reduce((a, b) => a + b, 0) / validTimes.length);
            return stdDev;
        };
        const thisWeekSleepStdDev = calculateSleepStdDev(thisWeekData.sleepTimes);
        if (thisWeekSleepStdDev !== null) {
            let consistencyText = '';
            if (thisWeekSleepStdDev < 15) consistencyText = '非常规律，堪称典范！';
            else if (thisWeekSleepStdDev < 30) consistencyText = '比较规律，继续保持。';
            else consistencyText = '波动较大，规律的作息是高效的基础。';
            insights.push(`您的入睡时间一致性评估：<strong>${consistencyText}</strong> (波动标准差: ${thisWeekSleepStdDev.toFixed(0)}分钟)`);
        }

        // --- 3. 渲染结果 ---
        if (insights.length > 0) {
            contentEl.innerHTML = '<ul style="list-style-type: none; padding-left: 0; margin: 0; display: flex; flex-direction: column; gap: 1rem;">' +
                insights.map(text => `<li style="line-height: 1.6;">- ${text}</li>`).join('') +
                '</ul>';
        } else {
            contentEl.innerHTML = '<p>本周数据平稳，未发现显著变化。继续努力！</p>';
        }
    }

    /**
     * 通用的预测计算函数
     * @param {number} totalGoal - 总目标量 (例如 90天 或 5000单词)
     * @param {number} currentProgress - 当前已完成量
     * @param {string} startDateString - 挑战开始日期字符串 'YYYY-MM-DD'
     * @returns {object|null} - 返回包含预测结果的对象，或在无法预测时返回null
     */
    function calculatePrediction(totalGoal, currentProgress, startDateString) {
        if (currentProgress <= 0 || !startDateString) {
            return null; // 无法预测
        }

        const startDate = new Date(startDateString);
        const today = new Date();
        // 计算已用天数（确保至少为1天，避免除零）
        const daysUsed = Math.max(1, Math.floor((today - startDate) / (1000 * 60 * 60 * 24)) + 1);

        // 1. 计算平均每日进度
        const avgDailyProgress = currentProgress / daysUsed;

        if (avgDailyProgress <= 0) {
            return null;
        }

        // 2. 计算剩余需要量
        const remainingGoal = totalGoal - currentProgress;

        // 3. 预测剩余所需天数
        const remainingDaysNeeded = Math.ceil(remainingGoal / avgDailyProgress);

        // 4. 预测总共需要的天数 (从开始日期算起)
        const totalDaysNeeded = daysUsed + remainingDaysNeeded;

        // 5. 预测完成日期
        const predictedCompletionDate = new Date(startDateString);
        predictedCompletionDate.setDate(predictedCompletionDate.getDate() + totalDaysNeeded - 1);

        return {
            avgDailyProgress: avgDailyProgress.toFixed(2),
            remainingDaysNeeded: remainingDaysNeeded,
            predictedCompletionDate: predictedCompletionDate.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' })
        };
    }



    function setupQuickCommand() {
        getEl('execute-command-btn')?.addEventListener('click', executeCommand);
        getEl('quick-command-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                executeCommand();
            }
        });
    }

    // 设置模板管理器的事件监听器
    function setupTemplateManager() {
        getEl('save-template-btn')?.addEventListener('click', saveDayAsTemplate);
        getEl('apply-template-btn')?.addEventListener('click', applyTemplate);
        loadTemplateDropdown();
    }

    // --- APP INITIALIZATION ---
    function initializeApp() {
        getEl('speech-weekly-tracker').innerHTML = '';
        generateAsylumTable();
        generateEnglishTable();
        generateMeditationTable();
        generateReadingTable();
        generateAnalysisTable();
        
        setupTabSwitching();
        setupMigrationButtons();
        setupAnimations();
        setupGuitarFunctionality();
        
        const startDateEl = getEl('challenge-start-date');
        // Check if there's a test start date from the test data generator
        const testStartDate = sessionStorage.getItem('test-start-date') || localStorage.getItem('challenge-start-date');
        if (testStartDate && !startDateEl.value) {
            startDateEl.value = testStartDate;
        } else if (!startDateEl.value) {
            startDateEl.value = getTodayDateString();
        }
        
        updateDynamicUI(); // Load data first
        renderHabitTracker(); // Then render with loaded data
        initializeHabitManager(); // Initialize habit management
        loadFinanceData(); // Load finance data
        initializeDateNavigation(); // This will also call loadDataForDate and update UI

        if (localStorage.getItem('charts-open') === 'true') getEl('lifestyle-chart-container').classList.add('open');

        // Update all statistics after initialization
        setTimeout(() => {
            console.log('开始更新所有统计...');
            updateAllStats();
            console.log('统计更新完成');
        }, 100);

        // Force update statistics again after a longer delay to ensure everything is loaded
        setTimeout(() => {
            console.log('强制再次更新统计...');
            updateAllStats();
        }, 500);

        // 初始化模板管理器
        setupTemplateManager();

        // 初始化快捷指令
        setupQuickCommand();



        // 初始化周度分析
        generateWeeklyAnalysis();
    }
    
    function setupTabSwitching() {
        const tabs = document.querySelectorAll('.nav-tab');
        const contents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const targetId = this.id.replace('tab-', '') + '-content';
                const targetContent = document.getElementById(targetId);
                if (targetContent) {
                    targetContent.classList.add('active');
                }

                // Update statistics when switching to specific tabs
                const tabType = this.id.replace('tab-', '');
                setTimeout(() => {
                    if (tabType === 'asylum') {
                        updateAsylumProgress();
                    } else if (tabType === 'english') {
                        updateEnglishProgress();
                    } else if (tabType === 'meditation') {
                        updateMeditationStats();
                    } else if (tabType === 'reading') {
                        updateReadingStats();
                        // Initialize bookshelf count
                        setTimeout(() => {
                            updateBookshelf();
                        }, 100);
                    } else if (tabType === 'habit') {
                        updateHabitStats();
                        // Re-initialize habit manager to ensure event listeners work
                        setTimeout(() => {
                            initializeHabitManager();
                        }, 100);
                    } else if (tabType === 'speech') {
                        updateSpeechStats();
                    } else if (tabType === 'lifestyle') {
                        updateLifestyleOverview();
                        updateMiniCharts(currentViewDate);
                    } else if (tabType === 'finance') {
                        loadFinanceData();
                    } else if (tabType === 'insights') {
                        // 运行自动洞察分析
                        runInsightsAnalysis();
                    } else if (tabType === 'analysis') {
                        // 确保切换到此页时，总是重新生成最新的分析
                        generateWeeklyAnalysis();
                    }
                }, 100);

                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('tab-ripple');
                this.appendChild(ripple);
                setTimeout(() => ripple.remove(), 1000);
            });
        });
    }

    // --- ENHANCED DATA IMPORT/EXPORT ---
    function handleFileUpload(event, isMerge) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);

                if (isMerge) {
                    // --- 合并逻辑 ---
                    let mergedCount = 0;
                    let skippedCount = 0;
                    Object.keys(data).forEach(key => {
                        const localValue = localStorage.getItem(key);
                        // 只有当本地没有这个值，或者值为空时，才写入
                        if (localValue === null || localValue === '') {
                            localStorage.setItem(key, data[key]);
                            mergedCount++;
                        } else {
                            skippedCount++;
                        }
                    });
                    alert(`合并导入成功！\n新增/更新了 ${mergedCount} 条数据。\n跳过了 ${skippedCount} 条本地已存在的数据。\n页面即将刷新。`);

                } else {
                    // --- 覆盖逻辑 ---
                    localStorage.clear(); // 先清空
                    Object.keys(data).forEach(key => {
                        localStorage.setItem(key, data[key]);
                    });
                    alert('覆盖导入成功！页面即将刷新。');
                }

                // 无论哪种模式，完成后都刷新页面
                location.reload();

            } catch (error) {
                alert('数据导入失败：文件格式不正确或内容已损坏。');
                console.error("Import error:", error);
            } finally {
                // 重置文件输入框，以便可以再次选择同一个文件
                event.target.value = '';
            }
        };
        reader.readAsText(file);
    }

    function exportAllData() {
        const allData = {};
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            allData[key] = localStorage.getItem(key);
        }
        const dataStr = JSON.stringify(allData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `growth-plan-data-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }

    function setupMigrationButtons() {
        // Export data functionality
        getEl('export-data-btn')?.addEventListener('click', exportAllData);

        // 覆盖导入
        getEl('import-data-input')?.addEventListener('change', (e) => {
            if (confirm('警告：覆盖导入将清除所有本地数据，并替换为文件中的数据。确定要继续吗？')) {
                handleFileUpload(e, false); // false代表不合并
            } else {
                // 用户取消，重置文件输入
                e.target.value = '';
            }
        });

        // 合并导入
        getEl('merge-import-input')?.addEventListener('change', (e) => {
            handleFileUpload(e, true); // true代表合并
        });
    }
    function setupAnimations() {
        // Add hover effects to cards and buttons
        document.querySelectorAll('.summary-item, .input-card, .overview-card').forEach(el => {
            el.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            });
            el.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });

        // Add click animation to buttons
        document.querySelectorAll('button').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Chart toggle animation
        getEl('toggle-charts-btn')?.addEventListener('click', function() {
            const container = getEl('lifestyle-chart-container');
            const arrow = this.querySelector('.toggle-arrow');
            if (container.classList.contains('open')) {
                container.classList.remove('open');
                arrow.textContent = '▼';
                localStorage.setItem('charts-open', 'false');
            } else {
                container.classList.add('open');
                arrow.textContent = '▲';
                localStorage.setItem('charts-open', 'true');
            }
        });
    }

    function setupGuitarFunctionality() {
        // Initialize guitar practice date to today
        const practiceDate = getEl('practice-date');
        if (practiceDate && !practiceDate.value) {
            practiceDate.value = getTodayDateString();
        }

        // Rating stars functionality
        document.querySelectorAll('.rating-star').forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.value);
                getEl('practice-rating').value = rating;

                // Update star display
                document.querySelectorAll('.rating-star').forEach((s, index) => {
                    if (index < rating) {
                        s.style.color = '#ff9500';
                        s.textContent = '★';
                    } else {
                        s.style.color = '#ccc';
                        s.textContent = '☆';
                    }
                });
            });
        });

        // Add practice record
        getEl('add-practice-btn')?.addEventListener('click', function() {
            const date = getVal('practice-date');
            const duration = getVal('practice-duration');
            const type = getVal('practice-type');
            const focus = getVal('practice-focus');
            const notes = getVal('practice-notes');
            const rating = getVal('practice-rating');

            if (!date || !duration || !type) {
                alert('请填写必要信息：日期、时长和练习类型');
                return;
            }

            const practiceId = `guitar-practice-${Date.now()}`;
            const practiceData = {
                id: practiceId,
                date: date,
                duration: parseInt(duration),
                type: type,
                focus: focus,
                notes: notes,
                rating: parseInt(rating) || 0
            };

            // Save to localStorage
            localStorage.setItem(practiceId, JSON.stringify(practiceData));

            // Clear form
            getEl('practice-duration').value = '';
            getEl('practice-focus').value = '';
            getEl('practice-notes').value = '';
            getEl('practice-rating').value = '0';
            document.querySelectorAll('.rating-star').forEach(s => {
                s.style.color = '#ccc';
                s.textContent = '☆';
            });

            // Refresh display
            loadGuitarPracticeLog();
            updateGuitarStats();

            alert('练习记录已保存！');
        });

        // Add song
        getEl('add-song-btn')?.addEventListener('click', function() {
            const title = getVal('song-title');
            const artist = getVal('song-artist');
            const difficulty = getVal('song-difficulty');
            const status = getVal('song-status');
            const notes = getVal('song-notes');

            if (!title) {
                alert('请输入曲目名称');
                return;
            }

            const songId = `guitar-song-${Date.now()}`;
            const songData = {
                id: songId,
                title: title,
                artist: artist || '未知',
                difficulty: parseInt(difficulty),
                status: status,
                notes: notes,
                addedDate: getTodayDateString(),
                lastPracticed: null
            };

            // Save to localStorage
            localStorage.setItem(songId, JSON.stringify(songData));

            // Clear form
            getEl('song-title').value = '';
            getEl('song-artist').value = '';
            getEl('song-notes').value = '';

            // Refresh display
            loadGuitarSongList();
            updateGuitarStats();

            alert('曲目已添加！');
        });

        // Load initial data
        loadGuitarPracticeLog();
        loadGuitarSongList();
        updateGuitarStats();
    }

    function loadGuitarPracticeLog() {
        const tbody = getEl('practice-log-body');
        if (!tbody) return;

        const practices = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('guitar-practice-')) {
                const data = JSON.parse(localStorage.getItem(key));
                practices.push(data);
            }
        }

        practices.sort((a, b) => new Date(b.date) - new Date(a.date));

        tbody.innerHTML = practices.map(practice => `
            <tr>
                <td>${practice.date}</td>
                <td>${practice.duration}分钟</td>
                <td>${getTypeDisplayName(practice.type)}</td>
                <td>${practice.focus || '-'}</td>
                <td>${'★'.repeat(practice.rating)}${'☆'.repeat(5-practice.rating)}</td>
                <td><button onclick="deleteGuitarPractice('${practice.id}')" class="delete-btn">删除</button></td>
            </tr>
        `).join('');
    }

    function loadGuitarSongList() {
        const tbody = getEl('song-list-body');
        if (!tbody) return;

        const songs = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('guitar-song-')) {
                const data = JSON.parse(localStorage.getItem(key));
                songs.push(data);
            }
        }

        songs.sort((a, b) => a.title.localeCompare(b.title));

        tbody.innerHTML = songs.map(song => `
            <tr>
                <td>${song.title}</td>
                <td>${song.artist}</td>
                <td>${'★'.repeat(song.difficulty)}</td>
                <td>${getStatusDisplayName(song.status)}</td>
                <td>${song.lastPracticed || '从未'}</td>
                <td><button onclick="deleteGuitarSong('${song.id}')" class="delete-btn">删除</button></td>
            </tr>
        `).join('');
    }

    function updateGuitarStats() {
        // Calculate weekly practice time
        const weeklyTime = calculateWeeklyPracticeTime();
        getEl('guitar-weekly-time').textContent = `${weeklyTime}分钟`;

        // Calculate streak days
        const streakDays = calculatePracticeStreak();
        getEl('guitar-streak-days').textContent = `${streakDays}天`;

        // Count mastered songs
        const masteredCount = countMasteredSongs();
        getEl('guitar-mastered-songs').textContent = `${masteredCount}首`;
    }

    function getTypeDisplayName(type) {
        const types = {
            'scales': '音阶练习',
            'chords': '和弦练习',
            'songs': '曲目练习',
            'technique': '技巧练习',
            'ear-training': '听力训练',
            'theory': '乐理学习',
            'other': '其他'
        };
        return types[type] || type;
    }

    function getStatusDisplayName(status) {
        const statuses = {
            'learning': '学习中',
            'practicing': '练习中',
            'polishing': '完善中',
            'mastered': '已掌握'
        };
        return statuses[status] || status;
    }

    function calculateWeeklyPracticeTime() {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        let totalTime = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('guitar-practice-')) {
                const data = JSON.parse(localStorage.getItem(key));
                const practiceDate = new Date(data.date);
                if (practiceDate >= oneWeekAgo) {
                    totalTime += data.duration;
                }
            }
        }
        return totalTime;
    }

    function calculatePracticeStreak() {
        const practices = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('guitar-practice-')) {
                const data = JSON.parse(localStorage.getItem(key));
                practices.push(data.date);
            }
        }

        const uniqueDates = [...new Set(practices)].sort().reverse();
        let streak = 0;
        const today = getTodayDateString();

        for (let i = 0; i < uniqueDates.length; i++) {
            const expectedDate = new Date();
            expectedDate.setDate(expectedDate.getDate() - i);
            const expectedDateStr = expectedDate.toISOString().split('T')[0];

            if (uniqueDates[i] === expectedDateStr) {
                streak++;
            } else {
                break;
            }
        }

        return streak;
    }

    function countMasteredSongs() {
        let count = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('guitar-song-')) {
                const data = JSON.parse(localStorage.getItem(key));
                if (data.status === 'mastered') {
                    count++;
                }
            }
        }
        return count;
    }

    window.deleteGuitarPractice = function(id) {
        if (confirm('确定要删除这条练习记录吗？')) {
            localStorage.removeItem(id);
            loadGuitarPracticeLog();
            updateGuitarStats();
        }
    };

    window.deleteGuitarSong = function(id) {
        if (confirm('确定要删除这首曲目吗？')) {
            localStorage.removeItem(id);
            loadGuitarSongList();
            updateGuitarStats();
        }
    };

    // Generate enhanced test data function for insights analysis
    function generateTestData() {
        console.log('开始生成增强测试数据...');

        // Set challenge start date to 30 days ago
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 29); // 30 days of data
        const startDateStr = startDate.toISOString().split('T')[0];
        localStorage.setItem('challenge-start-date', startDateStr);

        // Use the same planId format as the app
        const date = new Date(startDateStr);
        const planId = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

        // First, add some test habits to make insights more interesting
        const testHabits = [
            { id: 1, name: '刷抖音', frequency: 'daily' },
            { id: 2, name: '熬夜', frequency: 'daily' },
            { id: 3, name: '吃零食', frequency: 'daily' }
        ];

        // Clear existing habits and add test habits
        localStorage.setItem('habitList', JSON.stringify(testHabits));

        // Generate data for each day with realistic correlations
        for (let day = 1; day <= 30; day++) {
            const currentDate = new Date(startDate);
            currentDate.setDate(startDate.getDate() + day - 1);
            const dateStr = currentDate.toISOString().split('T')[0];

            // Base sleep quality (affects many other metrics)
            const sleepQuality = Math.random(); // 0-1, higher = better sleep
            const sleepHours = 6 + sleepQuality * 3; // 6-9 hours based on quality

            // Sleep data with realistic times
            const sleepStart = 22 + Math.random() * 2; // 22:00-24:00
            const sleepEnd = sleepStart + sleepHours;
            const actualSleepEnd = sleepEnd > 24 ? sleepEnd - 24 + 6 : sleepEnd; // Handle overnight

            localStorage.setItem(`${planId}-life-sleep-start-day${day}`,
                `${String(Math.floor(sleepStart)).padStart(2, '0')}:${String(Math.floor((sleepStart % 1) * 60)).padStart(2, '0')}`);
            localStorage.setItem(`${planId}-life-sleep-end-day${day}`,
                `${String(Math.floor(actualSleepEnd)).padStart(2, '0')}:${String(Math.floor((actualSleepEnd % 1) * 60)).padStart(2, '0')}`);

            // Mood rating (strongly correlated with sleep quality)
            const baseMood = 3 + sleepQuality * 5; // 3-8 base mood from sleep
            const moodVariation = (Math.random() - 0.5) * 2; // ±1 random variation
            const finalMood = Math.max(1, Math.min(10, Math.round(baseMood + moodVariation)));
            localStorage.setItem(`${planId}-mood-rating-day${day}`, finalMood);

            // Asylum data (affects mood positively, reduces bad habits)
            const asylumMotivation = 0.7 + sleepQuality * 0.3; // Better sleep = more motivation
            const didAsylum = Math.random() < asylumMotivation;
            if (didAsylum) {
                const asylumTime = 45 + Math.random() * 30; // 45-75 minutes
                localStorage.setItem(`${planId}-asylum-time-day${day}`, Math.round(asylumTime));
                localStorage.setItem(`${planId}-asylum-donetime-day${day}`, `${dateStr}T${String(Math.floor(Math.random() * 3) + 6).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`);
                localStorage.setItem(`${planId}-asylum-kcal-day${day}`, Math.floor(asylumTime * 6) + 200); // ~6 kcal/min
                localStorage.setItem(`${planId}-asylum-check-day${day}`, 'true');
            }

            // English learning (correlated with overall discipline)
            const studyMotivation = sleepQuality * 0.6 + (didAsylum ? 0.3 : 0) + Math.random() * 0.4;
            if (studyMotivation > 0.4) {
                const engTime = 20 + studyMotivation * 50; // 20-70 minutes
                const engWords = Math.round(engTime * 0.8); // ~0.8 words per minute
                localStorage.setItem(`${planId}-eng-time-day${day}`, Math.round(engTime));
                localStorage.setItem(`${planId}-eng-words-day${day}`, engWords);
                localStorage.setItem(`${planId}-eng-donetime-day${day}`, `${dateStr}T${String(Math.floor(Math.random() * 4) + 19).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`);
                localStorage.setItem(`${planId}-eng-content-day${day}`, `Unit ${Math.floor(day/3) + 1} - Lesson ${(day % 3) + 1}`);
                localStorage.setItem(`${planId}-eng-notes-day${day}`, `复习了${engWords}个单词`);
            }

            // Reading data (correlated with mood and discipline)
            const readingMotivation = (finalMood / 10) * 0.6 + studyMotivation * 0.4;
            if (readingMotivation > 0.3) {
                const readTime = 20 + readingMotivation * 60; // 20-80 minutes
                const readPages = Math.round(readTime / 2.5); // ~2.5 min per page
                localStorage.setItem(`${planId}-read-time-day${day}`, Math.round(readTime));
                localStorage.setItem(`${planId}-read-pages-day${day}`, readPages);
                localStorage.setItem(`${planId}-read-notes-day${day}`, `阅读心得第${day}天`);
            }

            // Meditation (helps with mood, inversely related to stress)
            const meditationChance = sleepQuality * 0.5 + (finalMood / 10) * 0.3 + Math.random() * 0.3;
            if (meditationChance > 0.4) {
                const medTime = 10 + Math.random() * 20; // 10-30 minutes
                localStorage.setItem(`${planId}-med-check-day${day}`, 'true');
                localStorage.setItem(`${planId}-med-time-day${day}`, Math.round(medTime));
                localStorage.setItem(`${planId}-med-donetime-day${day}`, `${dateStr}T${String(Math.floor(Math.random() * 2) + 6).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`);
            }

            // Bad habits (inversely correlated with good habits and mood)
            const stressLevel = 1 - sleepQuality; // Poor sleep = more stress
            const disciplineLevel = (didAsylum ? 0.3 : 0) + (studyMotivation > 0.4 ? 0.2 : 0) + (meditationChance > 0.4 ? 0.2 : 0);

            // 刷抖音 (inversely related to discipline, positively to stress)
            const douyinProbability = stressLevel * 0.7 + (1 - disciplineLevel) * 0.5;
            if (Math.random() < douyinProbability) {
                const douyinTime = 30 + stressLevel * 90; // 30-120 minutes when stressed
                const douyinCount = Math.ceil(douyinTime / 15); // ~15 min per session
                localStorage.setItem(`${planId}-habit-h1-d${day}-time`, Math.round(douyinTime));
                localStorage.setItem(`${planId}-habit-h1-d${day}-count`, douyinCount);
            }

            // 熬夜 (related to poor sleep quality)
            if (sleepQuality < 0.6) { // Poor sleep often means staying up late
                const lateNightTime = (1 - sleepQuality) * 120; // 0-120 minutes past bedtime
                localStorage.setItem(`${planId}-habit-h2-d${day}-time`, Math.round(lateNightTime));
                localStorage.setItem(`${planId}-habit-h2-d${day}-count`, 1);
            }

            // 吃零食 (stress eating, inversely related to discipline)
            const snackingProbability = stressLevel * 0.6 + (1 - disciplineLevel) * 0.4;
            if (Math.random() < snackingProbability) {
                const snackTime = 20 + stressLevel * 40; // 20-60 minutes
                const snackCount = Math.ceil(snackTime / 10); // ~10 min per snacking session
                localStorage.setItem(`${planId}-habit-h3-d${day}-time`, Math.round(snackTime));
                localStorage.setItem(`${planId}-habit-h3-d${day}-count`, snackCount);
            }

            // Other lifestyle data
            localStorage.setItem(`${planId}-life-reflection-day${day}`, `今天完成了计划的${Math.floor(disciplineLevel * 100)}%，感觉${finalMood > 6 ? '不错' : '一般'}`);
            localStorage.setItem(`${planId}-life-gratitude-day${day}`, `感谢今天的${['学习机会', '健康身体', '家人支持', '工作进展', '美好天气'][Math.floor(Math.random() * 5)]}`);
            localStorage.setItem(`${planId}-life-weight-day${day}`, (70 + Math.random() * 10).toFixed(1)); // 70-80 kg
            localStorage.setItem(`${planId}-life-fat-day${day}`, (15 + Math.random() * 10).toFixed(1)); // 15-25%
            localStorage.setItem(`${planId}-life-body-status-day${day}`, finalMood > 6 ? '良好' : finalMood > 4 ? '一般' : '疲劳');

            // Calorie data (affected by mood and discipline)
            const baseCalories = 1800;
            const moodEffect = (finalMood - 5) * 50; // ±250 calories based on mood
            const totalIntake = baseCalories + moodEffect + (Math.random() - 0.5) * 200;
            localStorage.setItem(`${planId}-life-breakfast-day${day}`, Math.round(totalIntake * 0.25)); // 25%
            localStorage.setItem(`${planId}-life-lunch-day${day}`, Math.round(totalIntake * 0.4)); // 40%
            localStorage.setItem(`${planId}-life-dinner-day${day}`, Math.round(totalIntake * 0.35)); // 35%
            localStorage.setItem(`${planId}-life-total-intake-day${day}`, Math.round(totalIntake));

            // Calorie deficit (affected by Asylum and total intake)
            const burnedCalories = 1600 + (didAsylum ? 400 : 0) + Math.random() * 200;
            const deficit = burnedCalories - totalIntake;
            localStorage.setItem(`${planId}-life-deficit-day${day}`, Math.round(deficit));
        }

        // Add some books
        const books = [
            {title: '深度工作', pages: 280},
            {title: '原子习惯', pages: 320},
            {title: '思考快与慢', pages: 450},
            {title: '刻意练习', pages: 280},
            {title: '心流', pages: 350}
        ];

        books.forEach((book, index) => {
            localStorage.setItem(`${planId}-book-goal-${index + 1}`, book.title);
            localStorage.setItem(`${planId}-book-goal-${index + 1}-pages`, book.pages);
        });

        // Set reading book selections for each day
        for (let day = 1; day <= 30; day++) {
            const bookIndex = Math.floor((day - 1) / 6) + 1; // Change book every 6 days
            localStorage.setItem(`${planId}-read-book-day${day}`, bookIndex);
        }

        // Generate guitar practice data
        const practiceTypes = ['scales', 'chords', 'songs', 'technique', 'ear-training', 'theory'];
        const focusContent = [
            'G大调音阶练习', 'C和弦转换', '《小星星》练习', '扫弦技巧',
            'Am和弦练习', 'F和弦按法', '节拍器练习', '指法练习',
            '《月亮代表我的心》', '和弦进行', '右手拨弦', '左手按弦'
        ];

        for (let i = 0; i < 25; i++) {
            const practiceDate = new Date(startDate);
            practiceDate.setDate(startDate.getDate() + i);
            const practiceId = `guitar-practice-${Date.now() + i}`;
            const practiceData = {
                id: practiceId,
                date: practiceDate.toISOString().split('T')[0],
                duration: Math.floor(Math.random() * 40) + 20, // 20-60 minutes
                type: practiceTypes[Math.floor(Math.random() * practiceTypes.length)],
                focus: focusContent[Math.floor(Math.random() * focusContent.length)],
                notes: `练习感受：${['进步明显', '需要更多练习', '手指灵活度提升', '节奏感改善'][Math.floor(Math.random() * 4)]}`,
                rating: Math.floor(Math.random() * 3) + 3 // 3-5 stars
            };
            localStorage.setItem(practiceId, JSON.stringify(practiceData));
        }

        // Generate guitar songs
        const songs = [
            {title: '小星星', artist: '儿歌', difficulty: 1, status: 'mastered'},
            {title: '月亮代表我的心', artist: '邓丽君', difficulty: 3, status: 'practicing'},
            {title: '同桌的你', artist: '老狼', difficulty: 2, status: 'polishing'},
            {title: '真的爱你', artist: 'Beyond', difficulty: 4, status: 'learning'},
            {title: '童年', artist: '罗大佑', difficulty: 2, status: 'mastered'},
            {title: '海阔天空', artist: 'Beyond', difficulty: 5, status: 'learning'},
            {title: '那些花儿', artist: '朴树', difficulty: 3, status: 'practicing'}
        ];

        songs.forEach((song, index) => {
            const songId = `guitar-song-${Date.now() + index + 1000}`;
            const songData = {
                id: songId,
                title: song.title,
                artist: song.artist,
                difficulty: song.difficulty,
                status: song.status,
                notes: `谱子来源：网络教程`,
                addedDate: startDateStr,
                lastPracticed: song.status !== 'learning' ? new Date(startDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null
            };
            localStorage.setItem(songId, JSON.stringify(songData));
        });

        // Generate bad habits data
        const habits = [
            {id: 1, name: '刷短视频'},
            {id: 2, name: '熬夜'},
            {id: 3, name: '吃零食'},
            {id: 4, name: '拖延症'}
        ];

        localStorage.setItem(`${planId}-habits`, JSON.stringify(habits));

        // Generate habit tracking data with more realistic patterns
        for (let day = 1; day <= 30; day++) {
            habits.forEach(habit => {
                // Different habits have different occurrence patterns
                let hasData = false;
                let count = 0;
                let time = 0;

                switch (habit.name) {
                    case '刷短视频':
                        hasData = Math.random() > 0.2; // 80% chance
                        if (hasData) {
                            count = Math.floor(Math.random() * 8) + 1; // 1-8次
                            time = Math.floor(Math.random() * 180) + 30; // 30-210分钟
                        }
                        break;
                    case '熬夜':
                        hasData = Math.random() > 0.6; // 40% chance
                        if (hasData) {
                            count = 1; // 熬夜通常是1次
                            time = Math.floor(Math.random() * 180) + 60; // 60-240分钟
                        }
                        break;
                    case '吃零食':
                        hasData = Math.random() > 0.3; // 70% chance
                        if (hasData) {
                            count = Math.floor(Math.random() * 5) + 1; // 1-5次
                            time = Math.floor(Math.random() * 60) + 10; // 10-70分钟
                        }
                        break;
                    case '拖延症':
                        hasData = Math.random() > 0.25; // 75% chance
                        if (hasData) {
                            count = Math.floor(Math.random() * 6) + 1; // 1-6次
                            time = Math.floor(Math.random() * 120) + 20; // 20-140分钟
                        }
                        break;
                    default:
                        hasData = Math.random() > 0.5; // 50% chance
                        if (hasData) {
                            count = Math.floor(Math.random() * 5) + 1;
                            time = Math.floor(Math.random() * 60) + 15;
                        }
                }

                // Store data (even if 0 for consistency)
                localStorage.setItem(`${planId}-habit-h${habit.id}-d${day}-count`, count);
                localStorage.setItem(`${planId}-habit-h${habit.id}-d${day}-time`, time);
            });
        }

        // Generate speech training data for 4 weeks
        for (let week = 1; week <= 4; week++) {
            for (let day = 1; day <= 7; day++) {
                if (Math.random() > 0.3) { // 70% completion rate
                    localStorage.setItem(`${planId}-speech-warmup-w${week}-d${day}-check`, 'true');
                    localStorage.setItem(`${planId}-speech-warmup-w${week}-d${day}-time`, Math.floor(Math.random() * 10) + 5);
                    localStorage.setItem(`${planId}-speech-structure-w${week}-d${day}-check`, 'true');
                    localStorage.setItem(`${planId}-speech-structure-w${week}-d${day}-theme`, `主题${week}-${day}`);
                    localStorage.setItem(`${planId}-speech-structure-w${week}-d${day}-time`, Math.floor(Math.random() * 20) + 10);
                }
            }
        }

        // Generate finance data
        const categories = ['food', 'shopping', 'transportation', 'entertainment', 'housing', 'utilities', 'health', 'education'];
        const categoryNames = {
            'food': '餐饮', 'shopping': '购物', 'transportation': '交通', 'entertainment': '娱乐',
            'housing': '住房', 'utilities': '水电费', 'health': '医疗健康', 'education': '教育'
        };

        for (let i = 0; i < 50; i++) {
            const transactionDate = new Date(startDate);
            transactionDate.setDate(startDate.getDate() + Math.floor(Math.random() * 30));
            const transactionId = `finance-transaction-${Date.now() + i}`;
            const isExpense = Math.random() > 0.2; // 80% expenses, 20% income
            const category = isExpense ? categories[Math.floor(Math.random() * categories.length)] : 'income';

            const transactionData = {
                id: transactionId,
                date: transactionDate.toISOString().split('T')[0],
                type: isExpense ? 'expense' : 'income',
                category: category,
                amount: isExpense ? Math.floor(Math.random() * 500) + 20 : Math.floor(Math.random() * 3000) + 1000,
                description: isExpense ? `${categoryNames[category]}支出` : '工资收入',
                note: `测试数据${i + 1}`
            };
            localStorage.setItem(transactionId, JSON.stringify(transactionData));
        }

        // Set monthly budget
        localStorage.setItem(`${planId}-monthly-budget`, '5000');

        console.log('测试数据生成完成！');

        // Update all statistics immediately
        updateAllStats();

        alert('完整的一个月测试数据已生成！页面将刷新以显示数据。');
        location.reload();
    }

    // Add test data generation to window for easy access
    window.generateTestData = generateTestData;

    // Start the application
    initializeApp();
});
</script>

    <script>
        function clearAllData() {
            if (confirm('确定要删除所有页面数据吗？此操作不可恢复！')) {
                localStorage.clear();
                alert('所有数据已成功删除！');
                window.location.reload(); // 刷新页面以反映变化
            }
        }
    </script>
    
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 9999;">
        <button onclick="clearAllData()" style="background-color: #ff453a; color: white; border: none; padding: 10px 15px; border-radius: 8px; font-size: 16px; cursor: pointer; box-shadow: var(--shadow-md);">
            删除所有数据
        </button>
    </div>
</body>
</html>