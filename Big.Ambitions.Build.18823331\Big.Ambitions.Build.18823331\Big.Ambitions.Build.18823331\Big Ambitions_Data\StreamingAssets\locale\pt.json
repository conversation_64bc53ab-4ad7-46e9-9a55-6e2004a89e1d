{"streetname_firstavenue": "1ª Avenida", "streetname_eighthstreet": "Rua 8", "streetname_fifthavenue": "5ª Avenida", "streetname_fifthstreet": "Rua 5", "streetname_firststreet": "Rua 1", "streetname_fourthavenue": "4ª Avenida", "streetname_fourthstreet": "Rua 4", "streetname_ninthstreet": "Rua 9", "streetname_secondavenue": "2ª Avenida", "streetname_secondstreet": "Rua 2", "streetname_seventhstreet": "Rua 7", "streetname_sixthavenue": "6ª Avenida", "streetname_sixthstreet": "Rua 6", "streetname_tenthstreet": "Rua 10", "streetname_thirdavenue": "3ª Avenida", "streetname_thirdstreet": "Rua 3", "businesstype_bank": "Banco", "businesstype_fastfoodrestaurant": "Restaurante fast food", "businesstype_giftshop": "<PERSON><PERSON> de presentes", "businesstype_supermarket": "Supermercado", "businesstype_school": "Escola", "businesstype_autorepairshop": "Oficina <PERSON>", "businesstype_clothingstore": "Loja de r<PERSON>", "businesstype_cardealership": "Concessionária de carros", "businesstype_appliancestore": "Loja de eletrodomésticos", "businesstype_wholesalestore": "Loja de atacado", "businesstype_recruitmentagency": "Agência de Empregos", "businesstype_furniturestore": "Loja de móveis", "common_subwaystation": "Estação de metrô", "businesstype_jewelrystore": "Joalher<PERSON>", "streetname_broadwaystreet": "Broadway", "businesstype_coffeeshop": "Cafeteria", "businesstype_liquorstore": "<PERSON><PERSON> de <PERSON>", "businesstype_marketingagency": "Agência de marketing", "businesstype_officesupplystore": "Papelaria", "businesstype_lawfirm": "Escritório de advocacia", "businesstype_headquarters": "Sede", "help_businesstype_giftshop_content": "**Lojas de Presentes** são negócios de varejo.\n\nOs clientes atendem a si mesmos\n\nO negócio precisa dos seguintes móveis para funcionar:\n\n* [<PERSON>lha de Cestas de Compras](furniture-stackofshoppingbaskets)\n* [Ponto de Venda](furniture-itemgrouppointofsale)\n* Pelo menos um produto para vender (veja abaixo)\n\nNegócios deste tipo vendem principalmente:\n\n* [Presente (Barato)](products-cheapgift)\n* [Presente (Caro)](products-expensivegift)\n* [Guarda-chuva](products-umbrella)\n\nE também podem vender:\n\n* [Smartwatch Arty Fish](products-smartwatch2)\n* [Flor (Barato)](products-cheapflower)\n* [Flor (Cara)](products-expensiveflower)\n* [Álbum de Fotos](products-picturebook)\n* [Rhythm By Tre](products-headphones01)\n* [Lata de Refrigerante](products-sodacan)\n* [Smartwatch ZanaMan](products-smartwatch1)\n\nFuncionários com as seguintes habilidades podem ser alocados:\n\n* [Atendimento ao Cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "common_business_types": "Tipos de negócios", "common_sellable_products": "Bens e serviços", "help_itemname_stackofshoppingbaskets_content": "**Pilha de Cestas de Compras** são exigidos em todos os negócios varejista onde os clientes pegam seus produtos por conta própria.\n\n**Capacidade de Clientes:** 30\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)\n* [AJ Pederson & Son](address:13 5a)\n* [Essentials Appliances](address:16 11s)", "help_itemname_cheapgift_content": "**Presente barato** é um tipo de produto vendido na [Loja de Presentes](businesstypes-giftshop).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Prateleira Arredondada](furniture-roundedshelf)\n* [Painel de Produtos](furniture-productpanel)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Metrô Atacado](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [JetCargo Imports](address: 1 pier)\n* [United Ocean Import](address: 3 pier)", "help_itemname_roundedshelf_content": "**Prateleira Redonda** pode ser usada para vender:\n\n* [<PERSON><PERSON>](products-cheapgift)\n* [Presente <PERSON>o](products-expensivegift)\n* [Flor Barata](products-cheapflower)\n* [Flor Cara]products-expensiveflower)\n\n**Capacidade do Produto:**\n* Presentes: 200\n* Flor Barata: 40\n* Flor cara: 25\n**Capacidade de clientes:** 15\n\nEla pode ser adquirida no seguinte local:\n* [Eletrodomésticos Quadrados](endereço:16 4a)", "help_itemname_expensivegift_content": "**Presente Caro** é um tipo de produto vendido na [Loja de Presentes](businesstypes-giftshop).\n\nEste produto pode ser colocado no seguinte movel:\n* [Prateleira Arredondada](furniture-roundedshelf)\n\nEste produto pode ser adquirido no seguinte local:\n* [NY Distribuidora](address:37 1s)\n\nEste produto pode ser importado no seguinte local:\n* [United Ocean Importadora](address: 3 pier)", "help_businesstype_coffeeshop_content": "**Cafeterias** são negócios de varejo. \n\nOs clientes são atendidos por funcionários.\n\nO negócio requer os seguintes móveis para funcionar:\n\n* Um<PERSON> [caixa registradora](furniture-cashregister) ou um [balcão de caixa](furniture-checkoutcounter).\n* Pelo menos um produto para vender (veja abaixo).\n\nNegócios deste tipo podem vender:\n\n* [Xícara de café](products-cupofcoffee)\n* [Croissant](products-croissant)\n* [Cupcake](products-cupcake)\n* [Donut](products-donut)", "help_businesstype_fastfoodrestaurant_content": "**Restaurantes fast-food** são empresas varejistas. \n\nOs clientes são servidos por funcionários.\n\nPara operar, a empresa precisa dos seguintes equipamentos:\n\n* [Caixa registradora](furniture-cashregister)\n* Pelo menos um produto à venda (veja abaixo)\n\nEmpresas desse tipo vendem principalmente:\n\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](products-burger)\n* [Batatas fritas](products-frenchfries)\n* [Cachorro-quente](products-hotdog)\n* [Sorvete](products-icecream)\n* [Espeto](products-kabob)\n* [Pizza](products-pizza)\n* [Salada](products-salad)\n* [Lata de refrigerante](products-sodacan)\n\nAlém disso, podem vender:\n\n* [Ma<PERSON>ã](products-apple)\n* [Banana](products-banana)\n **Nota:** é necessário ter uma [Balança digital](furniture-standingdigitalscale) para vender Frutas e Vegetais.\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_supermarket_content": "**Supermercados** são empresas varejistas. \n\nOs clientes servem a si mesmos.\n\nPara operar, a empresa precisa dos seguintes equipamentos:\n\n* [Pilha de cestas de compras](furniture-stackofshoppingbaskets)\n* [Ponto de vendas](furniture-itemgrouppointofsale)\n* Pelo menos um produto à venda (veja abaixo)\n\nEmpresas desse tipo vendem principalmente:\n\n* [Alimentos frescos](products-freshfood)\n* [Alimentos congelados](products-frozenfood)\n* [Sorvete](products-icecream)\n* [Latas de refrigerante](products-sodacan)\n\nAlém disso, podem vender:\n\n* [Maç<PERSON>](products-apple)\n* [Banana](products-banana)\n* [Garrafa de vinho](products-bottleofwine)\n* [Cenoura](products-carrot)\n* [Croissant](products-croissant)\n* [Cupcake](products-cupcake)\n* [Donut](products-donut)\n* [Alface](products-lettuce)\n* [<PERSON><PERSON><PERSON>](products-pear)\n* [Tomate](products-tomato)\n **Nota:** é necessário ter uma [Balança digital](furniture-standingdigitalscale) para vender Frutas e Vegetais.\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_jewelrystore_content": "**Joalherias** s<PERSON> negócios varejista.\n\nOs clientes se atendem.\n\nO negócio necessita dos seguintes móveis para funcionar:\n\n* [<PERSON><PERSON><PERSON> <PERSON> Cestas de Compras](furniture-stackofshoppingbaskets)\n* [Ponto de Vendas](furniture-itemgrouppointofsale)\n* Pelo menos um produto para vender (veja abaixo)\n\nNegócios deste tipo vendem principalmente:\n\n* [Jóia (Barata)](products-cheapjewelry)\n* [Jóia (Cara)](products-expensivejewelry)\n* [Smartwatch Arty Fish](products-smartwatch2)\n* [Smartwatch ZanaMan](products-smartwatch1)\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao Cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_clothingstore_content": "**Lojas de Roupas** são negócios varejista.\n\nOs clientes se atendem.\n\nO negócio necessita dos seguintes móveis para funcionar:\n\n* [<PERSON><PERSON><PERSON> de Cestas de Compras](furniture-stackofshoppingbaskets)\n* [Ponto de Vendas](furniture-itemgrouppointofsale)\n* Pelo menos um produto para vender (veja abaixo)\n\nNegócios deste tipo vendem principalmente:\n\n* [<PERSON><PERSON>pas (Feminino Clássico e Barato)](products-classiccheapfemaleclothing)\n* [Roupas (Masculino Clássico e Barato)](products-classiccheapmaleclothing)\n* [Roupas (Feminino Clássico e Caro)](products-classicexpensivefemaleclothing)\n* [Roupas (Masculino Clássico e Caro)](products-classicexpensivemaleclothing)\n* [Roupas (Feminino Moderno e Barato)](products-moderncheapfemaleclothing)\n* [Roupas (Masculino Moderno e Barato)](products-moderncheapmaleclothing)\n* [Roupas (Feminino Moderno e Caro)](products-modernexpensivefemaleclothing)\n* [Roupas (Masculino Moderno e Caro)](products-modernexpensivemaleclothing)\n\nAlém disso, podem vender:\n\n* [Jóia (Barata)](products-cheapjewelry)\n* [Jóia (Cara)](products-expensivejewelry)\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao Cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_liquorstore_content": "**Lojas de bebidas** são empresas varejistas. \n\nOs clientes servem a si mesmos.\n\nPara operar, a empresa precisa dos seguintes equipamentos:\n\n* [Pilha de cestas de compras](furniture-stackofshoppingbaskets)\n* [Ponto de vendas](furniture-itemgrouppointofsale)\n* Pelo menos um produto à venda (veja abaixo)\n\nEmpresas desse tipo vendem principalmente:\n\n* [Cerveja](products-beer)\n* [Garrafa de vinho](products-bottleofwine)\n* [Cigarro](products-cigar)\n* [Uísque](products-whisky)\n\nAlém disso, podem vender:\n\n* [Margarita](products-margarita)\n* [<PERSON><PERSON>](products-martini)\n* [Lata de refrigerante](products-sodacan)\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_lawfirm_content": "**Escritórios de Advocacia** são negócios baseados em escritórios.\n\nClientes são atendidos digitalmente e não estão fisicamente presentes nos edifícios.\n\nO negócio necessita dos seguintes móveis para funcionar:\n\n* [Computador <PERSON> Trabalho](furniture-computerworkstation)\n\nNegócios deste tipo podem vender:\n\n* [Taxa de Advogado (Hora)](fees-hourlylawyerfee)\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [<PERSON><PERSON><PERSON>](skill-lawyer)\n* [<PERSON><PERSON><PERSON>](skill-cleaning)", "help_businesstype_headquarters_content": "**Sedes Corporativas** são negócios especiais que podem ser estabelecidos em qualquer prédio de escritórios.\n\nEsse tipo de negócio nunca atende clientes. Em vez disso, pode acomodar todos os funcionários operacionais de uma organização.\n\nO negócio requer os seguintes móveis para funcionar:\n\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n\nFuncionários com as seguintes habilidades podem ser designados:\n\n* [Agente de Compras](skill-purchasingagent)\n* [Gerente de Logística](skill-logisticsmanager)\n* [Gerente de RH](skill-hrmanager)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-headhunter)\n* [<PERSON>peza](skill-cleaning)", "help_itemname_freshfood_content": "**Comida fresca** é um tipo de produto vendido principalmente em [Supermercados](tipos de empresa-supermercado).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Geladeira Industrial](móveis-Geladeira industrial)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Metro Atacadista](endereço:2 5a)\n* [NY Distro Inc](endereço:37 1s)\n* [Comércio total de produtos](endereço: 6 6a)\n\nO produto pode ser importado do seguinte local:\n* [SeaSide Internationals](endereço: 2 cais)", "help_itemname_frozenfood_content": "**Comida <PERSON>ada** é um tipo de produto vendido principalmente em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nas seguintes mobílias:\n* [Freezer Industrial (Pequeno)](furniture-smallindustrialfreezer)\n* [Freezer Industrial](furniture-industrialfreezer)\n\nO produto pode ser comprado nos seguintes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n* [Total Produce Trading](address:6 6a)\n\nO produto pode ser importado nos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_cheapjewelry_content": "**<PERSON><PERSON>** é um tipo de produto vendido principalmente em [Jo<PERSON>her<PERSON>](businesstypes-jewelrystore).\n\n<PERSON><PERSON><PERSON> disso, pode ser vendido em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> de Joias no Piso](furniture-jewelryfloorshowcase)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [JetCargo Imports](address: 1 pier)\n* [United Ocean Import](address: 3 pier)", "help_itemname_expensivejewelry_content": "**<PERSON><PERSON>** é um tipo de produto vendido principalmente em [<PERSON><PERSON><PERSON><PERSON>](businesstypes-jewelrystore).\n\n<PERSON><PERSON><PERSON> disso, pode ser vendido em [<PERSON><PERSON> de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> de Joias no Piso](furniture-jewelryfloorshowcase)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [United Ocean Import](address: 3 pier)", "help_itemname_pizza_content": "*Pizza** é um tipo de produto vendido em [Restaurante de Fast Food](businesstypes-fastfoodrestaurant).\n\nEste produto pode ser utilizado nos seguintes  móveis:\n* [Forno de Pizza](furniture-pizzaoven)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_frenchfries_content": "**Batatas fritas** é uma categoria de produto vendido em [restaurante de fast food](businesstypes-fastfoodrestaurant).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Máquina de fritadeira industrial](furniture-industrialfryermachine)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_burger_content": "**<PERSON><PERSON><PERSON><PERSON><PERSON>** é um tipo de produto vendido em [Restaurante fast food](businesstypes-fastfoodrestaurant).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Grelha industrial](furniture-industrialgrill)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_sodacan_content": "**Lata de refrigerante** é um tipo de produto vendido principalmente em [Restaurantes fast-food](businesstypes-fastfoodrestaurant) e [Supermercados](businesstypes-supermarket).\n\nAlém disso, pode ser vendido em [Cafeterias](businesstypes-coffeeshop) e [Lojas de eletrônicos](businesstypes-electronicsstore) e [Floricultura](businesstypes-florist) e [Lojas de presentes](businesstypes-giftshop) e [Lojas de bebidas](businesstypes-liquorstore) e [Casas noturnas](businesstypes-nightclub).\n\nÉ possível colocar o produto neste equipamento:\n* [Refrigerador de bebidas](furniture-drinksfridge)\n* [Refrigerador de bebidas grande](furniture-largedrinksfridge)\n\nÉ possível comprar o produto nestes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nÉ possível importar o produto dos seguintes locais:\n* [JetCargo Imports](address: 1 pier)\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_salad_content": "**Salada** é um tipo de produto vendido principalmente em [Restaurantes de Fast-Food](businesstypes-fastfoodrestaurant).\n\nAlém disso, pode ser vendido em [Cafeterias](businesstypes-coffeeshop) e [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Bar de Saladas](furniture-saladbar)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n* [Total Produce Trading](address:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_croissant_content": "**Croissant** é um tipo de produto vendido principalmente em [Cafeterias](businesstypes-coffeeshop).\n\n<PERSON>ém disso, pode ser vendido em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON><PERSON>daria](furniture-bakeryshowcase)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_cupofcoffee_content": "**Copo de café** é uma categoria de produto vendido na [cafeteria](businesstypes-coffeeshop).\n\nO produto pode ser atribuído aos seguintes móveis:\n* [Máquina de café industrial](furniture-industrialcoffeemachine)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_cigar_content": "**Charuto** é um tipo de produto majoritariamente vendido em [Liquor Stores](bussinesstypes-liquorstore).\n\nO produto pode ser colocado nas seguintes mobílias:\n* [Prateleira de Tabaco (Grande)](furniture-tobaccoshelf1)\n* [<PERSON>rateleira de Tabaco (Média)](furniture-tobaccoshelf2)\n* [Prateleira de Tabaco (Pequena)](furniture-tobaccoshelf3)\n\nO produto pode ser comprado nas seguintes localidades:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nas seguintes localidades:\n* [United Ocean Import](address: 3 pier)\n\nO produto pode ser produzido utilizando as seguintes receitas:\n* [Receita de Charuto](recipes-cigarrecipe)", "help_itemname_bottleofwine_content": "**Garrafa de Vinho** é um tipo de produto vendido principalmente em [Lojas de Bebidas Alcoólicas](businesstypes-liquorstore).\n\nAlém disso, pode ser vendido em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Prateleira de Vinhos](furniture-wineshelf)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [United Ocean Import](address: 3 pier)", "help_itemname_paperbag_content": "**Saco de papel** é um tipo de produto usado por [Ponto de vendas](furniture-itemgrouppointofsale).\n\nQuase toda empresa varejista precisa de Saco de papel para vender produtos.\n\nÉ possível comprar o produto nestes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nÉ possível importar o produto dos seguintes locais:\n* [JetCargo Imports](address: 1 pier)\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_hotdog_content": "**Cachorro-quente** é um tipo de produto vendido em [Restaurante Fast Food](businesstypes-fastfoodrestaurant).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Grill para Cachorro-quente](furniture-hotdoggrill)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_industrialfryermachine_content": "**Fritadeira Industrial** pode ser usado para vender:\n\n* [Batatas Fritas](products-frenchfries)\n\n**Capacidade de Produtos:** 200\n**Capacidade de Clientes:** 30\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_hotdoggrill_content": "**Grelha para Cachorro-Quente** pode ser usado para vender:\n\n* [Cachorro-Quente](products-hotdog)\n\n**Capacidade de Produtos:** 100\n**Capacidade de Clientes:** 30\n\n**Requer:** [Armário](furniture-cabinet)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_industrialgrill_content": "**Grelha Industrial** pode ser usada para vender:\n\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](products-burger)\n* [Espeto de Frango](products-kabob)\n\n**Capacidade de Produtos:**\n* Hamb<PERSON>rguer: 120\n* Espeto de Frango: 80\n**Capacidade de Clientes:** 20\n\n**Requer:** [<PERSON><PERSON><PERSON>](furniture-cabinet)\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_drinksfridge_content": "**Geladeira de Bebidas** pode ser usada para vender:\n\n* [Lata de Refrigerante](products-sodacan)\n\n**Capacidade do Produto:** 120\n**Capacidade de Clientes:** 20\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_industrialfridge_content": "**Geladeira Industrial** pode ser usado para vender:\n\n* [<PERSON><PERSON><PERSON>](products-freshfood)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 20\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_smallindustrialfreezer_content": "**Freezer Industrial (Pequeno)** pode ser usado para vender:\n\n* [Comida Congelada](products-frozenfood)\n* [Sorvete](products-icecream)\n\n**Capacidade de Produtos:**\n* Comida Congelada: 25\n* Sorvete: 150\n**Capacidade de Clientes:** 15\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_cashregister_content": "**Caixa Registradora** é uma estação especial para funcionários que requerem habilidade em [Atendimento ao Cliente](skill-customerservice).\n\nVocê pode usar um Designer de Interiores para ajustar a fila de clientes.\n\nO móvel requer os seguintes produtos para funcionar:\n* [<PERSON><PERSON>](products-paperbag)\n\n**Capacidade do Produto:** 1000\n**Capacidade de Clientes:** 20\n\n**Precisa ser colocado em um dos móveis abaixo:**\n\n* [Cabinets](furniture-cabinet)\n* [Cocktail Bar](furniture-cocktailbar)\n* [Cocktail Bar (Wooden)](furniture-woodencocktailbar)\n\nEsse item pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)\n* [AJ Pederson & Son](address:13 5a)\n* [Essentials Appliances](address:16 11s)", "help_itemname_jewelryfloorshowcase_content": "**Vitrine de <PERSON>óias** pode ser usado para vender:\n\n* [Jó<PERSON> (Barata)](products-cheapjewelry)\n* [Jóia (Cara)](products-expensivejewelry)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 15\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_industrialcoffeemachine_content": "**Cafeteira Industrial** pode ser usado para vender:\n\n* [Xícara de Café](products-cupofcoffee)\n\n**Capacidade de Produtos:** 300\n**Capacidade de Clientes:** 30\n\n**Requer:** [Armário](furniture-cabinet)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_bakeryshowcase_content": "**Vitrine de Padaria** pode ser usado para vender:\n\n* [<PERSON>roissant](products-croissant)\n* [Cupcake](products-cupcake)\n* [<PERSON><PERSON><PERSON><PERSON>](products-donut)\n\n**Capacidade de Produtos:** 100\n**Capacidade de Clientes:** 20\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_saladbar_content": "**Mesa de salada** pode ser usada para vender:\n\n* [Salada](products-salad)\n\n**Capacidade do produto:** 350\n**Capacidade do cliente:** 20\n\nEle pode ser adquirido no seguinte local:\n* [Square Appliances](address:16 4a)", "help_itemname_clothingrack_content": "**<PERSON><PERSON>** pode ser usado para vender:\n\n* [<PERSON><PERSON><PERSON> (Feminino Clássico e Barato)](products-classiccheapfemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Clássico e Barato)](products-classiccheapmaleclothing)\n* [<PERSON><PERSON>pas (Feminino Clássico e Caro)](products-classicexpensivefemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Clássico e Caro)](products-classicexpensivemaleclothing)\n* [<PERSON><PERSON><PERSON> (Feminino Moderno e Barato)](products-moderncheapfemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Moderno e Barato)](products-moderncheapmaleclothing)\n* [<PERSON><PERSON><PERSON> (Feminino Moderno e Caro)](products-modernexpensivefemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Moderno e Caro)](products-modernexpensivemaleclothing)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 10\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ <PERSON>rson & Son](address:13 5a)", "uncle_fred_tutorial_1": "<PERSON><PERSON>, j<PERSON><PERSON>, espero que esteja se sentindo melhor. Enfim, conversei com um amigo meu, <PERSON>, ele é uma figura. Bem, a conclusão é: ele tem um apartamento barato que você provavelmente poderia alugar. Não é muito, mas servirá.", "uncle_fred_tutorial_2": "Também transferi alguns dólares para sua conta bancária. Certifique-se de comprar algo para comer, ok?", "uncle_fred_tutorial_3": "Jovem. <PERSON><PERSON> paguei seu primeiro aluguel, mas <PERSON> isso, ok, você precisa procurar um emprego. Qualquer coisa. Você só precisa de um salário neste momento.", "uncle_fred_tutorial_4": "Você fez um ótimo trabalho. Você puxou o seu pai. Acho que talvez ele tinha aproximadamente a sua idade quando começou seu primeiro negócio. <PERSON><PERSON><PERSON>, se você precisar de um empréstimo para começar alguma coisa, eu tenho um amigo no Jensen Capital. O nome dele é Larry. Você pode pedir para falar com ele. E não se esqueça de dar um \"oi\" por mim, seu tio Fred, está bem?", "uncle_fred_tutorial_5": "Você encontrou o carro. Bom. Agora, não receba multas de estacionamento. Como eu disse antes, está na hora de pegar algumas coisas para a sua nova loja. Você pode usar seu carro novo ou continuar andando por aí com o carrinho manual de carga. Você decide.", "uncle_fred_tutorial_6": "Ah, muito bom. Agora está começando a parecer uma loja de verdade. A seguir, temos que comprar algumas coisas para você poder vender. <PERSON><PERSON> enquanto, vá até uma loja de atacado, mas no futuro, você se sairá muito melhor importando diretamente de um fabricante ou trazendo do exterior.", "uncle_fred_tutorial_7": "<PERSON><PERSON><PERSON>, jovem. Agora é hora de abrir e começar a lidar com algum dinheiro, fazer grana. Estou torcendo por você.", "uncle_fred_tutorial_8": "<PERSON><PERSON>, olhe para você andando por aí e já acumulando dinheiro. Eu lhe digo, seu pai teria ficado tão orgulhoso. Acho que está na hora da sua primeira contratação. E não se esqueça de fazer aquele curso na Escola de Administração de Empresas para saber o que está fazendo. Tudo bem.", "uncle_fred_tutorial_9": "<PERSON><PERSON>, olha quem está dominando. Você fez um bom trabalho naquela contratação, garoto. Eu sei que ter um funcionário reduz muito o que você recebe, mas você também fica com mais tempo. Hora de começar a limpar esses pisos. Ah, cara, eles estão horríveis.", "uncle_fred_tutorial_free_car": "Então você é dono de uma loja de presentes agora, hein? <PERSON><PERSON>, muito bom. <PERSON><PERSON> bom. Agora temos algumas compras para fazer. Nós vamos conseguir alguns móveis e alguns produtos muito legais para vender lá. E eu também queria mencionar, recentemente investi em uma concessionária de carros e encontramos um carro destruído na oficina nos fundos, mas ainda funciona. Não é muito, mas é seu se você quiser. A chave está no porta-luvas.", "uncle_fred_tutorial_11": "Pisos, com bom aspecto! Certifique-se de mantê-los limpos. Você não quer os clientes andando por aí em uma loja suja, ok. <PERSON><PERSON> bem. Agora de volta aos negócios. Talvez seja hora de começar a procurar outras oportunidades. <PERSON><PERSON> acaso, você já reparou que as pessoas, seus clientes, estão procurando algo para beber? Que tal estocar na geladeira ou algo assim com latas de refrigerante? Você pode ganhar algum dinheiro extra com isso.", "uncle_fred_tutorial_12": "<PERSON>nto, garoto. Continue ouvindo seus clientes e continue melhorando. Continue melhorando. E a propósito, você sabe, você não constrói um império inteiro com apenas uma loja de presentes, certo? Acho que você deveria considerar abrir um novo negócio. <PERSON>to isto, você não quer se apressar em algo querendo ou não. Você tem que saber o que as pessoas querem. Existe um aplicativo para isso. Aplicativo MarketInsider. Veja o que as pessoas estão procurando.", "uncle_fred_tutorial_13": "Agora você sabe: nos negócios, nós sempre pesquisamos o mercado antes de entrarmos nele, certo? Por fim, antes de iniciar o seu próximo negócio, encontre um edifício com um alto índice de fluxo de pessoas. Oferecer produtos com alta demanda junto com um endereço que recebe muito movimento de pedestres significa uma receita alta.", "uncle_fred_tutorial_14": "Você tem dois negócios agora, hein? <PERSON><PERSON>. Acho que é isso que a mídia chama de empreendedor serial. Muito bom trabalho. Eu vou deixar você voltar para isso e eu vou falar com você mais tarde, garoto.", "uncle_fred_tutorial_15": "Então você recebeu uma ligação do banco? Eles devem estar chocados ao ver o que está acontecendo com sua conta no modo como ela está crescendo. Você deveria encontrar um apartamento melhor e comprar alguns móveis realmente bonitos. Você merece isso.", "uncle_fred_tutorial_16": "<PERSON><PERSON>, garoto. Falei com um bom amigo ontem. O nome é Gary. Ele é dono de uma unidade na loja de atacado na área industrial. Eles vendem coisas em grandes pedaços, mas muito mais barato. Você deve trazer um carro. É muito longe para andar.", "uncle_fred_tutorial_17": "Lindo! Certifique-se de manter a popularidade alta para todos os seus negócios. Às vezes, você pode ter que aumentar seu orçamento de marketing para obter os mesmos resultados. O mercado muda a cada dia.<br><br> Vou visitar minha residência de férias na Costa Del Sol. Quando eu voltar, espero ver que você aumentou sua renda diária. É hora de levar seu império para o próximo nível, mas precisamos de mais dinheiro primeiro!", "uncle_fred_tutorial_18": "<PERSON><PERSON>, garo<PERSON>, você tem que vir ver minha nova vila em Marbella. Vamos beber areia gris juntos algum dia. É incrível. De qualquer forma, fico feliz em ver que você descobriu como aumentar seu lucro. Nosso próximo passo é cortar o intermediário, o atacadista. Temos que começar a importar nossos produtos nós mesmos.", "uncle_fred_tutorial_19": "Parabéns pela sua nova sede! Este é o lugar onde os melhores funcionários da sua organização irão trabalhar. Primeiro, temos que começar a contratar. Precisamos de um Gerente de vendas para ajudar a garantir alguns contratos de importação.", "uncle_fred_tutorial_20": "Ahhh progresso! Seu primeiro funcionário operacional trabalhando em sua nova sede. Começando a soar bem chique, hein? Agora precisamos fazer com que seu agente de compras funcione. Mas primeiro, precisamos de um local para armazenar todos os seus produtos importados.", "uncle_fred_tutorial_21": "Excelente. Vamos usar este armazém para guardar seus produtos até que possamos colocá-los nas suas lojas. Voltaremos a isso mais tarde. A<PERSON>a, vamos começar a colocar os produtos nessas lindas prateleiras novas.", "uncle_fred_tutorial_22": "Portanto, seu armazém está lotado de produtos. Estamos na última parte, tirando essas coisas do seu armazém e entrando nas suas lojas. Então vamos nos ocupar.", "uncle_fred_tutorial_23": "Você está indo muito bem, garo<PERSON>. Estou muito impressionado. Na verdade, acho que devo recuar um pouco, dar-lhe mais espaço para que você possa continuar construindo seu império de varejo por conta própria.", "tutorial_1_objective_1": "Viaje e alugue a <b>3rd Street 45</b>", "tutorial_1_objective_2": "Durma até recarregar totalmente a energia", "tutorial_2_objective_1": "Compre uma <b>Geladeira comum</b> na loja de eletrodomésticos local", "tutorial_2_objective_2": "Coloque a geladeira em seu apartamento", "tutorial_2_objective_3": "Vá ao supermercado e compre pelo menos 3 unidades de alimentos frescos", "tutorial_2_objective_4": "Abasteça a geladeira com alimentos frescos", "tutorial_2_objective_5": "Coma alguma coisa", "tutorial_3_objective_1": "Consiga um emprego no supermercado local", "tutorial_3_objective_2": "Continue trabalhando até ganhar <b>$ 300</b> com seu novo emprego", "tutorial_4_objective_1": "Faça um empréstimo de $ 15.000", "tutorial_4_objective_3": "Comece uma loja de presentes", "help_itemname_wineshelf_content": "**Prateleira de Vinhos** pode ser usado para vender:\n\n* [Garrafa de Vinho](products-bottleofwine)\n\n**Capacidade de Produtos:** 72\n**Capacidade de Clientes:** 10\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "tutorial_5_objective_1": "Compre um <u><PERSON><PERSON><PERSON></u>, uma <u>Caixa Registradora</u>, uma <u><PERSON><PERSON><PERSON> de Cestas de Compras</u> e uma <u>Prateleira Arredondada</u> em <b><PERSON>rson & Son</b>", "tutorial_5_objective_2": "Instale o armário e coloque a caixa registradora em cima dele na sua loja", "tutorial_5_objective_3": "Coloque a pilha de cestas de compras em sua loja", "tutorial_5_objective_4": "Coloque a prateleira redonda em sua loja", "tutorial_6_objective_1": "Compre uma <u>caixa de presentes baratos</u> e uma <u>caixa de sacolas de papel</u> no <b>Metro Wholesale</b> .", "tutorial_6_objective_2": "Abasteça a prateleira arredondada com <b>presentes barato<PERSON></b>", "tutorial_6_objective_3": "Abasteça a caixa registradora com <b>sacolas de papel</b>", "tutorial_7_objective_1": "Abra a loja usando o app <b>BizMan</b>", "tutorial_7_objective_2": "Comece a trabalhar no caixa", "tutorial_7_objective_3": "Gerencie o negócio por 2 dias", "tutorial_8_objective_1": "Concluir o curso “Gestão Básica”", "tutorial_8_objective_2": "Inicie uma campanha de recrutamento para funcionários de <b>Atendimento ao Cliente</b> na <b>Anderson Recruitment Corp.</b>", "tutorial_8_objective_3": "Use o <b>BizMan Schedule</b> para atribuir o funcionário ao caixa para cada dia que você deseja que ele trabalhe.", "tutorial_9_objective_1": "Compre uma estação de limpeza na Square Appliances", "tutorial_9_objective_2": "Coloque a estação de limpeza na sua loja de presentes", "tutorial_free_car_objective_1": "Pegue o Honza Mimic na concessionária de carros do tio Fred", "tutorial_11_objective_1": "Garanta $ 8.000 em sua conta bancária (salvando ou falando com o banco novamente)", "tutorial_11_objective_2": "Compre uma <b>gel<PERSON><PERSON> de bebidas</b> na loja de eletrodomésticos local", "tutorial_11_objective_3": "Compre uma <b>caixa de Lata de Refrigerante</b> em <b>Metro Wholesale</b>", "tutorial_11_objective_4": "Coloque a <b>gel<PERSON><PERSON> de bebidas</b> em sua loja", "tutorial_11_objective_5": "Encha a <b>gel<PERSON><PERSON> de be<PERSON></b> com <b>latas de refrigerante</b>", "tutorial_13_objective_1": "Encontre e alugue um edifício comercial com um índice de tráfego de pelo menos {trafficIndex} e defina o tipo de negócio como \"Restaurante Fast Food\"", "tutorial_14_objective_1": "Gere um lucro diário de {moneyAmount} por negócio", "tutorial_15_objective_1": "Visite a loja drive-in", "tutorial_16_objective_1": "Faça uma campanha de marketing para um dos seus negócios", "tutorial_16_objective_2": "Alcance um nível de marketing de 70% para um de seus negócios", "tutorial_17_objective_1": "Gere um lucro total de {moneyAmount} em um dia", "tutorial_18_objective_1": "Alugue um pequeno prédio de escritórios", "tutorial_18_objective_2": "Abra uma sede em seu novo prédio de escritórios", "tutorial_19_objective_1": "Contrate um agente de compras", "tutorial_19_objective_2": "Compre um <u>computador</u>, uma <u>escrivani<PERSON><PERSON></u> e uma <u>cade<PERSON></u> na loja de materiais de escritório do Sr. Scott", "tutorial_19_objective_3": "Coloque a mesa em sua sede e junte o computador e a cadeira à mesa", "tutorial_19_objective_4": "Atribuir o Agente de Compras à estação de trabalho", "tutorial_20_objective_1": "Alugue um pequeno armazém", "tutorial_20_objective_2": "Compre pelo menos uma prateleira de paletes", "tutorial_20_objective_3": "Coloque a prateleira de paletes no seu armazém", "tutorial_21_objective_1": "Estabeleça uma parceria de importação com a JetCargo Imports", "tutorial_21_objective_2": "Faça o pedido de uma remessa de pelo menos 1000 presentes baratos e 600 latas de refrigerante pela tela do Representante de compras da sua sede (BizMan)", "tutorial_21_objective_3": "Após receber as mercadorias importadas, verifique se há 1000 Presentes baratos e 600 Latas de refrigerante em seu armazém", "tutorial_22_objective_1": "Compre uma van UMC Desert na concessionária de caminhões", "tutorial_22_objective_2": "Dirija a van para o seu armazém e atribua-a a um slot", "tutorial_22_objective_3": "Contrate um Gerente de Logística", "tutorial_22_objective_4": "Contrate um entregador", "tutorial_22_objective_5": "Atribua o Gerente de Logística a uma estação de trabalho livre na sua sede (pode ser adquirida na loja de Materiais de escritório do Sr. Scott)", "tutorial_22_objective_6": "Atribua o motorista de entrega à van em seu depósito", "tutorial_22_objective_7": "Configure sua loja de presentes como destino na tela do Gerenciador de Logística de sua sede (BizMan)", "tutorial_22_objective_8": "Defina a contagem mínima de estoque de sua loja de presentes para pelo menos 200 presentes baratos e 100 latas de refrigerante", "tutorial_23_objective_1": "Obsolete: tutorial_23_objective_1", "uncle_freds_car": "Carro do tio Fred", "streetname_pier": "Cais", "streetname_thirdandahalfavenue": "Terceira Avenida e meia", "help_businesstype_webdevelopmentagency_content": "**Agências de Desenvolvimento Web** são do tipo escritório baseado em negócios.\n\nOs clientes são atendidos digitalmente e não estão fisicamente presentes nos prédios.\n\nO negócio requer os seguintes móveis para funcionar:\n* [Configuração da estação de trabalho](móveis-computador)\n\nNegócios deste tipo podem vender:\n* pagamento horária do programador", "help_itemname_computer_content": "**Computadores** podem ser usados para configurar uma estação de trabalho para funcionários de escritório.\nRequer uma [<PERSON><PERSON>](furniture-chair) e uma [Mesa](furniture-desk).\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [Mr. <PERSON>'s Office Supplies](address:11 1a)", "help_requirement_desk_content": "As **mesas** podem ser usadas para configurar uma estação de trabalho para funcionários do escritório.\nRequer uma [Cadeira](móveis-cadeira) e um [Computador](móveis-computador).\n\nExistem diferentes tipos de mesas:\n* Mesa de escritório padrão\n* Mesa Executiva\n* Mesa Padrão\n\nElas podem ser adquiridas no seguinte locais:\n* [Senhor. Scott's Office Supplies](endereço: 11 1a)\n* [Ika Bohag](endereço: 50 4s)\n* [Conceito Lu<PERSON>](endereço:68 5a)", "help_requirement_chair_content": "**Cadeiras** podem ser usadas para configurar uma estação de trabalho para funcionários do escritório.\nRequer uma [mesa](móveis-mesa) e um [computador](móveis-computador).\n\nExistem diferentes tipos de cadeiras:\n* Cadeira de escritório\n* Cadeira de escritório Stump Mesh\n* Cadeira normal\n* Cadeira Multiuso\n* Poltrona Sommerhus\n* Cadeira Braço Gammel\n* Cadeira de jogo\n\nElas podem ser adquiridas nos seguintes locais:\n* [Senhor. Scott's Office Supplies](endereço: 11 1a)\n* [Conceito Lux](endereço:68 5a)", "help_itemname_cleaningstation_content": "**Ponto de Limpeza** é uma *estação para funcionários* especial que requer funcionários com habilidade de [Limpeza](skill-cleaning).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)\n* [AJ Pederson & Son](address:13 5a)\n* [Essentials Appliances](address:16 11s)", "common_day": "<PERSON>a", "common_back": "Voltar", "menu_continue": "<PERSON><PERSON><PERSON><PERSON>", "menu_start_new_game": "Começar novo jogo", "menu_forum_login": "Login do fórum", "menu_forum_signup": "Cadastro no fórum", "menu_report_bug": "Aviso de bug", "menu_exit_to_desktop": "<PERSON>r para a área de trabalho", "menu_news_updates": "Novas atualizações", "menu_until_next_release": "até o próximo lançamento", "menu_days_next_release": "<PERSON><PERSON>", "menu_discover_roadmap": "Conheça o roteiro", "menu_more_hg_headline": "mais por <PERSON><PERSON><PERSON>", "menu_more_hg_description": "Startup Company é um jogo de simulação de negócios. Construa seu próprio site e compita contra grandes gigantes da tecnologia!", "menu_more_hg_check_out_on_steam": "Confira na Steam", "menu_more_hg_follow_us_sm": "SIGA-NOS NA MÍDIA SOCIAL", "menu_load_game": "<PERSON><PERSON><PERSON> jogo", "menu_load_game_show_recover_saves": "<PERSON>rar jogos salvos", "menu_load_game_delete_character": "Excluir personagem", "menu_load_game_save_files": "<PERSON><PERSON>", "menu_load_game_recover_saves": "<PERSON>cuperar jogos salvos", "menu_load_game_delete_save": "Excluir jogo salvo", "common_day_format": "{day} {dayNumber}", "menu_options": "Opções", "menu_options_graphics": "Grá<PERSON><PERSON>", "menu_options_audio": "<PERSON><PERSON><PERSON>", "menu_options_controls": "Controles", "menu_options_others": "Outros", "menu_options_graphics_fullscreen": "Tela cheia", "menu_options_graphics_resolution": "Resolução", "menu_options_graphics_fps_limit": "Limite de FPS", "menu_options_graphics_gfx_quality": "Qualidade GFX", "menu_options_graphics_aa": "Anti-aliasing", "menu_options_audio_global_volume": "Volume Global", "menu_options_controls_invert_rotation": "Inverter Rotação", "menu_options_others_language": "Idioma", "menu_options_audio_radio_volume": "Volume do rádio", "menu_options_audio_menumusic_volume": "Volume da música do menu", "citymap_map_filters": "filtros do mapa", "citymap_category_special_buildings": "Edifícios especia<PERSON>", "citymap_category_rentable": "Prédios para alugar", "citymap_category_active_businesses": "Negócios ativos", "tutorial_objectives": "Objetivos", "tutorial_upcoming_tasks": "<PERSON><PERSON><PERSON><PERSON> futuras", "tutorial_todotask_lowstock": "{producer_itemname} de <u>{businessname}</u> em breve ficará sem <u>{itemname}!</u>", "tutorial_todotask_emptystock": "{producer_itemname} de <u>{businessname}</u> está sem <u>{itemname}!</u>", "tutorial_todotask_missingemployee": "<u>{businessname}</u> est<PERSON> a<PERSON>, mas não há funcionário em <u>{itemname}</u> .", "tutorial_todotask_missingrequireditem": "<u>{businessname}</u> requer pelo menos um <u>{itemname}</u>.", "tutorial_todotask_missingschedule": "<u>{businessname}</u> não tem horário de funcionamento definido.", "tutorial_todotask_noproducers": "<u>{businessname}</u> não tem produtos para vender.", "tutorial_todotask_dirtyfloors": "<u>{businessname}</u> está sujo e precisa ser limpo.", "tutorial_todotask_employeeunassigned": "<u>{employeename}</u> atualmente não está atribuído a nenhuma empresa.", "tutorial_todotask_employeeidle": "<u>{employeename}</u> atualmente não está atribuído a nenhuma tarefa.", "tutorial_message_from_uncle_fred": "mensagem do tio fred", "common_monday": "Segunda-feira", "common_tuesday": "terça-feira", "common_wednesday": "Quarta-feira", "common_thursday": "quinta-feira", "common_friday": "Sexta-feira", "common_saturday": "Sábado", "common_sunday": "Domingo", "topbar_date_format": "{DayOfWeek} (Dia {CurrentNumberDay})", "common_cancel": "<PERSON><PERSON><PERSON>", "common_today": "Hoje", "common_tomorrow": "Amanhã", "common_setdestination": "Definir destino", "playerhud_currentjob_quit_job": "Sair do trabalho", "playerhud_currentjob_finished": "Finalizado", "playerhud_currentjob_day_off": "Dia de folga", "jobname_undefined": "Indefinido", "jobname_cashier": "Caixa", "itempanelui_sell": "Vender", "itempanelui_maintenance_condition": "Condição", "itempanelui_vehicle_fuel": "Combustível", "itempanelui_vehicle_parkingzone": "Zona de estacionamento", "itempanelui_parkingzone_legal": "PERMITIDO (GRATUITO)", "itempanelui_parkingzone_illegal": "ILEGAL", "itempanelui_parkingzone_notavailable": "-", "itempanelui_unpaid_total": "Total não pago", "itempanelui_buttons_drop_pallet": "Soltar Palete", "itempanelui_buttons_sleep": "<PERSON><PERSON><PERSON>", "itempanelui_buttons_auto_park": "Estacionamento", "itempanelui_buttons_park_vehicle": "Estacionar veículo", "itempanelui_buttons_grab": "<PERSON><PERSON><PERSON>", "itempanelui_buttons_place": "Colocar", "itempanelui_buttons_discard": "Descar<PERSON>", "itempanelui_buttons_leave": "<PERSON><PERSON>", "itempanelui_buttons_pack": "Empacotar", "sleeppanel_headline": "<PERSON><PERSON> <PERSON>ir", "sleeppanel_start_sleeping": "Comece a dormir", "sleeppanel_waking_up_at": "Acordar às {time}", "joboffer_wage_per_hour": "<PERSON><PERSON><PERSON> por hora", "studypanel_start_studying": "Comece a estudar", "studypanel_study_for": "<PERSON><PERSON><PERSON><PERSON> por {time} horas", "studypanel_ends_at": "<PERSON><PERSON><PERSON><PERSON> {time}", "diplomaname_basicmanagement": "Gerenciamento Básico", "diplomaname_fundamentalbusinessadministration": "Princípios Básicos de Administração de Empresas", "managecargo_unloadforklift": "<PERSON><PERSON><PERSON><PERSON>", "itemname_undefined": "Indefinido", "itemname_pizzaoven": "Forno De Pizza", "itemname_hotdog": "Cachorro-quente", "itemname_sodacan": "Lata de refrigerante", "itemname_closedcardboardbox": "Caixa de papelão fechada", "itemname_pizza": "Pizza", "itemname_industrialfryermachine": "Fritadeira Industrial", "itemname_roundedshelf": "Prateleira arredondada", "itemname_standardfridge": "Geladeira <PERSON>", "itemname_bed1": "Cama <PERSON>ão", "itemname_smallcabinet": "<PERSON><PERSON><PERSON> pe<PERSON>", "itemname_hotdoggrill": "<PERSON><PERSON><PERSON>nte", "itemname_paperbag": "Saco de papel", "itemname_floorlamp": "Luminária de piso", "itemname_recyclebin": "Lixeira de reciclagem", "itemname_cleaningstation": "Estação de limpeza", "itemname_kingsizebed": "Cama King-size", "itemname_industrialgrill": "Grelha industrial", "itemname_burger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_expensivegift": "Presente caro", "itemname_drinksfridge": "Geladeira de bebidas", "itemname_frenchfries": "Batatas fritas", "itemname_regularchair": "Cadeira normal", "itemname_coffeetable1": "Mesa de centro moderna", "itemname_storageshelf": "Prateleira de Armazenamento", "itemname_freshfood": "Comida fresca", "itemname_coffeetable2": "Mesa de centro oval", "itemname_industrialfridge": "Frigorífico Industrial", "itemname_frozenfood": "<PERSON><PERSON><PERSON> congel<PERSON>", "itemname_coffeetable3": "Mesa de centro dupla", "itemname_multipurposechair": "Cadeira multiuso", "itemname_computer": "Computador", "itemname_officedesk1": "Mesa de escritório padrão", "itemname_table1": "Mesa comum", "itemname_cashregister": "Caixa registradora", "itemname_stackofshoppingbaskets": "<PERSON><PERSON><PERSON> de Cestas de Compras", "itemname_shoppingbasket": "Cesta de compras", "itemname_hourlyprogrammerfee": "Serviço do programador por hora", "itemname_sofa1": "Sofá 1", "itemname_jewelryfloorshowcase": "<PERSON><PERSON><PERSON>", "itemname_cheapjewelry": "<PERSON><PERSON><PERSON>", "itemname_expensivejewelry": "<PERSON><PERSON><PERSON>", "itemname_croissant": "Croissant", "itemname_cupofcoffee": "Xícara de café", "itemname_pottedplant1": "Vaso de planta alto", "itemname_cheapgift": "<PERSON><PERSON>", "itemname_industrialcoffeemachine": "Máquina de café industrial", "itemname_classiccheapmaleclothing": "Roupas masculinas baratas e clássicas", "itemname_bakeryshowcase": "<PERSON><PERSON><PERSON>", "itemname_mop": "Esfregar", "itemname_saladbar": "Buffet de salada", "itemname_salad": "Salada", "itemname_counter1": "Armário com gavetas", "itemname_smallindustrialfreezer": "Congelador Industrial Pequeno", "itemname_officechair": "Cadeira de escritório", "itemname_palletshelf": "Prateleira de Paletes", "itemname_pallet": "<PERSON><PERSON><PERSON>", "itemname_clothingrack": "<PERSON><PERSON>", "itemname_classiccheapfemaleclothing": "Roupas femininas baratas e clássicas", "itemname_cigar": "<PERSON><PERSON><PERSON>", "itemname_moderncheapmaleclothing": "Roupas Masculinas Baratas e Modernas", "itemname_bottleofwine": "<PERSON><PERSON><PERSON>", "itemname_moderncheapfemaleclothing": "Roupas Femininas Modernas e Baratas", "itemname_wineshelf": "Prateleira de Vinhos", "itemname_classicexpensivemaleclothing": "Roupas masculinas clássicas e caras", "itemname_classicexpensivefemaleclothing": "Roupas femininas clássicas e caras", "itemname_modernexpensivemaleclothing": "Roupas masculinas modernas e caras", "itemname_itemgrouppointofsale": "Ponto de <PERSON>endas", "itemname_modernexpensivefemaleclothing": "Roupas femininas modernas e caras", "itemname_handtruck": "Carrinho de carga", "itemname_itemgroupdesktopworkstation": "Estação de trabalho", "itemname_hourlylawyerfee": "Honorários advocatícios", "itemname_trashbin": "Lixeira", "itemname_wallmountedtv": "TV 43\" ELGE 8467NW-43283833", "itemname_painting40x30": "Pintura 40x30", "itemname_painting30x30": "Pintura 30x30", "itemname_painting50x70": "Pintura 50x70", "itemname_pictureframe": "Porta-retrato", "itemname_wallclock": "Relógio de <PERSON>ede", "itemname_productpanel": "Painel de produtos", "itemname_ceilinglampround": "Lâmpada de teto redonda", "itemname_ceilinglampsquare": "Lâmpada de teto quadrada", "itemname_ceilinglamptube": "Tubo de lâmpada de teto", "itemname_counter2": "Armário com portas", "itemname_countercorner": "<PERSON><PERSON><PERSON> canto", "itemname_counterleftend": "Armário com acabamento (esquerdo)", "itemname_counterrightend": "Armário com acabamento (direito)", "itemname_modularsofa1l": "Sofá Modular 1L", "itemname_modularsofa1r": "Sofá Modular 1 R", "itemname_modularsofa1m": "Sofá Modular 1 M", "itemname_armchair1": "<PERSON><PERSON><PERSON>", "itemname_armchair2": "<PERSON><PERSON><PERSON>", "itemname_changingroom": "<PERSON>est<PERSON><PERSON><PERSON>", "itemoverlay_packed": "(embalado)", "vehicletypename_umcdesert": "UMC Desert", "vehicletypename_handtruck": "Carrinho manual de carga", "vehicletypename_honzamimic": "Honza Mimic", "vehicletypename_forklift": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicletypename_missamvillian": "Lytte L6", "vehicletypename_mersaidis500": "Mersaidi S500", "vehicletypename_freighttruckt1": "Caminhão de carga T1", "vehicletypename_ferdinand112": "<PERSON> 112", "common_cargo_capacity": "Capacidade de Carga", "common_purchase": "Compra", "common_open": "Abrir", "common_closed": "<PERSON><PERSON><PERSON>", "open_in_bizman": "Abrir no BizMan", "interior_designer": "Designer de interiores", "purchaseui_totalprice": "Preço total", "purchaseui_place_order": "Faça seu pedido", "sign_appearance": "Visual da placa", "sign_appearance_sign_type": "Tipo de placa", "sign_appearance_sign_light_color": "Placa luminosa colorida", "sign_appearance_lamp_color": "Luminária colorida", "common_save_changes": "SALVAR ALTERAÇÕES", "common_placeholder": "{data}", "menu_save": "<PERSON><PERSON>", "menu_main_menu": "MENU PRINCIPAL", "subwaystation_price_per_ride": "<b>{price}</b> por trajeto", "buildingpreview_cancel_preview": "Cancelar visualização", "smartphone_persona": "<PERSON><PERSON><PERSON>", "smartphone_myemployees": "Meus funcionários", "smartphone_bizman": "BizMan", "smartphone_econoview": "EconoView", "smartphone_vooglemaps": "Voogle Maps", "smartphone_marketinsider": "MarketInsider", "buildingresume_right_click": "Clique com o botão direito para opções", "buildingresume_left_click": "Clique com o botão esquerdo para entrar", "buildingresume_change_sign": "Alterar o visual da placa", "buildingresume_open_in_bizman": "Abrir no BizMan", "buildingresume_neighborhood": "Bairro: {neighbourhood}", "buildingresume_traffic_index": "Índice de tráfego: {index}", "buildingresume_closed_opening_in_hours": "<br><PERSON><PERSON><PERSON> em {hours} hora(s) ({time})", "buildingresume_closed_opening_in_hour": "<br><PERSON><PERSON><PERSON> em {hours} hora ({time})", "buildingresume_open_closing_in_hours": "<br><PERSON><PERSON><PERSON> em {hours} hora(s) ({time})", "buildingresume_open_closing_in_hour": "<br><PERSON><PERSON><PERSON> em {hours} hora ({time})", "buildingresume_occupied": "Ocupado", "buildingresume_rented_by_you": "Alugado por você", "common_available_for_rent": "Disponível para aluguel", "ownerdescription_government_building": "Edifício do governo", "ownerdescription_privately_owned": "Propriedade privada", "ownerdescription_owned_by": "Propriedade de {corporation}", "personalgoal_achieved": "Objetivo pessoal alcançado", "personalgoal_minemployees": "Empregar pelo menos {minimumEmployees} pessoas", "personalgoal_reach_valuation": "Alcance uma avaliação de {valuation}", "personalgoal_rent_apartment": "Alugue um apartamento de {size} m²", "personalgoal_run_businesses": "G<PERSON><PERSON><PERSON> {amount} neg<PERSON><PERSON>s bem-sucedidos {type}", "personalgoal_own_vehicle_worth": "Possuir um veículo no valor de {price}", "daily_summary": "Resumo do dia {day}", "total_profit": "Lucro total", "daily_summary_businesses": "<PERSON>eg<PERSON><PERSON><PERSON>", "common_confirm": "confirme", "hud_confirm_are_you_sure": "VOCÊ TEM CERTEZA?", "bizman_presentation_hud_confirm_terminate_contract": "Você receberá o depósito completo e os móveis serão vendidos automaticamente e adicionados à sua conta", "colorlist_hud_confirm_remove_color": "Tem certeza de que deseja remover esta cor?", "hud_confirm_discard_item": "Tem certeza de que deseja destruir permanentemente este item?", "itempanelui_hud_confirm_sellvehicle": "Tem certeza de que deseja vender {type} por {price}?", "itempanelui_hud_confirm_sellitem": "Tem certeza de que deseja vender {type} por {price}?", "mini_menu_hud_confirm_save_game_exists": "Já existe um jogo salvo com o nome <b>\"{name}\"</b>. Você deseja substitui-lo?", "mini_menu_hud_confirm_unsaved_changes_menu": "Tem certeza de que deseja sair do menu principal? <b>Qualquer progresso não salvo será perdido!</b>", "mini_menu_hud_confirm_unsaved_changes_desktop": "Tem certeza de que deseja sair para a área de trabalho? <b>Qualquer progresso não salvo será perdido!</b>", "bizman_marketing_hud_confirm_cancel_campain": "Deseja mesmo cancelar esta campanha de marketing?", "interior_designer_wall": "parede", "interior_designer_floor": "<PERSON><PERSON>", "character_customization_notification_name_required": "Nome obrigatório", "carcontroller_notification_warehouse_vehicle_assigned_slot": "{type} foi atribuído ao slot {index}", "personal_goal_notification_personal_goal_required_to_enter_building": "Meta pessoal '{goal}' necessária para acessar este edifício", "bed_notification_cant_use_with_vehicle": "Você não pode usar uma cama enquanto estiver usando um {vehicle}!", "businessemployeecontroller_notification_cant_use_with_vehicle": "Você não pode se sentar enquanto estiver usando um {vehicle}", "cashregister_notification_forklift_buy": "Você só pode comprar caixas que estão em suas mãos, em um carrinho de mão ou no porta-malas do seu carro.", "exitzonedespawner_notification_cant_leave_without_paying": "Você tem que pagar por seus itens antes de sair.", "exitzonedespawner_notification_cant_leave_with_while_in_repairmode": "Você não pode sair do prédio enquanto segura um item desse tipo.", "exitzonedespawner_notification_cant_leave_with_forklift": "Você não pode sair do prédio com este tipo de veículo.", "fridge_notification_empty_hands_to_empty": "Você precisa estar com as mãos vazias para esvaziar o {itemname}", "itemcontroller_notification_empty_hands_to_empty": "Suas mãos devem estar livres para remover o conteúdo", "itemcontroller_notification_not_in_placementmode_to_empty": "Você não pode remover o conteúdo enquanto move um item", "pallet_notification_pallet_full": "Este palete está cheio!", "pallet_notification_only_quantity_items": "Você só pode armazenar itens de quantidade em paletes!", "palletshelf_notification_shelf_full": "A prateleira de paletes está cheia!", "playeritempurchaser_notification_require_shoppingbasket": "Você precisa de uma cesta de compras para pegar este produto", "playeritempurchaser_notification_hands_full": "Suas mãos já estão cheias", "storageshelf_notification_shelf_full": "A prateleira de armazenamento está cheia!", "importmanagerdialog_notification_select_agent": "Selecione um agente de compras", "common_notification_select_business": "Selecione uma empresa", "marketingagencydialog_notification_select_website": "Selecione um site de marketing", "recruitmentagencydialog_notification_select_skill": "Selecione uma habilidade primeiro", "wholesalestoremanagerdialog_notification_require_shelf_in_business": "Você precisa de pelo menos um {shelf} no prédio", "import_partnership_notification_shipment_arrived": "Uma remessa de {name} chegou", "businesshelper_notification_shipment_from_to_has_arrived": "Sua remessa de {fromname} para {toname} chegou", "businesshelper_notification_no_employee_assigned": "<b>{name} <PERSON><PERSON>, mas não tem funcionários designados.</b>", "employeehelper_notification_employee_called_in_sick": "{name} ({businessName}) ligou dizendo que estava doente", "employeehelper_notification_employee_finished_training_male": "O funcionário {name} terminou seu treinamento em {skill}", "employeehelper_notification_employee_finished_training_female": "A funcionária {name} terminou seu treinamento em {skill}", "itemhelper_notification_you_are_to_stuffed_to_eat_this": "Você está muito cheio para consumir isso.", "forklift_notification_failed_to_load": "{amount} itens não puderam ser carregados.", "producer_notification_already_holding": "Este item já está segurando {name}.", "producer_notification_already_full": "{name} já está cheio!", "producer_notification_resource_not_fitting": "Este recurso não cabe neste tipo de estante.", "buildingmanager_notification_no_free_spot": "Não há vagas de veículos livres neste armazém.", "buildingmanager_notification_business_closed": "Este negócio está fechado no momento. Dica: Você pode descansar em bancos", "gamemanager_notification_save_successfull": "Jogo salvo com sucesso em: {name}", "handtruck_notification_cant_use_while_using_paperbag": "Você não pode usar um carrinho manual de carga ao carregar uma sacola de papel", "pointandclickobject_notification_cant_pick_occupied_objects": "Você não pode pegar um objeto que está ocupado.", "sleepingbench_notification_cant_use_with_handtruck": "Você não pode usar um banco enquanto estiver usando um carrinho manual de carga!", "joboffer_notification_already_got_job": "Você já conseguiu um emprego.", "managecargo_notification_vehicle_full": "{type} está cheio.", "itemoverlay_notification_no_storage_available": "Nenhum armazenamento disponível", "interiordesigner_cant_use_while_carrying_item": "Você não pode usar o designer de interiores enquanto carrega um item", "itempanelui_notification_sleeping_not_in_this_building": "Você não tem permissão para dormir dentro deste prédio", "itempanelui_notification_cant_grab_with_attachments": "Você não pode pegar um item com um item anexado", "itempanelui_notification_cant_grab_with_cargo_or_stock": "Você não pode pegar um item contendo carga ou estoque", "itempanelui_notification_cant_discard_with_attachments": "Você não pode descartar um item com um item anexado", "itempanelui_notification_cant_pack_with_cargo_or_stock": "Você não pode embalar uma carga de item ou estoque de prateleira", "itempanelui_notification_cant_pack_with_attchments": "Você não pode embalar um item com um item anexado", "itempanelui_notification_cant_sell_with_cargo": "Você não pode vender um veículo com carga", "itempanelui_notification_cant_add_to_temp_container": "Não é possível adicionar item a um contêiner temporário", "itempanelui_notification_cant_put_non_quanitity_items_into_temp_container": "Não é possível colocar um item não quantitativo em uma cesta de compras", "itempanelui_notification_cant_load_handtruck_into_handtruck": "Você não pode carregar um carrinho manual de carga em um carrinho manual de carga", "notificationui_notification_insufficient_energy": "Energia insuficiente", "notificationui_notification_insufficient_money": "<PERSON><PERSON><PERSON> insuficiente", "purchasevehicleui_notification_recently_purchase_vehicle_is_blocking": "Por favor, retire o seu veículo adquirido recentemente, antes de comprar outro.", "purchasevehicleui_notification_purchase_successfull": "Veículo adquirido com sucesso! Ele espera por você lá fora.", "buildingpreview_notification_you_are_already_in_the_same_building": "Você já está em um prédio como esse!", "bizman_purchasingagents_contact_notification_minimum_amount_not_reached": "O valor mínimo do pedido de {price} não foi atingido.", "bizman_purchasingagents_contact_notification_no_warehouse_assigned": "Um ou mais produtos não possuem depósito designado atribuído.", "bizman_schedule_notification_employee_missing_skill": "Este funcionário não tem habilidades adequadas para {itemname}", "bizman_schedule_notification_cant_assign_on_closed_days": "Os funcionários não podem ser atribuídos a dias agendados como fechados.", "bizman_warehouse_driverstation_notification_needs_skill": "O funcionário requer que a habilidade {skill} seja atribuída", "bizman_warehouse_driverstation_notification_driver_already_assigned": "Motorista já atribuído", "bizman_contractsettings_notification_max_boxes": "Quantidade máxima de caixas atingida", "bizman_presentation_notification_cant_terminate_when_inside": "Você não pode rescindir um contrato enquanto estiver dentro do prédio", "bizman_presentation_notification_itemsold": "Recebeu {price} pelos mó<PERSON>, estoque, veículos e depósito de {name}", "common_notification_invalid_amount": "Por favor, insira um valor válido", "bizman_presentation_notification_offer_rejected": "O proprietário da empresa rejeitou sua oferta de {price}", "bizman_presentation_notification_offer_accepted": "O proprietário da empresa aceitou sua oferta. Você agora é o proprietário de {name}. Parabéns!", "bizman_presentation_notification_fundamentalbusinessadministration_course_required": "Curso Fundamental de Administração de Empresas obrigatório para abrir mais de um negócio", "bizman_presentation_notification_no_business_name_entered": "Nenhum nome comercial inserido", "bizman_presentation_notification_no_business_type_entered": "Nenhum tipo de empresa inserido", "bizman_schedule_notification_headquaters_cant_change_opening_hours": "O horário de funcionamento não pode ser alterado para a sede.", "bizman_settings_notification_name_empty": "O nome da empresa não pode ficar vazio", "bizman_settings_notification_type_empty": "A empresa precisa ter um tipo", "bizman_settings_notification_name_duplicated": "Nome da empresa já em uso", "bizman_settings_notification_save_successfull": "Informações comerciais salvas com sucesso.", "feedback_notification_success": "Recebemos seu feedback. <PERSON><PERSON> o<PERSON>!", "feedback_notification_failure": "Caramba, algo deu errado! Por favor, nos deixe saber sobre este erro. Certifique-se de copiar/colar seus comentários antes de fechar esta janela.", "myemployees_unassign_for_training": "O funcionário deve ser desatribuído de qualquer empresa.", "buildingmanager_notification_warehouse_gate_is_bocked": "O portão está bloqueado do outro lado.", "common_close": "<PERSON><PERSON>", "deliveryplans_hud_confirm_discard_destination": "Tem certeza de que deseja descartar este destino?", "contact_hud_confirm_end_parnetship": "Tem certeza de que deseja encerrar esta parceria?", "econoviewloans_hud_confirm_payback_loan": "Tem certeza de que deseja devolver o valor total de {loan}?", "myemployees_hud_confirm_start_training": "Tem certeza que deseja começar a treinar <b>{skill}</b> até <b>{value}%</b>? Custará <b>{price}</b> e durará <b>1 dia</b>. O treinamento de funcionários também aumentará seu salário por hora.", "myemployees_hud_confirm_fire_employee": "Tem certeza de que deseja demitir este funcionário?", "common_help": "<PERSON><PERSON><PERSON>", "buildingmanager_notification_private_property": "Este edifício é uma propriedade privada.", "persona_characterinfo_age": "Idade: <b>{age}</b>", "persona_characterinfo_networth": "Valor líquido total: <b>{amount}</b>", "persona_characterinfo_businesses": "Neg<PERSON><PERSON><PERSON> totais: <b>{amount}</b>", "persona_characterinfo_weeklyincome": "Renda semanal: <b>{amount}</b>", "common_energy": "Energia", "common_hunger": "Fome", "persona_personal_goals_header": "<PERSON><PERSON> pessoais ({progress})", "persona_personal_goals_reward": "Recompensa {reward}", "persona_personal_goals_rewards": "Recompensas {reward}", "persona_personal_goals_no_rewards": "Sem recompensas", "persona_personal_goals_reward_building_unlocked": "{name} desb<PERSON>queado", "timestamp_full": "Dia {day}, {time}", "contacts_new_message": "Você recebeu uma nova mensagem de <b>{sender}</b>", "neighborhood_garmentdistrict": "Garment District", "neighborhood_hellskitchen": "Hell's Kitchen", "neighborhood_murrayhill": "<PERSON> Hill", "neighborhood_midtown": "Midtown", "neighborhood_global": "Global", "dialog_bank_amount_to_loan": "Valor do empréstimo", "tutorial_8_objective_wait_for_candiate": "Aguarde a agência enviar o primeiro candidato por meio do aplicativo <b>Meus Funcionários</b>.", "dialog_bank_larry": "<PERSON>", "dialog_bank_npc_name": "Bancario", "dialog_bank_loan_amount_header": "Valor do empréstimo", "dialog_import_npc_name": "Gerente de importação", "dialog_import_partnership_header": "Parceria de importação", "dialog_marketing_agency_npc_name": "Representante de Marketing", "dialog_marketing_agency_campaign_settings_header": "Definições da campanha", "dialog_recruitment_agency_npc_name": "Agente de recrutamento", "dialog_recruitment_agency_candidate_properties_header": "Propriedades do candidato", "dialog_wholesale_store_npc_name": "<PERSON><PERSON><PERSON>", "dialog_wholesale_store_delivery_contract_header": "Contrato de entrega", "dialog_bank_start": "Olá! Quanto dinheiro você quer pedir emprestado?", "dialog_bank_loan_accepted": "Com certeza, nós podemos ajudar você com isso. Farei com que o valor total seja transferido para sua conta bancária imediatamente. <br><PERSON><PERSON><PERSON> por fazer negócios conosco!", "dialog_bank_loan_denied_1": "Receio que seja mais do que podemos oferecer. Com suas atuais economias, não poderemos emprestar a você mais que um total de {amount}", "dialog_bank_loan_maximum_exceeded": "Infelizmente, não podemos oferecer um valor de empréstimo combinado superior a {amount} por cliente. Juntando suas atividades existentes em nosso banco com o valor solicitado, ultrapassaremos esse limite.", "dialog_bank_loan_fraud_physical": "Um valor negativo? <PERSON><PERSON><PERSON>, você está tentando abusar de nossos sistemas de TI? Quer que eu chame a polícia? SAIA DAQUI!!!", "dialog_bank_loan_fraud_phone": "Um valor negativo? <PERSON><PERSON><PERSON>, você está tentando abusar de nossos sistemas de TI? Quer que eu chame a polícia? <i>*desliga furiosamente*</i>", "dialog_bank_loan_zero_input": "Peço desculpas se nossos termos bancários são um pouco difíceis de entender, mas realmente só precisamos que você nos diga quanto deseja pedir emprestado.", "dialog_bank_loan_too_low": "<PERSON><PERSON><PERSON><PERSON>, mas não podemos conceder-lhe um empréstimo inferior a {amount}.", "dialog_bank_loan_denied_2": "<PERSON><PERSON><PERSON>, mas não podemos oferecer a você um empréstimo com suas atuais economias.", "dialog_bank_loan_request_player": "Eu gostaria de solicitar um empréstimo de ${amount}", "dialog_try_again_button": "Tentar novamente", "dialog_leave_button": "<PERSON><PERSON>", "dialog_close_button": "<PERSON><PERSON><PERSON>", "dialog_recruitment_agency_start": "Olá e seja bem-vindo a {businessName}. Que tipo de funcionário você deseja contratar?", "dialog_recruitment_agency_required_diploma_not_found": "<PERSON><PERSON><PERSON><PERSON>, mas antes que possamos ajudá-lo a contratar seu primeiro funcionário, você é obrigado por lei a concluir o curso <b>Gestão Básica</b>.<br> Recomendamos a Manhattan Business School.", "dialog_recruitment_agency_already_has_campaign_active": "Ainda estamos procurando os candidatos que combinamos da última vez. Tem certeza que deseja cancelar o recrutamento?", "dialog_recruitment_agency_on_cancel_campaign_recruiter": "<PERSON><PERSON><PERSON>, vamos finalizar esta campanha imediatamente.", "dialog_recruitment_agency_on_cancel_campaign_player": "<PERSON><PERSON>, cancele por favor", "dialog_recruitment_agency_on_recruitment_settings_set_recruiter": "<PERSON><PERSON> obri<PERSON>. Enviaremos o currículo dos candidatos assim que os encontrarmos.", "dialog_recruitment_agency_on_recruitment_settings_set_player": "Quero {amountOfCandidates} candidatos para {businessName} com boas habilidades em <lowercase>{skillKey}</lowercase>. <br>Os candidatos devem ser encontrados em {days} dia(s) e estar na faixa etária de: {ages}", "dialog_hang_up_button": "<PERSON><PERSON><PERSON>", "skillname_customerservice": "Atendimento ao Cliente", "skillname_cleaning": "Limpeza", "skillname_lawyer": "Advogado", "skillname_purchasingagent": "Agente de compra", "skillname_logisticsmanager": "Gerente de logística", "skillname_deliverydriver": "Motorista de entrega", "skillname_programmer": "Programador", "dialog_recruitment_agency_cancel_current_campaign": "Cancelar recrutamento", "dialog_import_start": "Olá e seja bem-vindo! Como posso ajudá-lo?", "dialog_import_no_warehouses": "<PERSON><PERSON><PERSON><PERSON>, mas sem ter um armazém, não podemos entregar as mercadorias para você.", "dialog_import_no_purchasing_agents": "<PERSON><PERSON><PERSON>, mas só podemos fazer negócios com você se você tiver um agente de compras livre e disponível para lidar com a parceria.", "dialog_import_on_partnership_settings_set_manager": "Maravilhoso! Estamos ansiosos para fazer negócios com você. Continuarei a negociação com {selectedEmployee}. Tenha um ótimo dia!", "dialog_import_on_partnership_settings_set_player": "Gostaria de estabelecer uma parceria gerenciada por nosso agente de compras {selectedEmployee}", "dialog_import_create_new_partnership": "Nova parceria", "dialog_accept_button": "Aceitar", "dialog_decline_button": "Recusar", "workpanel_start_working": "Comece a trabalhar", "workpanel_shift_ends_at": "O turno termina às {time}", "workpanel_employee_is_currently_assigned": "{name} está atualmente atribuído", "workpanel_business_is_currently_closed": "O negócio está fechado no momento", "workpanel_business_shift_not_yet_started": "Seu turno ainda não começou.", "workpanel_fast_forward": "Avanço rápido de tempo", "workpanel_unassign_self": "Cancelar a atribuição", "dialog_marketing_agency_start": "O<PERSON><PERSON> e seja bem-vindo a {businessName}. Em que tipo de anúncio você está interessado?", "dialog_marketing_agency_no_businesses": "<PERSON><PERSON><PERSON><PERSON>, mas sem ter um negócio, não podemos ajudar você a fazer campanhas de marketing.", "dialog_marketing_agency_on_campaign_settings_set_manager": "<PERSON><PERSON> obri<PERSON>. Vamos montar sua campanha imediatamente!", "dialog_marketing_agency_on_campaign_settings_set_player": "Quero um {marketingType} para {businessName}.", "dialog_wholesale_store_start": "Ol<PERSON>! Como posso ajudá-lo?", "dialog_wholesale_store_no_businesses": "<PERSON><PERSON><PERSON><PERSON>, mas sem ter um negócio, não podemos ajudar você com contratos de entrega.", "dialog_wholesale_store_on_contract_settings_set_manager": "Tudo bem! Nós temos um acordo.", "dialog_wholesale_store_on_contract_settings_set_player": "Gostaria de iniciar um contrato para o negócio {businessName}", "dialog_wholesale_start_contract": "Iniciar contrato", "contacts_message_calling_outside_working_hours": "Você nos contatou fora do nosso horário comercial. Por favor, tente novamente mais tarde.", "contacts_message_occupied": "Estamos atualmente ocupados. Por favor, ligue novamente mais tarde. Obrigada!", "contacts_message_not_implemented": "Ligar para {businessType} ainda não foi implementado.", "contacts_message_player_cancel_call": "<PERSON>ão importa, não estou mais interessado", "phone_import_partnership_delivery_not_enough_funds": "Não foi possível cobrar o valor {amount} necessário para prosseguir com a entrega.<br> O contrato foi cancelado.", "phone_import_partnership_delivery_no_available_space": "Não foi possível armazenar todas as caixas de {itemName} em {businessName}. Por favor, certifique-se de que teremos prateleiras de paletes disponíveis para a próxima entrega.", "phone_wholesale_store_delivery_not_enough_funds": "Não foi possível cobrar o {amount} necessário para prosseguir com a entrega de {businessName}.<br> A entrega foi cancelada.", "phone_wholesale_store_delivery_no_available_space": "Não foi possível armazenar todas as caixas de {itemName} em {businessName}. Por favor, certifique-se de que teremos prateleiras de armazenamento disponíveis para a próxima entrega.", "phone_recruitment_agency_new_candidate": "Olá! Boas notícias, encontramos um novo candidato para você:", "phone_government_parking_ticket": "<b>Aviso de infração de estacionamento</b><br><br> <PERSON><PERSON><PERSON> ou Senhora:<br><br> Estamos escrevendo para informar que seu veículo <b>{vehicleTypeName}</b> estava estacionado em local não permitido às {hour}:{minute}, dia {day}.<br><br> Foi cobrada automaticamente uma multa de estacionamento de <b>$125,00</b> da sua conta bancária principal.<br><br> At<PERSON>ciosamente,<br> Departamento de Finanças da Cidade de Nova York", "phone_boss_fire_message": "Este é o seu terceiro aviso. Estou farto de ti, estás despedido! Não volte!", "phone_boss_warning_onpremise": "<PERSON><PERSON>, onde você está? Seu hor<PERSON><PERSON> de trabalho é {startingHour}:00 - {endingHour}:00. Considere isso um aviso!", "phone_welcome_message_friendly_1": "Ol<PERSON>!", "phone_welcome_message_friendly_2": "Oi!", "phone_welcome_message_friendly_3": "Oláááá!", "phone_welcome_message_business_1": "<PERSON><PERSON><PERSON> por vir à {businessName}!", "phone_welcome_message_business_2": "A equipe de {businessName} gostaria de agradecer a sua presença em nossa empresa.", "phone_welcome_message_business_3": "Ol<PERSON>! Obrigado por vir a {businessName}. Se pudermos ajudá-lo, é só nos avisar!", "phone_welcome_message_business_4": "O<PERSON><PERSON> por usar os serviços da {businessName}. Você também pode entrar em contato conosco por telefone durante nosso horário de funcionamento.", "dialog_recruitment_agency_primary_skills": "Habilidade Primária", "dialog_recruitment_agency_amount_of_candidates": "Quantidade de candidatos", "dialog_recruitment_agency_days_to_deliver": "Dias para entrega", "common_total_price": "Preço total", "common_business": "<PERSON>eg<PERSON><PERSON>", "common_type": "Tipo", "common_price_per_day": "Preço por dia", "dialog_marketing_agency_impressions_per_week": "Impressões por semana", "dialog_import_purchasing_agent": "Agente de compra", "dialog_delivery_fee": "Taxa de entrega", "dialog_delivery_max_boxes": "Máximo de caixas", "common_hourly_wage": "<PERSON><PERSON><PERSON> por hora: <b>{hourlyWage}</b>", "phone_call_button": "Ligar", "phone_view_on_map_button": "Ver no mapa", "contacts_today_opening_hours": "Horário de funcionamento de hoje", "contacts_today_opening_hours_closed": "<PERSON><PERSON><PERSON>", "contacts_today_opening_hours_open": "{startingHour} - {endingHour}", "uncle_fred": "<PERSON><PERSON>", "the_city_of_new_york": "A cidade de Nova York", "friends_and_family": "Família e amigos", "government": "Governo", "common_address": "Endereço", "job_board": "Quadro de empregos", "sleeping_bench": "Banco", "sleeping_bench_click_to_sleep": "Clique para dormir", "common_storage_capacity_status": "Armazenamento: {amount}", "common_cargo_capacity_status": "Carga: {amount}", "fridgecontroller_empty_fridge": "Esvaziar", "itemcontroller_remove_content": "Remover conteúdo", "common_manage_storage": "Gerenciar armazenamento", "educationdoorcontroller_learn": "Aprender", "myemployees_employee_name": "nome do empregado", "myemployees_age": "<PERSON><PERSON>", "myemployees_task": "<PERSON><PERSON><PERSON>", "myemployees_primary_skill": "Habilidade Primária", "myemployees_current_business": "<PERSON><PERSON><PERSON><PERSON>", "myemployees_hours_per_week": "<PERSON>ras por semana", "myemployees_satisfaction": "Satisfação", "myemployees_hourly_wage": "<PERSON><PERSON><PERSON> por hora", "workpanel_assign_self": "Atribuir", "common_product": "produto", "marketinsider_demand": "<PERSON><PERSON><PERSON>", "marketinsider_import_price_index": "Índice de preços de importação", "marketinsider_providers": "Fornecedores", "common_number_of_businesses": "{amount} empresas", "common_years_amount": "{years} anos", "common_hours_per_week": "{weeklyHours} horas por semana", "tooltip_right_click_to_manage": "Clique com o botão direito para gerenciar", "requirement_on_counter_top": "Colocado num armário ou vitrine", "requirement_no_duplicates": "<PERSON>ão duplicado", "common_unassigned": "Não atribuído", "myemployees_no_task": "<PERSON><PERSON> tarefa", "requirement_chair": "<PERSON><PERSON>", "requirement_desk": "Mesa", "feedback_title": "Enviar relatório de <PERSON>", "feedback_description": "Obrigado por dedicar seu tempo para nos ajudar a fazer do Big Ambitions o jogo com o qual todos sonhamos.<br><br> Usamos ativamente seus comentários para priorizar o que devemos trabalhar em seguida. Todos os comentários são 100% anônimos, a menos que você escreva seus dados na caixa de texto. <b>Por favor, deixe todos os comentários apenas em inglês</b><br><br> Obrigada!<br> <PERSON><PERSON>", "feedback_input_field_placeholder": "Digite seus comentários aqui. Se tiver alguma dúvida, use os fóruns ou o Discord, caso contr<PERSON>rio, suas perguntas não serão respondidas.", "feedback_submit_button": "Enviar relatório de <PERSON>", "feedback_system_data_toggle": "Inclua informações do sistema, savegame, logs do jogador e captura de tela para nos ajudar a corrigir o bug.", "myemployees_title": "Funcionários", "common_age": "Idade: <b>{age}</b>", "common_gender": "Gênero: <b>{gender}</b>", "myemployees_days_hired": "Dias contratados: <b>{daysHired}</b>", "myemployees_called_in_sick": "{employeeName} ligou dizendo que está doente hoje", "myemployees_skills": "Habilidades", "myemployees_wants_and_demands": "Desejos e demandas", "myemployees_manage_schedule": "Gerenciar agenda", "myemployees_fire": "<PERSON><PERSON><PERSON>", "myemployees_satisfaction_description": "A satisfação depende diretamente das demandas atendidas. Quanto menos, menos qualidade eles entregam. Funcionários completamente insatisfeitos vão se demitir.", "myemployees_currently_training_text": "O funcionário está atualmente treinando a habilidade:", "help_button": "AJUDA", "jobdemand_freeweekends": "Sem fins de semana", "common_events": "Eventos", "myemployees_train_skill_button": "Treinar habilidade", "common_businesses": "<PERSON>eg<PERSON><PERSON><PERSON>", "bizman_menu_insight": "Entendimento", "bizman_menu_inventory_pricing": "Inventário e preços", "bizman_menu_schedule": "<PERSON><PERSON><PERSON><PERSON>", "bizman_menu_deliveries": "Entregas", "bizman_menu_settings": "Configurações", "bizman_menu_marketing": "Marketing", "bizman_menu_logistics_managers": "Gerentes de Logística", "bizman_menu_purchasing_agents": "Agentes de compras", "bizman_menu_hr_managers": "Gerentes de RH", "bizman_menu_drivers": "Motoristas", "bizman_menu_inventory": "Estoque", "common_day_number": "Dia {number}", "bizman_insight_customer_satisfaction": "Satisfação do cliente", "bizman_insight_promotion": "Promoção", "bizman_insight_promotion_description": "A promoção indica quantos clientes seu negócio atrai.", "bizman_traffic_index": "Índice de tráfego", "bizman_insight_marketing": "Marketing", "bizman_insight_satisfaction": "Satisfação", "bizman_insight_satisfaction_description": "A satisfação determina quanto dinheiro um cliente gasta no seu negócio.", "bizman_insight_customer_service": "Atendimento ao Cliente", "bizman_insight_pricing": "Preço", "bizman_insight_cleanliness": "Limpeza", "marketeventtype_businessopened_description": "{rivalName} abriu <b>{businessName}</b> em {address}.", "marketeventtype_businessclosed_description": "{rivalName} encerrou as atividades de <b>{businessName}</b> em {address}.", "marketeventtype_hype_description": "Os cidadãos estão mostrando recentemente uma forte demanda por <b>{itemName}</b> .", "marketeventtype_productshortage_description": "Os importadores estão relatando uma escassez temporária de <b>{itemName}</b> . A duração prevista é de {durationInDays} dias.", "marketeventtype_largeplayerpurchase_description": "Os importadores estão relatando uma escassez temporária de <b>{itemName}</b> . A duração prevista é de {durationInDays} dias.", "marketeventtype_productbackorder_description": "Há um pedido pendente global atual de <b>{itemName}</b> . A duração prevista é de {durationInDays} dias.", "bizman_insight_customers": "Clientes", "bizman_customers_capacity": "Capacidade do cliente", "bizman_insight_customers_capacity_description": "Você só pode obter tantos clientes por hora quanto sua capacidade permitir. Aumentar e equilibrar a quantidade de pontos de vendas disponíveis, prateleiras de produtos etc. resultará diretamente em mais possíveis clientes por hora.", "bizman_insight_current_capacity_per_hour": "Capacidade atual por hora", "bizman_insight_building_limit": "Limite de construção", "bizman_insight_customers_over_time": "Clientes ao longo do tempo", "bizman_insight_customers_chart_yesterday": "Ontem", "marketeventtype_businessopened": "<PERSON><PERSON><PERSON><PERSON>", "marketeventtype_businessclosed": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "marketeventtype_hype": "Hype", "marketeventtype_productshortage": "Escassez do produto", "marketeventtype_largeplayerpurchase": "Escassez do produto", "marketeventtype_productbackorder": "Pedido em espera do produto", "capacity_type_shopping_baskets": "Cestas de Compras", "capacity_type_point_of_sales": "Ponto de <PERSON>ndas", "capacity_type_desktop_workstations": "Estações de trabalho", "bizman_insight_shelf_type_capacity": "{shelfAmount} x {shelfLabel} ({customersPerHour})", "businesstype_webdevelopmentagency": "Agência de Desenvolvimento Web", "bizman_insight_customers_chart_last_week": "7 dias", "bizman_schedule_cleaning_employees_toggle": "Ocultar funcionários de limpeza", "bizman_schedule_shared_schedule": "Agenda compartilhada para todos os dias", "bizman_schedule_opening_hours": "Horário de funcionamento", "bizman_schedule_business_closed": "O comércio está fechado neste dia. Clique para abrir.", "bizman_schedule_employee": "{employeeName} ({employeeWeeklyHours} H/SEMANA)", "subwaystation_hellskitchennorthstation": "Hells Kitchen StationNorte", "subwaystation_hellskitchensouthstation": "Hells Kitchen Station South", "subwaystation_garmentdistricteaststation": "Garment DistrictEstação Leste", "subwaystation_garmentdistrictharborstation": "Garment District Harbour Station", "subwaystation_midtownstpatrickstation": "St. Patrick Station", "subwaystation_midtowntimessquarestation": "Times Square Station", "subwaystation_midtowngrandcentralstation": "Grand Central Station", "bizman_schedule_opening_hour_slot": "{startingHour} - {endingHour} ({hours} horas)", "bizman_delivery_contracts": "Contratos de entrega", "bizman_delivery_contract_enabled": "Entrega {day}", "bizman_delivery_contract_disabled": "Desativado", "bizman_delivery_contract_settings": "Configurações do contrato", "bizman_delivery_contract_cancel_contract": "Cancelar contrato", "bizman_delivery_contract_delivery_day": "Dia de entrega", "bizman_delivery_contract_delivery_fee": "Taxa de entrega: {fee}", "bizman_delivery_contract_boxes_limit": "Limite: {totalBoxes}/{maxBoxes} caixas", "bizman_delivery_contract_item_boxes": "{boxes} caixas ({boxesPrice})", "bizman_delivery_contract_add_entry": "Adicionar entrada", "bizman_delivery_contract_total_price_per_delivery": "Preço total por entrega: {totalPricePerDelivery}", "bizman_inventory_title": "Inventário do Armazém", "bizman_inventory_in_stock": "Em estoque", "bizman_inventory_shipped": "Enviado", "bizman_inventory_import": "Importar", "bizman_inventory_balance": "<PERSON><PERSON>", "bizman_inventory_days_until_empty": "Dias até ficar vazio", "bizman_inventory_product_days_until_empty": "{days} dias", "bizman_inventory_run_out": "Acabou", "bizman_inventory_never_runs_out": "Nunca", "bizman_drivers": "Motoristas", "bizman_drivers_slot_number": "Vaga #{number}", "bizman_drivers_vehicle_info": "Entre com o veículo pela porta da garagem do armazém para atribuí-lo a esta vaga", "bizman_drivers_driver_info": "Arraste o funcionário aqui para atribuir", "bizman_drivers_driver": "Motorista", "bizman_drivers_vehicle": "Veí<PERSON>lo", "bizman_products_inventory": "Produtos e inventário", "bizman_sold_last_7_days": "Vendido nos últimos 7 dias", "bizman_compared_last_period": "Comparado com {amount} último per<PERSON>", "bizman_based_on_7_days": "Baseado nos últimos 7 dias", "common_gross_profit_margin": "<PERSON><PERSON><PERSON> de lucro bruto", "common_best_selling_products": "<PERSON><PERSON>tos mais vendidos", "bizman_settings_manage_business_label": "Gerenciar negó<PERSON>", "bizman_business_information": "Informação de negócios", "bizman_business_information_description": "Escolha um novo nome, tipo ou logotipo para este negócio.", "bizman_customize_logo": "Personalizar logotipo", "bizman_logo_shape": "Forma do logotipo", "bizman_select_from_disk": "Selecionar do disco", "common_font": "Fonte", "bizman_logo_color": "Cor do logotipo", "bizman_font_color": "<PERSON><PERSON> da fonte", "bizman_background_color": "Cor do fundo", "bizman_sign_explanation": "As configurações do sinal podem ser acessadas de fora, clicando com o botão direito do mouse no prédio", "bizman_shutdown_business": "<PERSON><PERSON><PERSON>", "bizman_shutdown_business_description": "Você pode encerrar esta empresa para fechá-la permanentemente. Essa ação não pode ser desfeita!", "businesstype_warehouse": "Armazém", "bizman_select_business_type": "Selecione o tipo de negócio", "common_open_in_econoview": "Abrir no EconoView", "common_campaigns": "<PERSON><PERSON><PERSON>", "bizman_marketing_no_campaigns": "Você não tem nenhuma campanha de marketing ativa. Visite uma agência de marketing para configurar um.", "common_impressions_per_day": "Impressões por dia", "common_daily_expense": "Despesa diária", "common_days_amount_left": "{amount} dias restantes", "common_expired": "<PERSON><PERSON><PERSON>", "bizman_marketing_cancel_campaign": "<PERSON><PERSON><PERSON> camp<PERSON>", "bizman_marketing_auto_renew": "Auto-renovação", "bizman_marketing_restart_campaign": "<PERSON><PERSON><PERSON><PERSON> campan<PERSON>", "marketinggroups_internet_marketing": "Marketing na Internet", "common_show_warehouse": "Mostrar armazém", "bizman_logisticsmanagers_deliver_to_destinations_amount": "Entre<PERSON><PERSON> até <b>{amount}</b> destinos", "bizman_logisticsmanagers_hint_seated_only": "Somente Gerentes de Logística sentados podem ser acessados", "bizman_logisticsmanagers_adddestination": "Adicionar novo destino", "bizman_logisticsmanagers_delivery_plan_for_businessname": "Plano de entrega para <b>{businessName}</b>", "bizman_logisticsmanagers_warehouse_stock": "Estoque de armazém", "bizman_logisticsmanagers_min_stock_amount": "Quantidade mínima de estoque", "bizman_logisticsmanagers_runs_out_in": "Acaba em", "bizman_logisticsmanagers_destination_number": "<PERSON><PERSON> #{number}", "bizman_logisticsmanagers_no_vehicle_assigned": "Nenhum veí<PERSON>lo <PERSON>", "bizman_logisticsmanagers_no_driver": "Sem motorista", "transactiontype_undefined": "Indefinido", "transactiontype_cheat": "DÊ-ME DINHEIRO!!!!", "transactiontype_subwayride": "Passeio de met<PERSON>ô", "transactiontype_recruitmentcampaign": "Campanha de Recrutamento", "transactiontype_itemsold": "Vendido {itemSoldInfo}", "transactiontype_deliverycontractrefund": "{itemQuantityFormat} para {businessName}", "transactiontype_importdeliveryrefund": "<PERSON><PERSON><PERSON><PERSON> de {itemQuantityFormat} para {warehouseName} da {businessName}", "transactiontype_marketing": "Campanhas de marketing da {businessName}", "transactiontype_tuitionfee": "{diplomaName} ({hours} horas, {minutes} minutos)", "transactiontype_deliverycontract": "Entrega de {businessName} de {warehouseName}", "transactiontype_unassignedwage": "{employee} ({businessName} <PERSON><PERSON><PERSON>", "transactiontype_wage": "{employee} (Pa<PERSON><PERSON> diá<PERSON> {businessName})", "transactiontype_employeetraining": "Treinamento de {skillName} para {employee}", "transactiontype_playerjobsalary": "<PERSON><PERSON><PERSON> {businessName}", "transactiontype_loanpayment": "Pagamento do Empréstimo {businessName}", "transactiontype_loanpayoff": "Pagamento do Empréstimo {businessName}", "transactiontype_revenue": "<PERSON><PERSON><PERSON> de {businessName}", "transactiontype_importdelivery": "Entrega de {businessName}", "transactiontype_loanpayout": "Pagamento do Empréstimo {businessName}", "transactiontype_depositreturn": "<PERSON><PERSON><PERSON><PERSON> devolvido para {address}", "transactiontype_depositreturnfurniture": "<PERSON><PERSON><PERSON>is, estoque, veículos e depósito de {address}", "transactiontype_rent": "<PERSON><PERSON><PERSON> {address}", "transactiontype_deposit": "<PERSON><PERSON><PERSON><PERSON> {address}", "transactiontype_parkingticket": "Multa por estacionamento não permitido de {vehicleName}", "transactiontype_vehiclebought": "{vehicleName}", "bizman_purchasingagents_hint": "Apenas Agentes Comprados sentados podem ser acessados", "bizman_purchasingagents_no_contract": "Sem contrato", "bizman_purchasingagents_minimum_order": "<PERSON>ed<PERSON>", "bizman_purchasingagents_end_partnership": "Encerrar parceria de importação", "bizman_purchasingagents_next_delivery": "Próxima entrega", "bizman_purchasingagents_next_delivery_day": "Dia {nextDeliveryDay} às {time}", "bizman_purchasingagents_total_price": "Total {totalPrice}", "bizman_purchasingagents_order": "Fazer pedido", "bizman_purchasingagents_cancel_order": "Cancelar pedido", "bizman_purchasingagents_amount_to_buy": "# comprar", "bizman_purchasingagents_price": "Preço", "bizman_purchasingagents_designated_warehouse": "Armazém designado", "bizman_purchasingagents_product_price": "{totalPrice} ({pricePerPiece} cada)", "bizman_purchasingagents_product": "Produ<PERSON>", "smartphone_contacts": "Contatos", "bizman_purchasingagents_deliver_one_time": "Individual", "bizman_purchasingagents_deliver_every_x_days": "Reposição automática a cada {x} dia(s)", "itempanelui_cargo": "Carga:", "common_unknown": "Desconhecido", "common_select_type": "Selecione o tipo", "common_select_color": "Selecione a cor", "sign_type": "Tipo {number}", "colors_red": "Vermelho", "colors_green": "Verde", "colors_yellow": "<PERSON><PERSON>", "colors_darkgrey": "Cinza escuro", "colors_black": "Preto", "colors_blue": "Azul", "colors_white": "Branco", "colors_lime": "Verde Limão", "colors_lightgrey": "Cinza claro", "colors_darkgreen": "Verde escuro", "specialbuilding_bank": "Banco", "specialbuilding_school": "Escola", "specialbuilding_cardealership": "Concessionária de carros", "specialbuilding_appliancestore": "Loja de eletrodomésticos", "specialbuilding_wholesalestore": "Loja de atacado", "specialbuilding_recruitmentagency": "Agência de recrutamento", "specialbuilding_furniturestore": "Loja de móveis", "specialbuilding_marketingagency": "Agência de marketing", "specialbuilding_officesupplystore": "Loja de material de escritório", "specialbuilding_importexport": "Importação/Exportação", "menu_options_select_resolution": "Selecione a resolução", "menu_options_select_fps_limit": "Selecione Limite de FPS", "menu_options_none": "<PERSON><PERSON><PERSON>", "menu_options_fps_vsync": "VSync", "menu_options_fps_x_fps": "{x} FPS", "menu_options_quality_low": "Baixo", "menu_options_quality_medium": "Médio", "menu_options_quality_high": "Alto", "menu_options_aa_fxaa": "FXAA", "menu_options_aa_taalow": "TAA baixo", "menu_options_aa_taamedium": "TAA Médio", "menu_options_aa_taahigh": "TAA alto", "menu_options_aa_smaalow": "SMAA baixo", "menu_options_aa_smaamedium": "SMAA Médio", "menu_options_aa_smaahigh": "SMAA Alta", "menu_wishlist_description": "Seja notificado quando o Big Ambitions for lançado. Coloque já na lista de desejos!", "common_wishlist_now": "Lista de desejos agora!", "close_button": "<PERSON><PERSON><PERSON> [ESC]", "character_customization_step_one": "Customização do personagem - Etapa 1", "character_customization_step_one_title": "Etapa 1 - Informações básicas", "character_customization_step_two": "Customização do personagem - Etapa 2", "character_customization_step_two_title": "Etapa 2 - Detalhes do corpo", "character_customization_gener": "<PERSON><PERSON><PERSON><PERSON>", "character_customization_skin_color": "<PERSON><PERSON> da pele", "common_next": "Próximo", "common_previous": "Anterior", "common_finish": "Terminar", "character_customization_hair": "<PERSON><PERSON><PERSON>", "character_customization_head": "Cabeça", "character_customization_torso": "Tronco", "character_customization_legs": "<PERSON><PERSON>", "character_customization_shoes": "Sapatos", "intro_paragraph_one": "Faz 3 meses que a vovó morreu. Eu sei que sou um adulto agora que tenho 18 anos, mas ainda assim...", "intro_paragraph_two": "É tão assustador que ninguém está lá para cuidar das coisas.", "intro_paragraph_three": "Há uma coisa boa embora. No funeral, meu tio Fred me pediu meu número de telefone.", "intro_paragraph_four": "Ele disse que queria me ajudar a me estabelecer. Eu realmente não o conheço, mas acho que ele é da família, afinal.", "character_customization_hair_01": "Cabelo 1", "character_customization_hair_02": "Cabelo 2", "character_customization_torso_01": "Moletom", "character_customization_torso_02": "Paletó", "character_customization_torso_03": "<PERSON><PERSON><PERSON>", "character_customization_legs_01": "Bermuda", "character_customization_legs_02": "<PERSON><PERSON>", "character_customization_legs_03": "Calças de Terno", "character_customization_head_01": "Cabeça 1", "character_customization_head_02": "Cabeça 2", "character_customization_shoes_01": "Sapatos", "character_customization_shoes_02": "Sapatos social", "character_customization_shoes_03": "<PERSON><PERSON><PERSON>", "character_customization_shoes_04": "<PERSON><PERSON><PERSON> sociais", "new_character_save_game": "Novo jogo salvo de {character}", "common_distance_meters": "{distance} m", "help_title": "Sistema de Ajuda", "common_neighborhoods": "Bairros", "bizman_building_area": "<PERSON><PERSON> construí<PERSON>", "bizman_building_area_value": "{squaremeters} ({buildingSize}{buildingVersion})", "bizman_market_value": "Valor de mercado", "common_neighborhood": "Bairro", "bizman_building_buildingtype": "<PERSON><PERSON><PERSON><PERSON> {type}", "buildingtype_residential": "Residencial", "buildingtype_retail": "Varejo", "buildingtype_office": "Escritório", "buildingtype_warehouse": "Armazém", "buildingtype_special": "Especial", "bizman_buy_building": "Comprar <PERSON><PERSON><PERSON>", "bizman_not_for_sale": "Este edifício não está à venda", "bizman_preview_button": "Visualização", "condition_average": "Média", "bizman_estimated_valuation": "Valor estimado: <b>{valuation}</b>", "bizman_send_overtake_offer_button": "Enviar oferta para assumir o negócio", "business_description_appliance_store_1": "Loja familiar de eletrônicos que oferece eletrodomésticos de nível industrial", "business_description_appliance_store_2": "Temos todos os eletrodomésticos para sua casa ou empresa!", "business_description_bank_1": "Banco privado desde 1902", "business_description_bank_2": "<PERSON><PERSON><PERSON> pessoas e empresas a crescer como um banco justo e transparente", "business_description_car_dealership_1": "Sua concessionária de veículos local", "business_description_car_dealership_2": "Revendedor autorizado de caminhões industriais desde 2011", "business_description_furniture_store": "Móveis escandinavos de qualidade e fáceis de montar", "business_description_import_1": "Importação e exportação de vários tipos de produtos de varejo", "business_description_import_2": "Importação de cargas de produtos de atacado para a indústria alimentícia", "business_description_marketing_1": "Com mais de 50 anos de experiência, a CityAds é sua melhor opção para anúncios físicos em outdoors.", "business_description_marketing_2": "Agência de marketing moderna especializada em anúncios online", "business_description_office_supplies": "Obtenha o melhor dos seus funcionários com nossos móveis de escritório de última geração!", "business_description_recruitment_agency_1": "Somos a principal agência de recrutamento de Nova York", "business_description_recruitment_agency_2": "Agência de recrutamento sediada em Manhattan especializada em funcionários operacionais", "business_description_school": "Eleve suas habilidades de negócio", "business_description_wholesalestore_1": "Oferecemos produtos locais de atacado B2B", "business_description_wholesalestore_2": "Fundada em 2001, a NY Distro é a principal fornecedora de atacado em Manhattan", "common_amount": "Valor", "bizman_send_offer_button": "Enviar of<PERSON>a", "common_name": "Nome", "bizman_start_new_business": "Iniciar um novo negócio", "bizman_choose_business_name": "Digite o nome do seu negócio e confirme:", "bizman_start_business_button": "Iniciar <PERSON>", "bizman_residential_description": "Este edifício é zoneado apenas para uso residencial.", "bizman_start_new_business_button": "Iniciar novo negócio", "bizman_building_price_label": "<#{color}>{deposit}\n{defaultLayoutCost}</color>\n{calculatedDailyRent}", "bizman_rent_building_button": "Alugar ed<PERSON><PERSON><PERSON>", "bizman_terminate_contract_button": "Rescindir contrato", "bizman_building_price_label_description": "<#ABABAB><PERSON><PERSON><PERSON><PERSON>\nEletrodomésticos</color>\n<PERSON><PERSON><PERSON>", "contacts_your_contacts": "Seus contatos", "econoview_last_transactions": "Últimas transações", "econoview_loans": "Emprést<PERSON><PERSON>", "econoview_loan_cost_per_day": "{costPerDay} por dia", "econoview_loan_pay_off_button": "Pagamento ({remainingAmount})", "econoview_dashboard": "<PERSON><PERSON>", "econoview_income_statement": "Declaração de renda", "econoview_yesterday_date": "Ontem (dia {dayNumber})", "econoview_row_undefined": "Indefinido", "econoview_row_businesses": "<PERSON>eg<PERSON><PERSON><PERSON>", "econoview_row_ongoing_expenses": "Custo fixo", "econoview_row_private_residences": "Residências Privadas", "econoview_row_loans": "Emprést<PERSON><PERSON>", "econoview_row_unassigned_staff_wages": "Salários de funcionários não atribuídos", "econoview_row_total": "Total", "econoview_row_profit": "<PERSON><PERSON>", "econoview_row_salaries": "Salários", "econoview_row_rent": "<PERSON><PERSON><PERSON>", "econoview_row_marketing": "Marketing", "econoview_row_sales": "<PERSON><PERSON><PERSON>", "econoview_row_resources": "Recursos", "common_requirements": "Requisitos", "bizman_market_price": "Menor preço do mercado", "bizman_retail_price": "<PERSON>u preço", "bizman_stock_count": "Contagem de estoque", "transactiontype_itempurchase": "Compre de {businessName}", "common_loading": "Carregando...", "common_full_address": "{number} {street}", "menu_options_reset_to_defaults": "Redefinir para os padrões", "job_cashier_description": "Responsável por atender os clientes na caixa registradora", "common_loaded": "Carregado", "purchaseui_descriptions_coming_later": "Descrições disponíveis mais tarde...", "purchaseui_waiting_in_queue": "Esperando na fila...", "joboffer_accept_job": "Aceitar emprego", "itemoverlay_contents_headline": "Conte<PERSON><PERSON>", "itemoverlay_missing_requirements_headline": "Requisitos ausentes", "poi_custom_location": "Localização personalizada", "dialog_import_select_purchasing_agent": "Selecione o agente de compras", "dialog_select_business": "Selecionar negócio", "dialog_select_skill": "Selecionar habilidade", "pallet_shelf_price_label": "{totalPrice} ({quantity} peças)", "itempanelui_buttons_skip": "Pular musica", "itempanelui_buttons_toggle_radio": "Alternar <PERSON>", "businesstype_importexport": "Importar/Exportar", "common_days": "{value} dias", "school_closing_soon_warning": "É tarde demais para começar uma sessão agora. Volte amanhã.", "common_value": "{value}", "common_weeks": "{value} semanas", "buildingresume_taxi_travel": "<PERSON>je aqui ({price})", "click_to_use_taxi": "Clique para usar o táxi", "common_taxi": "Táxi", "filebrowser_search": "Procurar...", "filebrowser_filename": "Nome do arquivo", "filebrowser_show_hidden_files": "Mostrar arquivos ocultos", "filebrowser_delete_file_warning": "Tem certeza de que deseja excluir esses arquivos? Esta operação não pode ser desfeita.", "filebrowser_select_all": "Selecionar tudo", "filebrowser_deselect_all": "<PERSON><PERSON><PERSON> to<PERSON>", "filebrowser_new_folder": "Nova pasta", "filebrowser_delete": "Excluir", "filebrowser_rename": "Renomear", "filebrowser_all_files": "Todos os arquivos (.*)", "filebrowser_logo_shape_title": "Selecione a forma do logotipo", "filebrowser_images": "Imagens", "npc_expression_default": "Estou tendo sentimentos e emoções e estou expressando-os através de uma bolha", "npc_expression_bad_customer_service": "Que péssimo atendimento ao cliente. Eu não vou voltar.", "npc_expression_good_customer_service": "Que experiência agradável para o cliente. Com certeza voltarei aqui!", "npc_expression_no_cashiers": "Onde estão os caixas?", "npc_expression_no_shopping_baskets": "Como eles esperam que eu faça compras sem cestas? Estou fora.", "npc_expression_item_too_expensive": "Eu não vou pagar tudo isso por <b>{itemname}</b>", "npc_expression_no_item_in_stock": "Ah, que pena que não tem mais <b>{itemname}</b> no estoque", "npc_expression_no_cash_registers": "Não encontro nenhuma <b>caixa registradora</b> .", "npc_expression_no_paper_bags": "As sacolas de papel acabaram!", "notification_no_energy_to_run": "Você está muito cansando para correr", "notification_no_energy_to_walk": "Você está muito cansado para andar mais rápido", "npc_expression_no_item": "<PERSON>u não consigo encontrar nenhum <b>{itemname}</b>.", "common_furniture": "Mobília", "nearest_building": "{number} {street}", "marketingtypename_smallbillboard": "Outdoor pequeno", "marketingtypename_mediumbillboard": "Outdoor médio", "marketingtypename_largebillboard": "Outdoor grande", "marketingtypename_voogleads": "<PERSON>ún<PERSON>s no Voogle", "marketingtypename_latergramads": "<PERSON><PERSON><PERSON><PERSON> no <PERSON>", "marketingtypename_friendbookads": "Anúncios de livros de amigos", "marketingtypename_jitterads": "<PERSON><PERSON><PERSON><PERSON> no <PERSON>", "itemname_patterncarpet": "<PERSON><PERSON><PERSON>", "itemname_persiancarpet": "<PERSON><PERSON><PERSON>", "itemname_colorfulcarpet": "<PERSON><PERSON><PERSON> on<PERSON>o", "itemname_roundcarpet": "Tapete redondo", "jobdemand_parttime": "<PERSON><PERSON>", "jobdemand_fulltime": "Período integral", "jobdemand_nomornings": "Sem turnos matinais", "jobdemand_noevenings": "Sem turnos noturnos", "jobdemand_nonights": "Sem turnos noturnos", "jobdemand_nocleaning": "Sem turnos de limpeza", "jobdemand_fourdaysweek": "Semana de quatro dias", "jobdemand_fivedaysweek": "Semana de cinco dias", "businesstype_empty": "VAZIO", "myemployees_task_training": "Treinamento", "tutorial_20_objective_4": "Compre pelo menos uma prateleira de armazenamento", "tutorial_20_objective_5": "Coloque prateleira de armazenamento em sua loja de presentes", "employeehelper_notification_emloyee_resigned": "{employeeName} p<PERSON><PERSON> dem<PERSON> ({businessName})", "businesshelper_notification_delivery_no_storage_shelves": "A entrega para {businessName} não foi possível devido à falta de prateleiras de armazenamento disponíveis", "transactiontype_taxiride": "Passeio de táxi", "notification_need_empty_hands_to_interact": "Você precisa estar com as mãos vazias para interagir com este item", "menu_options_open_localizor": "Abrir localizador", "menu_options_others_localizor_tip": "Nossas traduções são editadas e aprovadas pela comunidade.", "workpanel_timemachine": "Máquina do tempo até o fim do turno", "menu_options_select_language": "Selecione o idioma", "notification_delivery_contract_arrived": "Sua remessa de {fromname} para {toname} chegou", "bizman_schedule_cant_assign_on_closed_days": "Os funcionários não podem ser atribuídos a dias agendados como fechados.", "business_description_supermarket": "Seu pit stop para lanches, bebidas, comida e tudo mais!", "playerhud_currentjob_notification_cant_quit_working": "Não pode deixar o emprego enquanto trabalha", "menu_copyright_disclaimer": "Ei, streamer!", "menu_copyright_disclaimer_description": "Temos os direitos de todas as músicas do jogo.\nVocê não precisa se preocupar com reivindicações de direitos autorais.", "bizman_contractsettings_product_label": "{itemName} ({boxSize}x) - {boxPrice}/caixa", "carcontroller_notification_warehouse_vehicle_unassigned": "{type} foi desatribuído de {warehouseName}", "playeritempurchaser_notification_require_interaction_with_cashier": "Você precisa interagir com o caixa para comprar este item", "jobdemand_fulltime_description": "Entre 30 e 50 horas por semana", "jobdemand_freeweekends_description": "Sem turnos aos finais de semana", "jobdemand_parttime_description": "Entre 10 e 30 horas por semana", "jobdemand_nomornings_description": "Nenhum turno atribuido entre 06:00 (6 AM) e 10:00 (10 AM)", "jobdemand_noevenings_description": "Nenhum turno atribuído entre 18h00 (6 PM) e 22h00 (10 PM)", "jobdemand_nonights_description": "Nenhum turno atribuído entre 22h00 (10 PM) e 04h00 (4 AM)", "jobdemand_nocleaning_description": "Nenhum turno de limpeza atribuído", "jobdemand_fourdaysweek_description": "Designado para trabalhar 4 dias por semana", "jobdemand_fivedaysweek_description": "Designado para trabalhar 5 dias por semana", "common_condition": "Condição", "common_enter_building": "Entrar no edifício", "tutorial_4_objective_4": "Peça demissão no supermercado local", "voogle_maps_close": "Fechar o Voogle Maps", "help_common_hunger_content": "**Fome** é uma das principais propriedades do seu personagem.\n\nPara diminuir a sua fome, você tem que comer ou beber. Os alimentos podem ser adquiridos em estabelecimentos espalhados pela cidade ou estocando na geladeira no seu apartamento ou empresa.\n\nQuando a fome atingir 0%, terá o seguinte impacto:\n* Aumento do consumo de energia em 200%", "help_common_energy_content": "**Energia** é uma das principais propriedades do seu personagem.\n\nPara melhorar o status de energia, você precisa dormir ou beber bebidas que contenham cafeína.\n\nQuando a energia chegar a 0%, terá os seguintes impactos:\n* Andar mais devagar\n* Risco de desmaiar devido ao cansaço\n* Diminuição mais rápida do status de fome\n\nVocê pode dormir nas seguintes opções:\n* [Cama <PERSON>](furniture-bed1)\n* [Cama King Size](furniture-kingsizebed)\n* Bancos da Cidade\n* Veículos\n* Barcos", "help_common_personal_goals_content": "**Objetivos pessoais** são os objetivos secundários do seu personagem. Eles não estão relacionados aos objetivos dados pelo tio Fred.\n\nAlcançar um objetivo pessoal dá um reforço temporário de **felicidade**.\n\nObjetivos pessoais estão diretamente ligados aos resultados do Steam.", "common_personal_goals": "Objet<PERSON>s pessoais", "help_general": "G<PERSON>", "common_business_type": "Tipo de Negócio", "common_building_type": "Tipo de construção", "common_size_sq": "{size} m2", "common_select": "Selecione", "common_market_demands": "Necessidades do mercado", "common_real_estate": "Imobiliária", "common_subtotal": "Subtotal", "common_price": "Preço", "common_buildings_for_sale": "Prédios a venda", "common_happiness": "Felicidade", "common_days_left": "{days} dias restantes", "common_hours_left": "{hours} horas restantes", "common_vehicle": "Veí<PERSON>lo", "real_estate_type_and_size": "Tipo e tamanho", "real_estate_estimated_value": "Valor estimado", "real_estate_building_sold_notification": "Você vendeu {address} por {price}", "bizman_businesses_and_real_estate": "Negócios e imóveis", "bizman_warehouses": "Armazéns", "bizman_headquarters": "Sede", "bizman_private_residences": "Residências Privadas", "bizman_avg_daily_income": "Renda média diá<PERSON>", "bizman_alerts": "<PERSON><PERSON><PERSON>", "bizman_status": "Status", "bizman_number_of_alerts": "{amount} alerta(s)", "bizman_hover_destination_button": "Clique para definir como destino", "bizman_hover_manage_button": "Clique para gerenciar negócios", "bizman_empty_building": "Vazio ({buildingType})", "bizman_vehicle_slots": "Vagas de veículos", "bizman_manage_drivers": "Gerenciar motoristas", "bizman_show_full_inventory": "Mostrar inventário completo", "bizman_linked_businesses": "Empresas vinculadas", "bizman_manage_delivery_plan": "Gerenciar plano de entrega", "bizman_warehouse_driverstation_not_enough_skill": "O motorista não tem habilidade suficiente para dirigir este veículo", "bizman_choose_business_type": "Selecione o tipo de negócio que deseja iniciar:", "bizman_primary_products": "<PERSON>dutos primá<PERSON>", "bizman_competitors_in_neighborhood": "<b>{amount} concorrentes</b> no bairro", "bizman_purchasingagents_warehouse_stock": "Estoque do armazém", "bizman_building_total_size": "Tam<PERSON>ho total", "bizman_send_purchase_offer": "Enviar oferta de compra de prédio", "bizman_building_description_retail": "Um edifício que é alugado para negócios como lojas e restaurantes", "bizman_building_description_office": "Um edifício que é alugado para negócios como escritórios de advocacia e agências de desenvolvimento digital", "bizman_building_description_residential": "Um edifício que é alugado para residência de pessoas ou famílias", "bizman_building_description_warehouse": "Um prédio que é alugado para empresas que desejam expandir sua logística", "bizman_presentation_notification_building_offer_accepted": "O proprietário do edifício aceitou sua oferta. Agora você é o proprietário de {address}. Parabéns!", "bizman_presentation_notification_building_offer_rejected": "O proprietário do edifício rejeitou sua oferta de {price}", "bizman_menu_realestate": "Configurações de Imóveis", "bizman_real_estate_rent_management": "Gerenciamento de aluguel", "bizman_real_estate_self_occupied_unit": "Unidade autônoma", "bizman_real_estate_set_for_sale": "Conjunto a venda", "bizman_real_estate_current_rent": "Aluguel atual: <b>{rent}/{unit}</b>", "bizman_real_estate_average_market_price": "Preço médio de mercado: <b>{price}/{unit}</b>", "bizman_real_estate_time_left_for_new_rent": "Tempo restante até a alteração de valor para novo aluguel: <b>{days} dia(s)</b>", "bizman_real_estate_rent_applied": "Preço do aluguel já aplicado", "bizman_real_estate_apply_new_rent": "Aplicar novo aluguel", "bizman_real_estate_occupancy": "Ocupação", "bizman_real_estate_total_daily_rent": "Total do aluguel diário ({occupancy}% de {totalSqm})", "bizman_real_estate_self_occupied_unit_description": "Como proprietário deste edifício, você pode alugar <b>1 unidade de negócios <lowercase>{buildingType}</lowercase> gratuitamente</b>.", "bizman_real_estate_takeover_unit": "Unidade de aquisição", "bizman_real_estate_unit_generating_rent": "A unidade está atualmente ocupada e gerando aluguel.", "bizman_real_estate_unit_owned_by_player": "A unidade atualmente é sua e não gerará nenhum aluguel.", "bizman_real_estate_price_at_purchase": "Preço que você pagou (day {day})", "bizman_real_estate_estimated_market_price": "Preço estimado de mercado", "bizman_real_estate_enter_sales_price": "Insira o preço de venda", "bizman_real_estate_your_potential_gain": "<PERSON><PERSON> ganho potencial", "bizman_real_estate_mark_for_sale": "Marcar para venda", "bizman_real_estate_no_sales_price_notification": "Insira um preço de venda válido", "bizman_real_estate_for_sale_info": "O edifício está à venda por {price}", "bizman_real_estate_cancel_sale": "CANCELAR VENDA", "bizman_real_estate_taxes_info": "Imposto de propriedade {daysInterval}-dias:", "carcontroller_notification_warehouse_employee_unassigned_slot": "{employeeName} não foi atribuído ao slot {slotNumber} devido ao nível de habilidade insuficiente", "carcontroller_notification_fuel_empty": "Seu veículo ficou sem combustível. Dica: tanques de reserva estão disponíveis em postos de gasolina", "gasstationoverlay_fillup": "Encher ({price})", "gasstationoverlay_jerrycan": "Galão ({price})", "gasstationoverlay_repair": "Reparar ({price})", "businesstype_florist": "Florista", "help_businesstype_florist_content": "**Floriculturas** são negócios de varejo.\n\nOs clientes servem a si mesmos.\n\nO negócio precisa dos seguintes móveis para funcionar:\n\n* [<PERSON>lha de Cestas de Compras](furniture-stackofshoppingbaskets)\n* [Ponto de Venda](furniture-itemgrouppointofsale)\n* Pelo menos um produto para vender (veja abaixo)\n\nNegócios deste tipo vendem principalmente:\n\n* [Flor (Barata)](products-cheapflower)\n* [Flor (Cara)](products-expensiveflower)\n\nE também podem vender:\n\n* [Presente (Barato)](products-cheapgift)\n* [Presente (Caro)](products-expensivegift)\n* [Lata de Refrigerante](products-sodacan)\n* [Guarda-chuva](products-umbrella)\n\nFuncionários com as seguintes habilidades podem ser designados:\n\n* [Atendimento ao Cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_itemname_cheapflower_content": "**Flor (Barato)** é um tipo de produto vendido em [Floristas](businesstypes-florist).\n\n<PERSON>é<PERSON> disso, pode ser vendido em:\n\n* [Lojas de presentes](businesstypes-giftshop)\n* [Livraria](businesstypes-bookstore)\n\nO produto pode ser colocado nos seguintes móveis:\n* [Prateleira arredondada](furniture-roundedshelf)\n* [Expositor (Produto em camadas)](furniture-productdisplaystandtiered)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](endereço:37 1s)\n* [Total Produce Trading](endereço:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [JetCargo Imports](endereço: 1 pier)\n* [United Ocean Import](endereço: 3 pier)", "help_itemname_expensiveflower_content": "**Flor (Cara)** é um tipo de produto vendido em [Floristas](businesstypes-florist).\n\n<PERSON>é<PERSON> disso, pode ser vendido em [Lojas de presentes](businesstypes-giftshop).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Prateleira arredondada](furniture-roundedshelf)\n* [Expositor (Produto em camadas)](furniture-productdisplaystandtiered)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](endereço:37 1s)\n* [Total Produce Trading](endereço:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [United Ocean Import](endereço: 3 pier)", "itemname_jerrycan": "<PERSON> (10L)", "itemname_cheapflower": "Flor barata", "itemname_expensiveflower": "Flor cara", "itemname_deskplant01": "Planta de Mesa 01", "itemname_deskplant02": "Planta de Mesa 02", "itemname_deskplant03": "Planta de mesa quadrada", "itemname_largemeetingtable": "Mesa de reunião grande", "itemname_largewallclock": "Relógio de parede grande", "itemname_lcdrestaurantscreen": "Tela LCD para restaurante", "itemname_wallradiator": "<PERSON><PERSON><PERSON>", "itemname_officechair2": "Cadeira de escritório <PERSON>", "itemname_valenciachair": "<PERSON>ira Val<PERSON>", "itemname_valenciaottoman": "Valencia Otomana", "contacts_taxes_message_heading": "<b>Aviso de pagamento de imposto pendente</b><br><br> <PERSON><PERSON><PERSON> se<PERSON><PERSON> ou senh<PERSON>:<br><br> As apurações de impostos para o período Dia {startingDay}-{endingDay} já estão disponíveis.", "contacts_taxes_message_ending": "Pague o imposto devido no dia {lastPayingDay} no escritório local da Receita Federal:<br> {address}<br><br> <b>At<PERSON>ciosamente,<br> Receita Federal</b>", "contacts_taxes_registered_businesses": "Empresas cadastradas:", "contacts_taxes_tax_deductible_expenses": "Despesas dedutíveis de impostos:", "contacts_taxes_real_estate": "Impostos imobiliários:", "contacts_corporate_tax_percentage": "Percentual de imposto corporativo", "contacts_taxes_to_be_paid": "Impostos a pagar", "internal_revenue_service": "Receita Federal", "tutorial_todotask_paytaxes": "Pagar impostos em {remainingDays} dias", "contacts_taxes_message_warning": "<b>Aviso de pagamento de imposto pendente</b><br><br> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ou senh<PERSON>:<br><br> <PERSON>da não recebemos o pagamento dos seus impostos pendentes.<br><br> Caso o pagamento não seja concluído até o <b>dia {day}</b>, seremos obrigados a apreender objetos de valor da sua empresa, sem aviso prévio.<br><br> Por favor, pague o valor devido no escritório local da Receita Federal.<br> {address}<br><br><b>Atenciosamente,<br> Receita Federal</b>", "taxes_pay_item": "Impostos", "market_insider_show_only_for_sale": "Mostrar apenas à venda", "market_insider_citizen_data": "Dados do cidadão", "market_insider_real_estate_data": "Dados imobiliários", "market_insider_total_buildings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "market_insider_average_building_price": "Preço médio de <lowercase>{buildingType}</lowercase> \n preço/{unit}", "socialclass_working": "Classe operária", "socialclass_middle": "Classe média", "socialclass_upper": "Classe alta", "transactiontype_hospitalbill": "Conta Hospitalar", "transactiontype_taxpayment": "Pagamento de taxa", "hospital_respawn_notification": "Você não tinha mais energia e desmaiou. As autoridades locais transportaram você para o hospital para se recuperar.", "businesstype_government": "Governo", "transactiontype_buildingbought": "Aquisição de {address}", "transactiontype_rentrevenue": "<PERSON><PERSON><PERSON> de al<PERSON> de {address}", "transactiontype_autotowservice": "Serviço de reboque automático de NY", "econoview_row_rent_revenue": "<PERSON><PERSON><PERSON> de aluguel", "irs_tax_payment_station": "Estação de pagamento de impostos", "happinessmodifiertype_startedaheadquarters": "<PERSON><PERSON> uma sede", "happinessmodifiertype_sleptinthecar": "<PERSON><PERSON>ir no carro", "happinessmodifiertype_walkedinthepark": "And<PERSON> no parque", "happinessmodifiertype_wenttohospital": "Foi para o hospital", "jobdemand_peacefulworkenvironment": "Ambiente de trabalho tranquilo", "jobdemand_peacefulworkenvironment_description": "Proprietário tem uma felicidade de 50% ou mais", "towdestination_gasstation": "Transporte para o posto de gasolina mais próximo", "towdestination_autorepairshop": "Transporte para a oficina mecânica mais próxima", "dialog_auto_tow_start": "Bem-vindo ao NY AutoTow, como podemos ajudá-lo?", "dialog_auto_tow_service_no_vehicle": "Obrigado por ligar para o NY AutoTow. Por favor, sente-se no veículo para o qual deseja os serviços e ligue novamente.", "dialog_auto_tow_service_settings_set": "<PERSON>ão tem problema, será {amount}. Estamos ao virar da esquina! Vejo você em breve.", "dialog_auto_tow_service_settings_set_player": "Eu gostaria da opção '{autoTowServiceOption}'", "autotowservicedialog_notification_vehicle_towed": "Seu veículo foi rebocado para {towAddress}.", "auto_tow_service_ny": "Serviço de reboque automático de NY", "contact_description_special": "Especial", "sadperiodtype_stayathome": "Ficar em casa", "sadperiodtype_noeat": "Sem comer", "sad_period_cant_leave_vehicle_notification": "Você está muito triste para deixar o veículo. Você vai precisar dormir por um tempo", "sad_period_cant_leave_home_notification": "Você está muito triste para sair de casa. Você vai precisar dormir por um tempo", "sad_period_cant_enter_building_notification": "Você está muito triste para entrar neste prédio. Você vai precisar dormir por um tempo", "sad_period_cant_eat": "Você está muito triste para comer. Você precisará esperar um pouco até poder comer novamente", "sadperiodtype_stayathome_started_notification": "Você está entrando em um período triste sem sair do veículo/casa", "sadperiodtype_noeat_started_notification": "Você está entrando em um período triste sem comer", "sadperiod_finished_notification": "Você não está mais em um período triste", "sad_period_persona_app_notification": "Em um período triste: {sadPeriod} <color=#515A60>({hours} horas restantes)</color>", "help_itemname_productpanel_content": "O **Painel de Produtos** pode ser usado para vender:\n\n* [<PERSON><PERSON>](products-cheapgift)\n* [Guarda-chuva](products-umbrella)\n\n**Capacidade de Produtos:** 100\n**Capacidade de Clientes:** 10\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [AJ <PERSON>rson & Son](address:13 5a)", "bizman_real_estate_rent_not_applied": "Novo preço de aluguel não aplicado", "fridge_notification_cant_fit_some_items": "{itemname} est<PERSON> cheio, alguns itens não puderam ser armazenados", "help_common_happiness_content": "**Felicidade** é uma das principais propriedades do seu personagem. A quantidade de felicidade depende dos bônus atuais que o seu personagem possui.\n\nAumentos acontecerão por:\n* Ter receita positiva\n* Alcançar objetivos pessoais\n* Alcançar objetivos importantes (como abrir uma sede, contratar seu primeiro funcionário...)\n* Caminhar no parque\n* Ir numa pista de skate\n* [Assistir TV](common_watchtv)\n* [Jogar no Computador](common_playcomputer)\n* [Ler Livros](common_readbooks)\n* [Exercitar](common_exercise)\n* [Apostar (somente às Sextas-feiras)](address: 4 pier)\n* Relaxar no seu barco\n* Usando [Cabine de DJ](furniture-djbooth) em sua casa\n\nReduções acontecerão por:\n* Dormir no carro\n* Não ter um apartamento\n* Ser levado ao hospital\n\nQuando a felicidade chegar a 0% terão os seguintes impactos:\n* Risco de entrar em depressão\n* Redução na felicidade dos funcionários\n* Rápida diminuição na fome\n* Rápida diminuição na energia\n\nExistem dois tipos depressão:\n* Período sem comer: você não conseguirá comer nada\n* Período sem sair de casa/veículo: você não poderá sair da sua casa/veículo atual ou entrar em qualquer edifício que não seja sua casa", "bizman_list_real_estate_type": "Imóveis ({buildingType})", "happinessmodifiertype_nohome": "Sem casa", "happinessmodifiertype_firstdayonny": "Primeiro dia em Nova York", "happinessmodifiertype_firstapartment": "Primeiro apartamento", "happinessmodifiertype_firstjob": "Primeiro emprego", "happinessmodifiertype_firstemployee": "Primeiro funcionário", "happinessmodifiertype_completedpersonalgoal": "Objetivo pessoal concluído", "happinessmodifiertype_positiverevenue": "<PERSON><PERSON><PERSON> positiva", "contacts_taxes_repossession_message_heading": "<b>Aviso de cobrança</b><br><br> <PERSON><PERSON><PERSON> ou se<PERSON><PERSON>:<br><br> <PERSON><PERSON> à pendência do pagamento de impostos, procedemos com a apreensão de alguns bens da sua empresa.", "contacts_taxes_repossession_message_ending": "<b><PERSON><PERSON><PERSON>,<br> Receita Federal</b>", "contacts_taxes_repossessed_valuables": "Objetos de valor apreendidos:", "vehicletypename_bima320": "BIMA 320", "sleepingbench_notification_cant_use_with_item_in_hand": "Você não pode usar o banco de dormir com um item na mão", "help_itemname_pizzaoven_content": "**Forno de Pizza** pode ser usado para vender:\n\n* [Pizza](products-pizza)\n\n**Capacidade de Produtos:** 60\n**Capacidade de Clientes:** 20\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "notification_destination_set": "{address} foi definido como destino", "transactiontype_buildingsold": "{address} vendido", "myemployees_no_business_assigned_notification": "O funcionário não está atribuído a nenhum negócio", "itempanelui_parkingzone_legal_payed": "LEGAL ({price})", "transactiontype_publicparking": "Estacionamento público para {vehicleName}", "bizman_browse_store_inventory": "Navegue pelo inventário da loja", "bizman_store_inventory_title": "Inventário de {businessName}", "furniture_delivery_dialog_order": "Pedido", "furniture_delivery_dialog_add_item": "<PERSON><PERSON><PERSON><PERSON>", "furniture_delivery_item": "{itemName} - {itemPrice}", "dialog_furniture_store_npc_name": "<PERSON><PERSON><PERSON>", "dialog_furniture_store_start": "O<PERSON><PERSON>, como podemos ajudá-lo?", "dialog_furniture_store_no_addresses": "Você deve ter pelo menos um endereço disponível para iniciar uma entrega.", "dialog_furniture_delivery_header": "Entrega de móveis", "dialog_furniture_store_player_start": "Eu gostaria de encomendar os seguintes itens", "dialog_select_address": "Selecionar endereço", "dialog_select_delivery_time": "Selecione o horário de entrega", "dialog_delivery_time": "Tempo de entrega", "dialog_furniture_delivery_time_slot": "{day} (dia {number}) {hour}:00", "dialog_furniture_delivery_item_already_in_list_notification": "Este item já está na lista", "common_notification_select_address": "Selecione um endereço", "common_notification_select_delivery_time": "Selecione o horário de entrega", "dialog_furniture_delivery_select_items_notification": "Você precisa selecionar pelo menos um item para ser entregue", "dialog_furniture_store_on_contract_settings_set_player": "Eu gostaria que esses {amount} itens fossem entregues em {address} no dia {day} às {hour}:<br> {text}", "dialog_furniture_store_on_contract_settings_set_manager": "<PERSON><PERSON> bem. Faremos a entrega seguindo suas especificações. Obrigada!", "dialog_furniture_store_already_has_delivery": "O<PERSON><PERSON>! Já temos um contrato de entrega pendente. Deseja cancelá-lo?", "dialog_furniture_store_cancel_delivery_button": "Cancelar entrega", "dialog_furniture_store_cancel_delivery": "<PERSON><PERSON>, cancele por favor.", "dialog_furniture_store_delivery_cancelled": "<PERSON><PERSON><PERSON>, esse contrato de entrega foi cancelado.", "menu_options_ui_zooming": "Zoom da interface do usuário", "smartphone_title": "BizPhone 1.0", "jobdemand_seatedatofficechair2": "Cadeira de escritório <PERSON>", "jobdemand_seatedatmultipurposechair": "Cadeira multiuso", "jobdemand_seatedatofficedesk2": "Mesa Executiva", "jobdemand_standardfridge": "Geladeira <PERSON>", "jobdemand_largemeetingtable": "Mesa de reunião grande", "jobdemand_sofa": "<PERSON>ual<PERSON> so<PERSON>", "jobdemand_coffeemachine": "Cafeteira", "jobdemand_seatedatofficechair2_description": "O funcionário exige estar sentado em uma cadeira de escritório Stump Mesh", "jobdemand_seatedatmultipurposechair_description": "O funcionário exige estar sentado em uma cadeira multifuncional", "jobdemand_seatedatofficedesk2_description": "Funcionário exige sentar-se numa Mesa Executiva", "jobdemand_standardfridge_description": "Exigências de funcionários para geladeira padrão", "jobdemand_largemeetingtable_description": "Demandas de funcionários para grande mesa de reunião", "jobdemand_sofa_description": "Exigências de funcionários para qualquer tipo de sofá", "jobdemand_coffeemachine_description": "Funcionário exige uma máquina de café barata", "employeehelper_notification_employee_amount_called_in_sick": "{amount} funcionários chamados doentes", "econoview_loans_daily_interest_rate": "Taxa de juros diária", "econoview_loans_daily_payment": "Pagamento Diá<PERSON>", "econoview_loans_time_left": "Tempo restante", "main_menu_browse_savegame_folder": "Procurar pasta savegame...", "myemployees_task_absent": "<PERSON><PERSON>", "loading_hint_handtrucks": "Use os carrinhos de mão gratuitos (localizados em todas as lojas) para mover facilmente várias caixas.", "loading_hint_prefix": "<b>Dica:</b>", "loading_hint_cityworkforce": "Há mais de uma agência de recrutamento e cada uma é especializada em diferentes tipos de funcionários.", "dialog_slot_machine_start": "Clique no botão para girar as rodas!", "dialog_slot_machine_spin_the_wheel_button": "Gire a roda ({price})", "transactiontype_casino": "Cassino ({element})", "dialog_casino_no_prize_dialog": "Sem sorte desta vez!", "dialog_slot_machine_jackpot_prize": "Parabéns! Você ganhou o grande prêmio de {prize}", "dialog_casino_regular_prize_dialog": "Parabéns! Voc<PERSON> ganhou {prize}", "common_add": "<PERSON><PERSON><PERSON><PERSON>", "dialog_roulette_start": "Be<PERSON>-vindo à roleta!", "dialog_bet_button": "Apostar", "dialog_bet_set_dealer": "Boa sorte a todos!", "dialog_roulette_no_prize": "Sem vitória desta vez. Mais sorte da próxima vez!", "dialog_bet_amount_input": "<PERSON>or da aposta", "dialog_bet_question_dealer": "Quanto você gostaria de apostar?", "dialog_roulette_bet_color": "Cor", "dialog_roulette_bet_number": "Número", "dialog_roulette_bet_color_select": "Selecione a cor", "dialog_roulette_bet_number_select": "Selecionar número", "common_any": "<PERSON>ual<PERSON>", "common_notification_select_color": "Por favor selecione uma cor", "common_notification_select_number": "Por favor, selecione um número", "help_common_clothing_content": "Os tipos diferentes de roupas existentes são:\n* [Roupa clássica e barata masculina](itemname_classiccheapmaleclothing)\n* [Roupa clássica e barata feminina](itemname_classiccheapfemaleclothing)\n* [Roupa moderna e barata masculina](itemname_moderncheapmaleclothing)\n* [Roupa moderna e barata feminina](itemname_moderncheapfemaleclothing)\n* [Roupa clássica e cara masculina](itemname_classicexpensivemaleclothing)\n* [Roupa clássica e cara feminina](itemname_classicexpensivefemaleclothing)\n* [Roupa moderna e cara masculina](itemname_modernexpensivemaleclothing)\n* [Roupa moderna e cara feminina](itemname_modernexpensivefemaleclothing)", "help_itemname_classiccheapmaleclothing_content": "**Roupas Masculinas Clássicas Baratas** é um tipo de produto vendido principalmente em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON>](furniture-clothingrackangled)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Metro Wholesale](address:2 5a)\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_classiccheapfemaleclothing_content": "**Roupas Femininas Clássicas Baratas** é um tipo de produto vendido principalmente em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON>](furniture-clothingrackangled)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_moderncheapmaleclothing_content": "**Roupas Masculinas Modernas Baratas** é um tipo de produto vendido principalmente em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON>](furniture-clothingrackangled)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_moderncheapfemaleclothing_content": "**Roupas Femininas Modernas Baratas** é um tipo de produto vendido principalmente em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON>](furniture-clothingrackangled)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_classicexpensivemaleclothing_content": "**Roupa masculina clássica e cara** é um tipo de produto vendido na [Loja de roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON> de roupas](furniture-clothingrack)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_classicexpensivefemaleclothing_content": "**Roupa feminina clássica e cara** é um tipo de produto vendido na [Loja de roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON> de roupas](furniture-clothingrack)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_modernexpensivemaleclothing_content": "**Roupas Masculinas Modernas Caras** é um tipo de produto vendido principalmente em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON>](furniture-clothingrackangled)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "help_itemname_modernexpensivefemaleclothing_content": "**Roupas Femininas Modernas Caras** é um tipo de produto vendido principalmente em [Lojas de Roupas](businesstypes-clothingstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON>](furniture-clothingrack)\n* [<PERSON><PERSON>](furniture-clothingrackangled)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [BlueStone Imports](address: 4 pier)", "common_clothing": "<PERSON><PERSON><PERSON>", "help_skillname_customerservice_content": "Funcionários com a habilidade **Atendimento ao cliente** são usados em empresas varejistas.\n\nFuncionários de Atendimento ao cliente trabalham em qualquer ponto de vendas de local de varejo para atender aos clientes.\nEles também reabastecerão o estoque de mostruário a partir das prateleiras do depósito nos fundos.\n\nO nível de habilidade (%) define a qualidade do tratamento recebido pelos clientes.\n\nÉ possível empregá-los em:\n* [Pontos de vendas](furniture-itemgrouppointofsale)\n* [Guarda-volumes esquerdo](furniture-coatcheckleft)\n* [Guarda-volumes direito](furniture-coatcheckright)\n\nÉ possível contratá-los na:\n* [Anderson Recruitment Corp.](address: 16 5a)", "help_skillname_cleaning_content": "Funcionários com a habilidade **Limpeza** são usados para negócios de varejo.\n\nFuncionários de limpeza limpam sua loja ou escritório durante o horário para o qual estão escalados.\n\nO nível de habilidade (%) define o quanto eles podem limpar a cada hora.\n\nEles podem ser designados para:\n* [Estações de limpeza](furniture-cleaningstation)\n\nEles podem ser contratados de:\n* [Anderson Recruitment Corp.](endereço: 16 5a)", "help_skillname_lawyer_content": "Funcionários com a habilidade de **Advogado** são utilizados por escritórios de advocacia.\n\nAdvogados trabalham com clientes digitais para ganhar suas [Taxas por Hora de Advogado](fees-hourlylawyerfee).\n\nO nível de habilidade (%) define quanto os clientes estão dispostos a pagar pelos serviços.\n\nEles podem ser designados para:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n\nEles podem ser contratados através de:\n* [City Workforce Inc.](address: 41 4a)", "help_skillname_purchasingagent_content": "Funcionários com a habilidade **Representante de Compras** são usados em sedes.\n\nCada Representante de Compras gerencia 1 [contrato de importação](importers-contract) e controla [estoque de importação](importers-overview) do seu [armazéns](businesstypes-warehouse).\n\nO nível de habilidade (%) define quanto desconto eles conseguirão obter nos contratos de importação.\n\nEles podem ser atribuídos em:\n* [Computador de Trabalho](furniture-computerworkstation)\n\nEles podem ser contratados em:\n* [City Workforce Inc.](address: 41 4a)", "help_skillname_logisticsmanager_content": "Funcionários com a habilidade **Gestor de Logística** são usados em sedes.\n\nCada Gestor de Logística gerencia 1 [armazém](businesstypes-warehouse) e controla [estoque de entrega](logistics-overview) para suas lojas.\n\nO nível de habilidade (%) define em quantos locais eles podem entregar.\n\nEles podem ser atribuídos em:\n* [Computador de Trabalho](furniture-computerworkstation)\n\nEles podem ser contratados em:\n* [City Workforce Inc.](address: 41 4a)", "help_skillname_deliverydriver_content": "Funcionários com a habilidade **Motorista de Entrega** são usados para Armazéns e Fábricas.\n\nMotoristas de Entrega são designados para um veículo no armazém ou fábrica e entregarão mercadorias em suas lojas às 02:00 (2:00 AM) com base na configuração de entrega do Gerente de Logística.\nObservação: atualmente você não os vê fazendo entregas.\n\nO nível de habilidade (%) define quais tipos de veículos eles podem dirigir.\n\nEles podem ser designados para:\n* [Armazém](businesstypes-warehouse)\n* [Fábrica](businesstypes-factory)\n\nEles podem ser contratados de:\n* [Anderson Recruitment Corp.](endereço: 16 5a)", "help_skillname_programmer_content": "Funcionários com a habilidade de **Programador** são utilizados por agências de desenvolvimento web.\n\nProgramadores trabalham com clientes digitais para ganhar suas [Taxas por Hora de Programador](fees-hourlyprogrammerfee).\n\nO nível de habilidade (%) define quanto os clientes estão dispostos a pagar pelos serviços.\n\nEles podem ser designados para:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n\nEles podem ser contratados através de:\n* [City Workforce Inc.](address: 41 4a)", "bank_dialog_total_daily_payment": "Pagamento diário total", "bank_dialog_interest_daily_payment": "<PERSON><PERSON> ({rate}%)", "menu_options_reset_windows": "Redefinir as posições das janelas", "menu_options_reset_windows_tip": "Coloca as janelas móveis de volta nas posições padrão", "menu_options_reset_windows_button": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "menu_options_reset_windows_notification": "As posições das janelas foram redefinidas com sucesso", "loading_hint_walkinpark": "Sua felicidade está baixa? Experimente dar uma volta no parque.", "loading_hint_purchasingagent": "Um representante de compras de nível superior garantirá melhores preços de importação.", "loading_hint_coffee": "Beber café lhe dará um pouco mais de energia!", "loading_hint_neighborhoodprices": "Bairros mais ricos permitem que você venda produtos a preços mais altos.", "loading_hint_marketing": "O índice de tráfego da sua empresa está baixo? Use o marketing para aumentar sua pontuação de promoção.", "loading_hint_towservice": "Seu carro está sem gasolina ou completamente danificado? Ligue para o NY Tow Service através do aplicativo Contatos.", "loading_hint_speedrun": "Você está com pressa enquanto está dentro de casa? Segure SHIFT para correr.", "loading_hint_subway": "Use as estações de metrô para se locomover pela cidade de modo rápido e fácil.", "loading_hint_customerdialogs": "Não sabe como melhorar seu negócio? Tente observar o que seus clientes dizem quando visitam sua loja.", "loading_hint_employeedemands": "Atenda as exigências dos seus funcionários. Funcionários insatisfeitos têm um desempenho abaixo do seu nível de habilidade.", "loading_hint_storageshelf": "Prateleiras são uma ótima maneira de organizar melhor o estoque da sua loja.", "loading_hint_cleaningstation": "Para que seus funcionários limpem uma loja, eles precisam de uma estação de limpeza.", "loading_hint_sleepingincar": "Do<PERSON>ir no carro é conveniente, mas afeta negativamente a sua felicidade.", "loading_hint_benches": "Bancos da cidade podem ser usados para fazer o tempo passar mais rápido e ganhar um pouco de energia.", "loading_hint_realestate": "Cansado de pagar aluguel? Economize e compre o prédio!", "loading_hint_contacts": "Depois de visitar lugares especiais, como lojas de eletrodomésticos, agências de recrutamento, etc., você poderá ligar para eles diretamente pelo aplicativo Contatos.", "loading_hint_handtruck_in_trunk": "Você pode guardar um carrinho de mão no porta-malas do seu veículo.", "loading_hint_opening_hours": "Use \"Clientes ao longo do tempo\" da BizMan para saber em que horas seu negócio será mais lucrativo.\n", "loading_hint_help": "Você pode pressionar F1 a qualquer momento para acessar o menu Ajuda.", "dialog_roulette_ball_land_position": "A bola sorteada foi a de número {number} e cor {color}", "casino_message_welcome": "Bem-vindo às águas internacionais. Boa sorte!", "casino_message_trip_over": "A noite acabou e o barco está voltando para o porto", "vehicletypename_vordv150": "Ford V150", "vehicletypename_vordpony": "Vord Pony", "employeehelper_notification_employee_retired": "{name} se aposentou de ({businessName})", "ticket_house": "Bilheteria do Cassino", "casinoboatmanager_leaveboat": "Tem certeza de que deseja avançar até o final da viagem?", "casinoboat_ticket_pay_item": "Passagem do barco do cassino", "dialog_blackjack_start": "Bem vindo ao <PERSON> e um!", "dialog_blackjack_dealer": "Crupiê", "dialog_blackjack_score": "Pontuação: {score}", "dialog_blackjack_player": "Jogador", "dialog_blackjack_hit": "Pedir carta", "dialog_blackjack_stand": "<PERSON><PERSON>", "dialog_blackjack_insurance": "Garantia", "dialog_blackjack_surrender": "Desistir", "dialog_blackjack_both_blackjack": "Tanto o crupiê quanto o jogador somaram vinte e um. Ninguém ganhou!", "dialog_blackjack_player_blackjack": "Jo<PERSON><PERSON> de vinte e um! Jo<PERSON><PERSON> ganha {prize}", "dialog_blackjack_surrender_result": "Você desistiu. Metade da sua aposta foi devolvida.", "dialog_blackjack_dealer_blackjack_insurance": "Crupiê do Vinte e um! O jogador recebe o dinheiro de volta devido à garantia.", "dialog_blackjack_dealer_blackjack_no_insurance": "Crupiê do Vinte e um! Mais sorte da próxima vez!", "dialog_blackjack_tie": "Mesmos resultados para o jogador e o crupiê! Devolvendo as apostas.", "dialog_blackjack_player_wins": "<PERSON><PERSON><PERSON> ganha {prize}!", "dialog_recruitment_no_skills_available_notification": "Esta agência de recrutamento não tem nenhuma habilidade disponível para o negócio selecionado. Tente outra agência de recrutamento", "bizman_store_furniture_delivery_title": "Inventário de {businessName} ({currentAmount}/{maxAmount} itens selecionados)", "happinessmodifiertype_gambled": "A<PERSON>ou no cassino", "bizman_store_furniture_delivery_add_to_cart": "Adicionar ao car<PERSON>ho", "businessemployeecontroller_notification_cant_use_with_item_in_hand": "Você não pode sentar enquanto segura um item", "carcontroller_no_exitpositon_found": "Nenhuma posição disponível encontrada para o personagem. Por favor, mova o veículo para outra posição", "transactiontype_casinoboatticket": "Passagem do barco do cassino", "itemoverlay_items_attached_headline": "Itens anexados", "common_distance_feet": "{distance} pés", "menu_options_others_time_format": "Usar formato de hora 12h", "menu_options_others_unit_system": "Usar sistema imperial", "main_menu_story_mode": "<PERSON><PERSON>", "main_menu_custom_game": "<PERSON><PERSON>", "main_menu_new_game_start": "Começar o jogo", "main_menu_custom_game_configure": "Configu<PERSON> jogo", "main_menu_story_mode_new_players_recommendation": "Recomendado para novos jogadores", "main_menu_story_mode_description": "Comece sua jornada como empreendedor com a ajuda e orientação de seu tio Fred.", "main_menu_story_mode_recommended_for_first_playthrough": "Recomendado para sua primeira jogada", "main_menu_story_mode_guidance_and_objectives": "Orientação e objetivos", "main_menu_story_mode_unlocks_achievements": "Desbloqueia conquistas", "main_menu_custom_game_description": "<PERSON><PERSON><PERSON> as variáveis você mesmo para uma experiência de jogo perfeita.", "main_menu_custom_game_not_recommended_for_new_players": "Não recomendado para novos jogadores", "main_menu_custom_game_no_guidance_or_objectives": "Nenhuma orientação ou objetivos a seguir", "main_menu_custom_game_sandbox_experience": "Experiência de sandbox completa sem limitações de progresso", "main_menu_custom_game_achievements_disabled": "Conquistas desativadas", "main_menu_custom_game_starting_age": "<PERSON><PERSON> inicial", "main_menu_custom_game_disable_aging": "Desativar envelhecimento", "main_menu_custom_game_all_courses_unlocked": "Todos os cursos desbloqueados", "main_menu_custom_game_days_per_year": "Dias por ano", "main_menu_custom_game_starting_money": "<PERSON><PERSON><PERSON> inicial", "main_menu_custom_game_tax_percentage": "Percentual de imposto", "main_menu_custom_game_market_price_multiplier": "Multiplicador de preços ao público", "main_menu_custom_game_employee_hourly_salary_multiplier": "Multiplicador de salário por hora do funcionário", "main_menu_custom_game_bank_interest_multiplier": "Multiplicador de juros bancários", "itemname_loudspeaker1": "Alto-falante JayBeeel 1", "itemname_loudspeaker2": "Alto-falante JayBeeel 2", "dialog_furniture_delivery_minimum_price_not_reached": "O custo de compra deve ser de pelo menos {minimumCost}", "city_map_cant_use_while_in_placement_mode_notification": "Não é possível abrir o Voogle Maps ao mover um item", "itempanelui_notification_cant_sell_vehicle_at_current_position": "Não é possível vender um veículo nesta posição", "bizman_schedule_auto_fill": "Preencher automaticamente todos", "tickethouse_handtruck": "Suas mãos devem estar vazias para entrar no barco do cassino", "difficulty_easy": "F<PERSON><PERSON>l", "difficulty_normal": "Normal", "difficulty_hard": "Dif<PERSON><PERSON>l", "bizman_schedule_auto_fill_disabled_tooltip": "Você precisa de um Gerente de RH para usar este recurso", "bizman_hrmanagers_hint_seated_only": "Somente gerentes de RH sentados podem ser acessados", "bizman_menu_hr_manager_settings": "Configurações do gerente de RH", "common_salary": "<PERSON><PERSON><PERSON>", "bizman_hrmanager_employee_primary_skill": "{skill} ({value}%)", "bizman_hrmanager_salary": "{wage}/hr", "bizman_hrmanager_assign_employees": "Atribuir funcionários", "bizman_hrmanager_assigned_employees_amount": "{amount} de {max} funcionários atribuídos", "bizman_hrmanager_settings_replace_absent_employees": "Substitua automaticamente funcionários ausentes por funcionários temporários", "bizman_hrmanager_settings_replace_absent_employees_hint": "Nota: funcionários temporários custam 10% a mais que o funcionário substituído", "bizman_hrmanager_settings_train_employees": "Treine automaticamente funcionários até:", "bizman_hrmanager_average_salary": "<PERSON><PERSON><PERSON>", "bizman_hrmanager_average_satisfaction": "Satisfação Média", "bizman_hrmanager_average_primary_skill": "Porcentagem Média de Habilidade Primária", "bizman_hrmanager_assign_employees_title": "Atribuir funcionários a {manager} ({amount}/{max})", "common_assigned": "Atribu<PERSON><PERSON>", "broken_savegames": "Jogos salvos quebrados detectados!", "menu_brokensavegames_description": "Um ou mais jogos salvos não corrigíveis quando detectados:", "menu_brokensaves_delete": "Excluir jogos salvos quebrados (recomendado)", "menu_brokensaves_move": "Mover jogo<PERSON> salvos quebrados (recomendado)", "menu_brokensaves_ignore": "Ignorar jogos salvos quebrados", "common_area_feet": "{area} pés²", "common_area_meters": "{area} m²", "vehicletypename_umcnunavut": "UMC Nunavut", "skillname_hrmanager": "Gest<PERSON> de RH", "bizman_hrmanager_max_employees_reached": "Não é possível atribuir mais funcionários a este gerente de RH", "bizman_hrmanager_fill": "<PERSON><PERSON><PERSON>", "main_menu_custom_game_disable_energy": "Desativar energia", "main_menu_custom_game_disable_happiness": "Desativar felicidade", "happinessmodifiertype_cheat": "Magia", "transactiontype_hrtraining": "Custos de treinamento de {employee}", "itemname_eameschair": "<PERSON><PERSON> James Lawns", "itemname_eamesottoman": "James Lawns Otomano", "transactiontype_replacementwage": "Substituição para {employee} (salário de {businessName})", "vehicletypename_flatbed": "Flatbed", "bizman_settings_terminate_contract": "Clique abaixo para rescindir seu contrato de locação para este edifício. O depósito integral será devolvido.", "help_itemname_itemgrouppointofsale_content": "**Pontos de vendas** são necessários em empresas varejistas para lidar com as compras dos clientes.\n\nEstes equipamentos são pontos de vendas:\n* [Caixa registradora](furniture-cashregister)\n* [Balcão de caixa esquerdo](furniture-checkoutcounterleft)\n* [Balcão de caixa direito](furniture-checkoutcounterright)", "uncle_fred_tutorial_nicer_apartment": "Então você recebeu uma ligação do banco? Eles devem estar chocados ao ver o que está acontecendo com sua conta, haha... do jeito que está crescendo!<br><br> Você deveria encontrar um apartamento melhor. E compre alguns móveis muito bonitos. Você merece isso!", "tutorial_nicer_apartment_objective_1": "Alugue um apartamento (residencial) de pelo menos 90 m²", "tutorial_nicer_apartment_objective_2": "Compre uma <b>cama king size</b> da I<PERSON> Bolag", "dialog_recruitment_agency_cancel_campaign": "<PERSON><PERSON><PERSON> camp<PERSON>", "dialog_recruitment_agency_start_new_campaign": "Iniciar nova campanha", "dialog_recruitment_agency_already_has_campaign_continue": "Podemos te ajudar em mais alguma coisa?", "dialog_recruitment_agency_already_has_campaign_new": "O<PERSON><PERSON>, como podemos ajudá-lo hoje?", "dialog_recruitment_agency_manage_campaigns": "Gerenciar campanhas", "dialog_recruitment_agency_no_campaign_selected": "Desculpe sobre qual campanha você está falando de novo?", "dialog_recruitment_campaigns_list_info": "{businessName}, {skillKey}, {scheduleTypes}. {daysLeft} dias restantes, {amountOfCandidatesLeft} candidatos de {amountOfCandidates} restantes", "vehicletypename_petrollsfanton": "<PERSON><PERSON>", "purchasecar_specs": "Especificações", "purchasecar_colors": "Cores", "vehicle_tank_capacity": "Capacidade do tanque", "vehicle_auto_park_support": "Suporte ao estacionamento automático", "vehicle_max_speed": "velocidade máxima", "common_yes": "sim", "common_no": "Não", "tutorial_nicer_apartment_objective_3": "Coloque a cama king size em seu novo apartamento", "common_area_meter_unit": "m²", "common_area_feet_unit": "pés²", "common_distance_meter_unit": "m", "common_distance_feet_unit": "pés", "bizman_hrmanager_clear": "Cancelar atribuição de tudo", "help_skillname_hrmanager_content": "Funcionários com a habilidade de **Gerente de RH** são utilizados para sedes.\n\nPara cada funcionário atribuído ao Gerente de RH, o RH irá:\n* Aumentar a habilidade do funcionário enquanto eles trabalham\n* Fornecer um trabalhador temporário para cobrir o turno do funcionário se eles adoecerem.\n* Gerenciar um [Plano de Seguro Saúde](employee_demands_benefits_healthinsurance) do [Hospital](address: 0 7s) para o funcionário\n\nO nível de habilidade (%) define quantos funcionários eles podem lidar e quanto custa o treinamento automático dos funcionários.\n\nEles podem ser designados para:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n\nEles podem ser contratados através de:\n* [City Workforce Inc.](address: 41 4a)", "bizman_hrmanager_settings_train_employees_hint": "Nota: o treinamento é mais lento, mas mais barato e os funcionários continuarão trabalhando em suas tarefas", "uncle_fred_tutorial_marketing": "<PERSON><PERSON>, vamos voltar aos seus negócios novamente, porque você ainda tem muito o que aprender, garo<PERSON>. As coisas estão indo muito bem. Existem vários clientes em potencial por aí que nunca ouviram falar de suas lojas. Então é hora de começar a comercializar.", "uncle_fred_tutorial_totalprofit": "Lindo. Ei, certifique-se de manter a popularidade muito alta para todos os seus negócios. Às vezes, você pode ter que gastar mais dinheiro em marketing para obter os mesmos resultados que obteve antes, porque o mercado muda todos os dias. E a propósito, estou planejando visitar minha casa, minha casa de férias, na Costa del Sol. Mas quando eu voltar, é isso que eu espero ver: que você aumentou sua renda diária. É hora de levar seu império para o próximo nível, mas precisamos de mais dinheiro primeiro.", "hospital_respawn_timemachine_info": "Você está sendo levado para o hospital...", "casino_timemachine_info_start": "Viajar para Águas Internacionais...", "casino_timemachine_info_end": "Viajar para a cidade...", "itemname_donut": "Rosquin<PERSON>", "help_itemname_donut_content": "**Rosquinha** é um tipo de produto vendido principalmente em [Cafeterias](businesstypes-coffeeshop).\n\n<PERSON>ém disso, pode ser vendido em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> Padaria](furniture-bakeryshowcase)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "common_time": "{hours} horas {minutes} minutos", "playpanel_headline": "Jogar videogames", "playpanel_start_playing": "Começa a jogar", "happinessmodifiertype_playedvideogames": "Jogar videogames", "itemhelper_notification_no_more_energy_generation": "Você não está mais sentindo nenhum efeito da cafeína.", "businessrequirement_atleastoneproduct": "Mínimo 1 produto", "businessrequirement_pointofsales": "Caixa registradora ou balcão de caixa", "businessrequirement_computer": "Estação de trabalho de mesa", "businessrequirement_cashregister": "Caixa registradora", "businessrequirement_stackofshoppingbaskets": "<PERSON><PERSON><PERSON> de Cestos de Compras", "tutorial_nicer_apartment_objective_4": "Rescindir o contrato do seu antigo apartamento", "tutorial_13_objective_3": "Compre uma <b>grelha industrial</b> e um <b><PERSON><PERSON><PERSON></b>", "tutorial_13_objective_4": "Instale o armário e coloque a grelha industrial em cima dele na sua loja", "tutorial_13_objective_5": "Abasteça a grelha industrial com <b>hamb<PERSON>rgueres</b>", "itemcustomizer_set_custom_queue": "Definir fila personalizada", "itemcustomizer_reset_queue": "Redefinir fila", "intro_start_game": "Começar o jogo", "intro_name_placeholder": "Nome do personagem...", "intro_character_name": "Você pode me ligar...", "intro_body": "Corpo", "intro_head": "Cabeça", "intro_clothes": "<PERSON><PERSON><PERSON>", "dialog_bank_selector": "O<PERSON><PERSON>! Como posso te ajudar hoje?", "econoview_investments_changeperiod": "Alterar últimos 14 dias", "econoview_investments_changetotal": "Mudança total", "econoview_investments_order_payout": "Pagamento do pedido", "econoview_investments_initial_investment": "Investimento inicial", "econoview_investments_current_value": "<PERSON>or atual", "econoview_investments_notenoughdata": "Não há dados suficientes para mostrar o gráfico", "econoview_investments_noinvestments": "Você não tem nenhum investimento. Entre em contato com o seu banco para mais informações.", "econoview_investments_confirmpayout": "Tem certeza de que deseja retirar o saldo total deste fundo de investimento?", "investmentfundname_euroenergyhigh": "Euro Energy High", "investmentfundname_franklinus": "Franklin US", "investmentfundname_alliancestechnologya": "Alliances Technology A", "investmentfundname_laceglobala": "Lace Global A", "investmentfundname_asiadynamicindustries": "Asia Dynamic Industries", "investmentfundname_hgchinabonds": "HG China Bonds", "dialog_select_investment_fund": "Selecione o fundo de investimento", "common_investment_fund": "Fundo de investimento", "common_risk": "Risco", "dialog_bank_investment_accepted": "<PERSON><PERSON> obrigado. O valor total foi investido e agora você pode ver em seu portfólio usando o aplicativo EconoView.", "dialog_bank_new_investment": "Novo investimento", "dialog_bank_new_loan": "Novo empréstimo", "itemname_gamingcomputer": "Configuração básica do PC para jogos", "itemname_loudspeaker3": "P&S Peolab 90", "itemname_loudspeaker4": "Alto-falante JayBeeel 3", "menu_options_controls_control_mode": "Modo de controle", "options_control_mode_mouse": "Aponte e clique (mouse)", "options_control_mode_controller": "Teclado ou controle", "npc_expression_no_place_to_sitdown": "Não consigo encontrar um lugar para sentar.", "npc_expression_no_missing_trashbin": "Não consigo encontrar um lugar para colocar meu lixo.", "itemname_ceilinglamppinecone": "<PERSON><PERSON><PERSON> em forma de pinha", "bizman_enter_price_placeholder": "Inserir preço", "tutorial_objective_reach_profit": "Alcance um lucro diário de {moneyAmount}", "uncle_fred_tutorial_25": "Você está realmente pegando o jeito dessas coisas. <PERSON>r isso, eu acho que está na hora de você entrar em um novo território: negócios de escritório. É semelhante a empresas de varejo, mas é importante levar em consideração o que está em demanda no seu bairro específico. Você sabe o que fazer. Então faça!", "uncle_fred_tutorial_26": "Veja isso! Todas essas pessoas bem vestidas, sentadas em mesas, gan<PERSON>o dinheiro para você. Nem parece que, há pouco tempo, você estava só começando. Vou te dizer uma coisa, acho que está hora de uma pequena comemoração. Você não pode jogar na cidade, mas pode pegar um cruzeiro no rio Hudson e ir para alto-mar. Isso pode custar alguns dólares, mas você não é mais um garoto falido.", "uncle_fred_tutorial_27": "Você ganhou alguma coisa? <PERSON><PERSON>, não importa. Às vezes, é bom só gastar por gastar, se isso te deixar feliz. Mas agora está hora de voltar aos negócios. Você está começando a se sentir um pouco sobrecarregado com todas essas pessoas ficando doentes aqui e ali, não é? Você precisa contratar um gerente de RH para cuidar de tudo isso pra você.", "uncle_fred_tutorial_28": "<PERSON><PERSON><PERSON> <PERSON>, com gerentes de RH, você ganha mais tempo livre todos os dias. <PERSON><PERSON>, garo<PERSON>, lamento, mas agora eu tenho uma aula de golfe importante. Vá ganhar seus milhões e eu vejo você mais tarde.", "tutorial_objective_rent_office_building": "Alugue um escritório", "tutorial_objective_start_lawfirm": "Comece um escritório de advocacia", "tutorial_objective_hire_lawyer": "Contrate um advogado", "tutorial_objective_hire_hr_manager": "Contrate um Gerente de RH", "tutorial_objective_assign_lawyer": "Atribua uma nova mesa ao advogado no seu escritório de advocacia", "tutorial_objective_assign_hr_manager": "Atribua uma nova mesa ao Gerente de RH na sua sede", "tutorial_objective_assign_employees_to_hr_manager": "Atribua no mínimo um funcionário ao seu Gerente de RH na sede da BizMan", "tutorial_objective_buy_ticket_to_casino": "Compre passagem para o barco do cassino (apenas às sextas-feiras)", "tutorial_objective_2_mill_bank_balance": "Garanta $2,000,000 na sua conta bancária", "bizman_purchasingagents_amount_target_on": "meta #", "npc_expression_no_path": "É impossível se locomover nesta loja. Estou fora daqui!", "itemname_expensivetv": "P&S Sympathy de 88 polegadas", "click_to_play": "Clique para jogar videogames", "click_to_sleep": "Clique para dormir", "bizman_browse_files": "<PERSON><PERSON><PERSON>", "common_refresh": "<PERSON><PERSON><PERSON><PERSON>", "common_investments": "Investimentos", "notification_cant_takeover_inside": "Você não pode assumir o controle de um negócio enquanto estiver dentro dele", "happinessmodifiertype_afreshstart": "Um novo começo", "vehicletypename_mersaidimgagt": "Mersaidi MGA GT", "dialog_bank_investment_request_player": "Eu gostaria de investir {amount} em {investmentFund}", "dialog_bank_investment_header": "Investimento", "transactiontype_investment": "Investimento em {investmentFundName}", "transactiontype_investmentpayout": "Pagamento {investmentFundName}", "menu_options_controls_run_by_default_indoor": "Administrar por padrão em ambiente interno", "transactiontype_banknegativeinterestrate": "Taxa de juros bancários negativa", "main_menu_custom_game_bank_negative_interest_rate": "Taxa de juros bancários negativa", "common_revert": "<PERSON><PERSON><PERSON>", "common_apply": "Aplicar", "myemployees_configure_uniform_presets": "Configurar predefinições de uniforme", "myemployees_preset_customizer_generic_name": "Predefinição #{number}", "myemployees_uniform": "Uniforme", "myemployees_preset_customizer_unsaved_changes_warning": "Alterações não salvas na predefinição serão perdidas. Continuar?", "myemployees_preset_customizer_remove_preset": "Tem certeza que deseja excluir a predefinição '{presetName}'?", "alert_low_stock": "{producer_itemname} em breve ficará sem <u>{itemname}</u>", "alert_empty_stock": "{producer_itemname} está sem <u>{itemname}</u>", "alert_missing_employee": "O negócio está aberto, mas não há funcionários trabalhando", "alert_missing_required_item": "<PERSON><PERSON> pelo menos um(a) <u>{itemname}</u>.", "alert_missing_schedule": "O negócio não tem horário de funcionamento definido", "alert_no_producers": "O negócio não tem produtos para vender", "alert_dirty_floors": "O negócio está sujo e precisa de limpeza", "bizman_marketing_efficiency": "Eficiência de marketing", "bizman_marketing_efficiency_description": "As ações de marketing mudam de edifício para edifício. Negócios maiores exigem campanhas maiores e uma verba maior para serem eficientes.", "bizman_marketing_total_daily_expenses": "Total de despesas diárias", "marketing_notification_no_money_campaigns_disabled": "As campanhas de marketing da {businessName} foram desativadas devido a falta de dinheiro", "marketingtypename_smallinternet": "Campanha pequena na internet", "marketingtypename_mediuminternet": "Campanha média na internet", "marketingtypename_largeinternet": "Campanha grande na internet", "marketingagencydialog_notification_campaign_already_running": "Já existe uma campanha deste tipo em execução no negócio selecionado", "transactiontype_interiordesigner": "Designer de interiores", "itemcustomizer_set_colors": "Definir cores", "itemcustomizer_color_label": "Cor {number}", "transactiontype_furniturerecolor": "Repintar {itemName}", "itemcustomizer_no_colors_options": "Este item não tem opções de cores", "common_default": "Padrão", "econoview_row_negative_interest_rates": "Taxas de juros negativas", "itemname_groundplant01": "Planta em vaso alto 1", "itemname_groundplant02": "Planta em vaso alto 2", "itemname_groundplant03": "Planta em vaso quadrado", "itemname_groundplant04": "<PERSON><PERSON><PERSON><PERSON>", "tutorial_17_objective_2": "Comece a vender presentes caros na sua loja de presentes", "gamemanager_notification_autosave_started": "Salvando automaticamente ...", "menu_options_time_between_auto_saves": "Tempo entre salvamentos automáticos", "menu_options_max_auto_saves_per_game": "Máximo de salvamentos automáticos por jogo", "menu_options_time_between_auto_saves_mins": "{minutes} minutos", "menu_options_max_auto_saves_per_game_amount": "{amount} salvamentos", "itemname_cornersofa01": "<PERSON><PERSON><PERSON>", "entertain_panel_play_headline": "Jogar videogames", "entertain_panel_watchtv_headline": "Assistir à TV", "entertain_panel_start_play_button": "Come<PERSON>r a jogar", "entertain_panel_start_watchtv_button": "Começar a assistir à TV", "entertain_panel_stop_at": "<PERSON><PERSON> {time}", "entertain_panel_boost_info": "{boost} adicionou felicidade por {hours} horas", "itemname_scorpiogamingsetup": "Conjunto para jogar videogame Scorpio", "itemname_gamingchair": "Cadeira para jogar videogame", "itemname_cupcake": "Cupcake", "econoview_full_transactions_title": "Transações", "econoview_last_transactions_full_view_button": "Visualização completa das transações", "econoview_full_transactions_export_to_csv": "Exportar para CSV", "econoview_full_transactions_all_types": "Todos os tipos", "econoview_full_transactions_all_days": "Todos os dias", "amountoption_all": "Todos os valores", "amountoption_positive": "Valores positivos", "amountoption_negative": "Valores negativos", "common_label": "<PERSON><PERSON><PERSON><PERSON>", "common_balance": "<PERSON><PERSON>", "transactiontype_rent_label": "<PERSON><PERSON><PERSON>", "transactiontype_wage_label": "Pagamento", "transactiontype_revenue_label": "<PERSON><PERSON><PERSON>", "transactiontype_loanpayout_label": "Pagamento de empréstimo", "transactiontype_parkingticket_label": "Multa de estacionamento", "transactiontype_depositreturn_label": "Devolução de depósito", "transactiontype_playerjobsalary_label": "<PERSON><PERSON><PERSON>", "transactiontype_unassignedwage_label": "Pagamento", "transactiontype_marketing_label": "Marketing", "transactiontype_cheat_label": "<PERSON><PERSON><PERSON>", "transactiontype_subwayride_label": "<PERSON><PERSON><PERSON>", "transactiontype_recruitmentcampaign_label": "Campanha de recrutamento", "transactiontype_importdelivery_label": "Entrega de importação", "transactiontype_importdeliveryrefund_label": "Reembolso de entrega de importação", "transactiontype_deliverycontract_label": "Contrato de entrega", "transactiontype_deliverycontractrefund_label": "Reembolso de contrato de entrega", "transactiontype_itempurchase_label": "Compra de item", "transactiontype_loanpayment_label": "Pagamento de empréstimo", "transactiontype_loanpayoff_label": "Pagamento de empréstimo", "transactiontype_tuitionfee_label": "Mensalidade", "transactiontype_deposit_label": "<PERSON><PERSON><PERSON><PERSON>", "transactiontype_depositreturnfurniture_label": "Depósito de devolução de móveis", "transactiontype_employeetraining_label": "Treinamento de funcionários", "transactiontype_itemsold_label": "<PERSON>em vendido", "transactiontype_vehiclebought_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> comprado", "transactiontype_taxiride_label": "Trajeto de táxi", "transactiontype_hospitalbill_label": "Despesas hospitalares", "transactiontype_buildingbought_label": "<PERSON><PERSON><PERSON><PERSON>", "transactiontype_rentrevenue_label": "<PERSON><PERSON><PERSON> de aluguel", "transactiontype_autotowservice_label": "Serviço de reboque de automóveis", "transactiontype_taxpayment_label": "Pagamento de impostos", "transactiontype_buildingsold_label": "<PERSON><PERSON><PERSON><PERSON> vendi<PERSON>", "transactiontype_publicparking_label": "Estacionamento público", "transactiontype_casino_label": "Cassino", "transactiontype_casinoboatticket_label": "Passagem do barco do cassino", "transactiontype_hrtraining_label": "Treinamento de RH", "transactiontype_replacementwage_label": "Pagamento do substituto", "transactiontype_investment_label": "Investimento", "transactiontype_investmentpayout_label": "Pagamento do investimento", "transactiontype_banknegativeinterestrate_label": "Taxa de juros bancários negativa", "transactiontype_interiordesigner_label": "Design de interiores", "transactiontype_furniturerecolor_label": "Repintura de móveis", "myemployees_mass_action_dropdown_placeholder": "Com os selecionados...", "myemployees_mass_action_uniform": "Estabelecer como predefinição de uniforme {presetName}", "myemployees_mass_action_confirm": "Você realmente deseja aplicar a seguinte ação a {employeesAffected} funcionários: \"{action}\"", "myemployees_mass_action_no_employees_selected": "Nenhum funcionário se<PERSON>", "menu_options_controls_key_bindings": "Combinações de teclas", "menu_options_controls_key_bindings_change": "Alterar", "menu_options_controls_key_bindings_reset": "Redefinir", "menu_options_controls_key_bindings_bind_tip": "Você pode cancelar o remapeamento com ESC", "vehicletypename_anselmoaf90": "Anselmo AF90", "input_key_interact": "Interação", "input_key_secondaryinteract": "Interação secundária", "input_key_specialinteract": "Interação especial", "input_key_cancel": "<PERSON><PERSON><PERSON>", "input_key_rotateleft": "Girar para a esquerda", "input_key_rotateright": "<PERSON><PERSON><PERSON> para a direita", "input_key_confirm": "Confirmar", "input_key_nextoption": "Próxima opção", "input_key_previousoption": "Opção anterior", "input_key_menu": "<PERSON><PERSON>", "input_key_togglerunning": "Mudar para executar", "input_key_skipsong": "Pular a música", "input_key_sell": "Vender", "input_key_sleep": "<PERSON><PERSON><PERSON>", "input_key_opennotifications": "Abrir notificações", "vehicletypename_vordtiaravic": "Vord Tiara Vic", "tutorial_22_objective_9": "Atribua o gerente ao seu armazém usando a tela de Gerentes de Logística da sua sede (BizMan)", "dialog_bank_amount_to_invest": "Valor a investir", "common_select_option": "Selecione a opção", "myemployees_mass_action_assign_uniform_title": "Selecione a predefinição de uniforme a atribuir aos funcionários", "myemployees_mass_action_assign_uniform_confirm": "Atribuir como predefinição de uniforme {presetName}", "myemployees_mass_action_assign_business_option": "Atribuir negócio", "myemployees_mass_action_assign_business_title": "Selecione o negócio para atribuir aos funcionários", "myemployees_mass_action_assign_business_confirm": "Atribuir ao negócio {businessName}", "myemployees_mass_action_cant_assign_business_to_training_employee": "O funcionário {employeeName} está em treinamento e não pode ser atribuído ao negócio", "myemployees_mass_action_train_primary_skill_confirm": "Treinar habilidade primária. Nota: funcionários alocados serão desalocados e terão aumento no salário por hora.", "myemployees_mass_action_fire_confirm": "<PERSON><PERSON><PERSON>", "common_unassign": "Cancelar atribuição", "myemployees_mass_action_assign_business_unassign_confirm": "Cancelar atribuição ao negócio", "common_no_option_selected": "Nenhuma opção selecionada", "radio_now_playing": "A<PERSON>a tocando...", "menu_options_controls_keyboard": "Tecla", "menu_options_controls_controller": "Controlador", "input_key_move": "Mover", "input_key_steering": "Volante", "input_key_throttle": "Acelerador", "input_key_brakes": "<PERSON><PERSON><PERSON>", "input_key_handbrake": "Freio de m<PERSON>", "rebind_wait_for_input": "Aguardando entrada {type}...", "rebind_wait_for_type_input": "Aguardando entrada {type}...", "rebind_waiting": "<Aguardando...>", "rebind_binding": "Mapeamento {name}.", "input_component_up": "Para cima", "input_component_down": "Para baixo", "input_component_left": "E<PERSON>rda", "input_component_right": "<PERSON><PERSON><PERSON>", "tutorial_5_objective_5": "Pegue um carrinho manual de carga", "item_customizer_reset_colors": "Redefinir cores", "input_key_pause": "Pausar", "tutorial_objective_open_law_firm": "Abra um escritório de advocacia", "help_itemname_cupcake_content": "**Cupcake** é um tipo de produto vendido principalmente em [Cafeterias](businesstypes-coffeeshop).\n\n<PERSON>ém disso, pode ser vendido em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [<PERSON><PERSON><PERSON>daria](furniture-bakeryshowcase)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "rebinding_collision": "A entrada que você tentou conectar já está em uso", "input_expected_control_type_button": "Botão", "input_expected_control_type_vector2": "Eixo", "dialog_marketing_agency_more_help_offered": "Podemos ajudar com algo mais?", "dialog_marketing_agency_start_new_campaign": "Nova campanha", "happinessmodifiertype_watchedtv": "Assistiu à TV", "itemname_restaurantbooth": "Booth de restaurante", "tutorial_objective_return_to_the_city": "Volte para a cidade", "myemployees_mass_action_employee_has_no_valid_skills": "O funcionário {employeeName} não tem habilidades úteis para o negócio {businessName}", "colors_orange": "<PERSON><PERSON>", "econoview_row_parking_fees": "Taxas de estacionamento", "boattypename_speedboat": "Dragonfly F1", "boattypename_yacht": "SeaKing 8000", "boattypename_luxuryyacht": "Blue Yachts OG", "boat_yearly_maintenance": "Manutenção anual", "purchasevehicleui_notification_purchase_boat_successful": "Barco comprado com sucesso!", "transactiontype_boatbought_label": "<PERSON><PERSON> comprado", "transactiontype_boatbought": "Compra de {boatName}", "transactiontype_boatyearlymaintenance_label": "Manutenção anual do barco", "transactiontype_boatyearlymaintenance": "Manutenção do {boatName}", "transactiontype_boatsold_label": "<PERSON><PERSON> vendido", "transactiontype_boatsold": "{boatName} vendido", "boat_sell_confirmation": "Tem certeza que deseja vender {boatName} por {price}? Manutenções pré-pagas não serão reembolsadas", "boat_sleeppanel_header": "Relaxar no barco", "boat_sleeppanel_sleep_button": "<PERSON><PERSON><PERSON>", "input_key_autorun": "Mudar para execução automática", "main_menu_welcome_title": "Bem-vindo ao acesso antecipado Big Ambitions", "main_menu_welcome_message": "Em primeiro lugar, bem-vindo ao programa de acesso antecipado Big Ambitions!\n\nSomos um estúdio de jogos pequeno e totalmente independente tentando trazer\nalgo novo e exclusivo no gênero de gerenciamento.\n\nCom a sua decisão de adquirir o nosso jogo, podemos continuar pagando os salários dos\nnossos funcionários e seguir melhorando o jogo.\n\n<b>Portanto: muito obrigado, você é incrível! <3</b>", "main_menu_welcome_message_language": "Idioma", "main_menu_welcome_game_updates": "Cadastre-se para atualizações do jogo (opcional):", "main_menu_welcome_game_updates_email_placeholder": "<EMAIL>", "businesstype_hospital": "Hospital", "itemname_stoveoven": "Fogão com forno", "itemname_bookshelf": "<PERSON><PERSON><PERSON>", "itemname_desktopcomputer": "Computador <PERSON>", "itemname_trafficsign01": "Placa de trânsito 1", "itemname_trafficsign02": "Placa de trânsito 2", "itemname_cheapcoffeemachine": "Cafeteira barata", "dialog_doctor_npc_name": "<PERSON><PERSON>", "dialog_doctor_start": "Ol<PERSON>! Como posso ajudá-lo?", "dialog_doctor_extended_warranty": "Pergunte sobre garantia estendida", "dialog_doctor_too_young": "No momento, não podemos oferecer a você nenhum dos nossos tratamentos especiais.", "dialog_doctor_operation_explanation_1": "Ahhh, é você! O Fred já tinha me falado que você viria. Prazer em conhecê-lo. Certo, vou direto ao assunto. O que vamos fazer é injetar em você algo chamado \"BA-1003-0817\". Devo dizer que isso não é aprovado, certificado ou mesmo conhecido por qualquer órgão governamental. Isso é algo muito especial que preferimos manter fora dos olhares do público.", "dialog_doctor_operation_explanation_2": "Alguns minutos após a injeção, seu corpo começará a reverter o envelhecimento. Normalmente, vemos a idade do nosso cliente diminuir em 10 anos.", "dialog_doctor_operation_explanation_3": "A cada injeção, o modelo de DNA se torna mais difícil de elaborar. É por isso que temos que cobrar um pouco mais a cada aplicação.", "dialog_doctor_operation_explanation_proceed": "Deseja continuar?", "hospital_operation_info": "Executando operação...", "transactiontype_doctorappointment": "Consulta médica", "itemname_wardrobe": "Guarda roupa", "tutorial_4_objective_2": "Encontre e alugue um <b>edif<PERSON><PERSON> de <PERSON>ejo</b> de <b>no máximo {size}</b> em <b>Garment District</b> usando o <b>Voogle Maps</b>", "personalgoal_owned_vehicles": "Possuir um total de {amount} veí<PERSON>los", "personalgoal_owned_boats": "Possuir um total de {amount} barcos", "personalgoal_apartment_value": "Possuir um apartamento com móveis que valem pelo menos {amount}", "personalgoal_apartments_rented": "Al<PERSON><PERSON> pelo menos {amount} apartamentos", "personalgoal_warehouses_rented": "Alugar pelo menos {amount} armazéns", "personalgoal_bank_balance": "Alcançar um saldo bancários de {amount}", "personalgoal_business_weekly_income": "Alcançar uma renda semanal de {amount} para um negócio", "personalgoal_employee_max_skill": "Alcançar o nível de {amount}% de qualquer habilidade para um funcionário", "personalgoal_boat_value": "Possuir um barco que vale pelo menos {price}", "personalgoal_taxi_rides": "Completar {amount} trajetos de táxi", "personalgoal_vehicles_repairs": "Gastar pelo menos {amount} em conserto de veículos", "personalgoal_gas_spent": "Gastar pelo menos {amount} em gasolina", "personalgoal_age": "Comemorar seu {amount}° aniversário", "personalgoal_uncle_fred_objectives": "Complete {amount} objetivos do tio Fred", "personalgoal_hr_managers": "Contratar e fazer a programação de pelo menos {amount} Gerentes de RH", "personalgoal_lawyers": "Contrate e agende pelo menos {amount} advogados", "personalgoal_programmers": "Contrate e agende pelo menos {amount} programadores", "personalgoal_deliverydrivers": "Contrate e agende pelo menos {amount} motoristas de entrega", "personalgoal_taxes_paid": "<PERSON><PERSON> pelos menos {amount} em impostos", "personalgoal_hospitalized": "Ficar hospitalizado {amount} vezes", "personalgoal_realestate": "Possuir pelo menos {amount} edif<PERSON><PERSON>s", "personalgoal_casinowin": "<PERSON><PERSON><PERSON> um total de {amount} no cassino", "personalgoal_casinovisits": "Visitar o cassino pelo menos {amount} vezes", "personalgoal_parkingtickets": "Re<PERSON>ber pelo menos {amount} multas de estacionamento", "personalgoal_interiordesigner": "Gastar pelo menos {amount} em design de interiores", "personalgoal_dailycustomers": "Atingir pelo menos {amount} clientes por dia para um negócio", "loading_scene_loading": "Carregando {scene}", "loading_scene_unloading": "Descar<PERSON><PERSON><PERSON> {scene}", "loading_screen_loaded": "Carregado!", "loading_screen_mainscene": "Fundações", "loading_screen_ui": "Interfaces", "loading_screen_buildings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading_screen_exteriorprops": "Obstáculos e outros aborrecimentos", "loading_screen_lightsandpostprocessing": "Sol e Lua", "loading_screen_streets": "<PERSON><PERSON><PERSON>", "loading_screen_indoor": "Casas e negócios", "loading_screen_mainmenu": "<PERSON>u principal", "loading_screen_intro": "Introdução", "common_requires_dlc": "<PERSON>ste conteúdo requer o DLC {dlc}", "tutorial_13_objective_6": "Cumpra todos os requisitos da sua loja e abra-a (Dica: abra o BizMan Insight para ver os requisitos)", "tutorial_nicer_apartment_objective_5": "Compre uma mesa, uma cadeira e um computador da IKA Bohag", "tutorial_nicer_apartment_objective_6": "Coloque a mesa em seu apartamento e junte com a cadeira e computador.", "tutorial_nicer_apartment_objective_7": "Jogue videogame por pelo menos 1 hora", "confirm_work_at_closed_business": "Este negócio não está planejado para abrir hoje. Tem certeza?", "npc_expression_demand_music": "<PERSON><PERSON> gosto quando as lojas tocam música. Está estranhamente quieto aqui.", "npc_expression_demand_seating": "Eles querem que a gente sente no chão? Uau...", "npc_expression_demand_employeeuniforms": "Que tal um uniforme, talvez?", "npc_expression_demand_interiordesign": "As paredes e pisos são tão feios aqui.", "uncle_fred_tutorial_29": "Uau! Eu não previa tanto dinheiro. Espero que você já tenha pago aquele empréstimo de $15.000 para o <PERSON>, hein? De qualquer forma, ter todo esse dinheiro no banco faz você se sentir bem, mas não é uma atitude tão inteligente. Você já ouviu falar em juros negativos? Significa que o banco cobra para manter o seu dinheiro. Além do mais, você não está ganhando praticamente nada com todo esse dinheiro. Faria muito mais sentido colocar seu dinheiro em um fundo de investimento.", "uncle_fred_tutorial_30": "Esse é um lugar muito melhor para os seus dólares. Acredite, nenhum homem rico deixará muito dinheiro no banco. Os fundos de investimento são a solução. O dinheiro trabalha para você, assim como os seus funcionários. Mudando de assunto, lembre-se de se cuidar. Pense um pouco na sua felicidade. A vida não é só acumular dinheiro.", "uncle_fred_tutorial_31": "É bom ver você com um sorriso no rosto. A propósito, eu ia te perguntar: você notou uma queda nas vendas no Garment District? Nunca se sabe. Às vezes, as coisas aumentam. Outras vezes, elas caem. O melhor a fazer é diversificar seus negócios. Chamamos isso de espalhar o risco.", "uncle_fred_tutorial_32": "<PERSON><PERSON><PERSON> be<PERSON>, isso tornará seu império muito menos vulnerável a todas essas possíveis mudanças de mercado. De qualquer forma, agora eu tenho que voltar ao meu jogo de golfe, para melhorar o meu swing. E não esqueça que eu ainda tenho aquela sangria à sua espera em Marbella. <PERSON><PERSON> mais, garoto.", "uncle_fred_tutorial_33": "<PERSON><PERSON>, garo<PERSON>, você sabe se é possível trapacear no golfe? Preciso descobrir isso, pois não consigo vencer o <PERSON> e isso está me deixando louco. Ah, falando nisso, ele me disse que o valor dos seus negócios está disparando. Isso é ótimo. Mas lembre-se do que eu disse sobre espalhar o risco. O mesmo vale para os seus investimentos. Que tal um pouco de tijolo e argamassa? Acho que você deveria começar a investir em imóveis.", "uncle_fred_tutorial_34": "Veja só esse número! Quem diria? <PERSON><PERSON> dinhe<PERSON>. <PERSON><PERSON>ra dos velhos tempos, quando eu tinha que pagar seu aluguel? Provavelmente, você está começando a perceber que ganhar dinheiro não traz felicidade, não é? Isso é muito normal. A vida não é só ganhar dinheiro. Também é preciso ter uma meta e aproveitar a jornada. Aqui está outro objetivo desafiador para você.", "uncle_fred_tutorial_35": "<PERSON><PERSON>, eu inventei esse desafio. Na verdade, nunca esperei que você fosse alcançar. Você é incrível. Invencível mesmo. Estou começando a me preocupar com a minha posição de primeiro na lista de bilionários. Ei, não me venha com ideias, garoto.", "uncle_fred_tutorial_36": "Você está fazendo 65 anos. Parabéns! Um dia desses, você vai começar a ter rugas. Mas eu ainda vou te chamar de criança. Agora vou te dar o maior presente da sua vida. Preste muita atenção: pessoas como nós têm acesso a coisas que outras pessoas não têm. Então, vá visitar o Midtown Hospital. Procure o Sr. Ternity e diga que você está interessado no pacote de garantia estendida. É um pouco caro. Algo em torno de $1.000.000.000, se me lembro bem. Um preço que não para de subir.", "uncle_fred_tutorial_37": "Já está se sentindo-se mais jovem? <PERSON><PERSON><PERSON>, acho que não tenho mais nada para te ensinar. Estou realmente muito impressionado com tudo o que você conquistou. E eu tenho certeza que você fará muito mais no futuro. Então, espero que você vá me visitar em Marbella, como prometeu. Ouvi dizer que vão até abrir um novo aeroporto em breve. Até mais. Cuide-se, garoto.", "tutorial_29_objective_1": "Aplique pelo menos $1,000,000 em fundos de investimento", "tutorial_30_objective_1": "Alcance 100% de felicidade", "tutorial_31_objective": "Administre 2 negócios em {neighbourhood}", "tutorial_32_33_objective_1": "Atinja uma avaliação de {valuation}", "tutorial_33_objective_1": "Compre um edifício", "tutorial_34_objective_1": "Administre 80 negócios na cidade", "tutorial_35_objective_1": "Alcance o top 10 da Tabela de Classificação dos Rivais", "tutorial_35_objective_2": "Chegue aos 65 anos", "tutorial_36_objective_1": "Faça a cirurgia especial no Midtown Hospital", "tutorial_37_objective_1": "Espere o aeroporto internacional abrir", "old_age_back_up_save_name": "{years} anos de idade salvos", "dialog_doctor_proceed": "Procedimento ({price})", "funeral_timemachine_info": "<PERSON><PERSON><PERSON> morreu", "funeral_ui_load_back_up_save": "Voltar no tempo para {age} anos de idade", "loading_screen_transitiontosave": "Salva a transição", "elevator_go_to_parking": "Ir para o estacionamento", "elevator_go_to_exit": "Ir para fora", "elevator_go_to_building": "Ir para o edifício", "elevator_cant_enter_building": "Este edifício não está acessível", "common_casino": "Cassino", "common_international_waters": "Alto-mar", "happinessmodifiertype_restedonaboat": "Tranquilo em um barco", "loading_save": "Carregando jogo salvo", "autotowservicedialog_notification_no_free_space": "Algo está bloqueando o destino do reboque. Remova quaisquer objetos ou veículos primeiro.", "vehicleautoparksystem_vehicle_to_broken": "Seu veículo está muito danificado para usar este recurso.", "personalgoal_items_in_stock": "Faça uma reserva de pelo menos {amount} de qualquer produto", "specialbuilding_hospital": "Hospital", "econoview_row_other_income": "<PERSON>ra renda", "econoview_row_salary_income": "<PERSON><PERSON><PERSON>", "bizman_tooltip_item_in_use_title": "Item em uso", "bizman_tooltip_item_in_use_description": "Este item está sendo usado atualmente no negócio específico", "options_unstuck_title": "Liberar personagem", "options_unstuck_button": "Liberar", "options_unstuck_description": "Move o personagem para a saída do atual edifício ou edifício mais próximo", "jobdemand_cleanworkplace": "Ambiente de trabalho limpo", "jobdemand_cleanworkplace_description": "O funcionário exige ter pelo menos 80% de limpeza no edifício", "input_key_rotate": "<PERSON><PERSON><PERSON>", "input_key_zoom": "Ampliar", "itemname_industrialfreezer": "Freezer industrial", "tutorial_9_objective_3": "Pegue um esfregão na estação de limpeza e use para limpar os pisos da sua loja", "systemrequirements_header": "Requisitos de sistema", "systemrequirements_description": "Parece que seu computador não atende os requisitos mínimos para jogar de forma ideal. Pode haver travamentos ou baixo desempenho.", "systemrequirements_close": "Fechar e aceitar possíveis problemas", "systemrequirements_supported": "Compatível", "systemrequirements_unsupported": "Não compatível", "systemrequirements_cpuspeed": "Velocidade da CPU", "systemrequirements_ram": "Memória do sistema", "systemrequirements_vram": "Memória Gráfica", "systemrequirements_dedicatedgpu": "GPU específica", "systemrequirements_dedicated": "Específica", "systemrequirements_integrated": "Integrado", "systemrequirements_shaderlevel": "<PERSON><PERSON><PERSON> de shader", "systemrequirements_computeshader": "Compute Shader Support", "systemrequirements_gigabyte": "GB", "systemrequirements_gigahertz": "GHz", "menu_credits": "C<PERSON>dit<PERSON>", "credits_developers": "<PERSON><PERSON><PERSON><PERSON>", "credits_3dartists": "Artistas 3D", "credits_characterandanimations": "Personagens e Animações", "credits_gameproducer": "Produtor do jogo", "credits_uidesign": "Design de interface do usuário", "credits_soundeffects": "Efeitos sonoros", "credits_qualityassurance": "<PERSON><PERSON><PERSON> da Qualidade", "credits_translations": "Traduções", "credits_musiclicensing": "Licenciamento de música", "menu_forum": "Fórum", "help_itemname_industrialfreezer_content": "**Freezer Industrial** pode ser usado para vender:\n\n* [Comida congelada](products-frozenfood)\n* [Sorvete](products-icecream)\n \n**Capacidade de Produtos:**\n* Comida Congelada: 300\n* Sorvete: 50\n**Capacidade de Clientes:** 30\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [Square Appliances](address:16 4a)", "change_character_clothes_title": "<PERSON><PERSON><PERSON>", "change_character_clothes_save_button": "<PERSON><PERSON> rou<PERSON>", "change_character_clothes_unsaved_changes_warning": "Alterações não salvas serão perdidas. Continuar?", "uniform_customizer_save_preset_changes": "Salvar alterações na predefinição selecionada", "systemrequirements_your_system_header": "<PERSON><PERSON> siste<PERSON>", "systemrequirements_minimum_requirements_header": "Requisitos mín<PERSON>", "color_picker_hex_value": "Valor hexadecimal", "menu_save_and_exit_to_desktop": "Salvar e sair", "itempanelui_notification_item_too_tall": "Este item é muito alto para este edifício", "itemname_shopbarrier": "Divisória de loja", "checkout_counter_warning": "Balcões de caixa não são compatíveis com este tipo de negócio.\nUse caixas registradoras.", "econoview_loans_noloans": "Você não tem nenhum empréstimo. Entre em contato com o seu banco para mais informações.", "bizman_insight_interior": "Interior", "bizman_insight_help_customer_service": "Possuir funcionários altamente treinamentos e satisfeitos irá aumentar este valor.", "bizman_insight_help_pricing": "Aumentar ou diminuir os preços dos produtos ou serviços que você vende terá um impacto na satisfação. Cobrar um preço mais alto pode resultar em menor satisfação do cliente.", "bizman_insight_help_interior": "Decorar o piso e as paredes do seu negócio aumentará esse valor. Acrescentar móveis como mesas, lixeiras, alto-falantes etc. terá um efeito positivo.\n\nMonitorar seus funcionários e clientes dentro do edifício pode dar mais ideias.", "bizman_insight_help_cleanliness": "Uma loja limpa deixará seus clientes satisfeitos. Esfregue regularmente ou contrate funcionários de limpeza para fazer o trabalho para você.", "bizman_insight_help_traffic_index": "O índice de tráfego mostra quantas pessoas passam pelo seu negócio. Mais tráfego equivale a mais clientes em potencial. Este índice é baseado na localização e não pode ser melhorado.", "bizman_insight_help_marketing": "O marketing indica o quanto você está anunciando sua empresa para clientes em potencial. Isso pode ser melhorado com a realização de campanhas de marketing.", "click_to_get_mop": "Click para pegar um esfregão", "click_to_change_clothes": "Clique para trocar de roupa", "click_to_watch": "Clique para assistir à TV", "work_panel_confirm_work_on_day_off": "Você não tem turno programado para hoje. Tem certeza que quer trabalhar?", "systemrequirements_folderaccess": "Acesso de leitura à pasta de ativos", "systemrequirements_folderaccesserrorheader": "Falha ao acessar os arquivos", "systemrequirements_folderaccesserrorlabel": "O jogo não está conseguindo acessar os seguintes arquivos no seguinte comando:\n{folder}\nPara resolver isso, tenha certeza de que sua conta tenha acesso a leitura e gravação na pasta e no seu conteúdo. Alé<PERSON> disso, se você tiver algum programa de segurança, tente adicionar o caminho à lista de permissões do programa.", "employee_contact_description": "Funcionário", "employee_contact_message_low_satisfaction": "<PERSON><PERSON>, chefe!\n\nDevo informar que não tenho andado feliz com o meu trabalho nos últimos tempos. Para ser sincero, se a situação não mudar logo, serei forçado a procurar um emprego em outra empresa.\n\nEspero que você entenda e faça as mudanças necessárias.\n\nAtenciosamente,\n{employeeName} ({businessName})", "employee_contact_message_quit": "<PERSON><PERSON> de novo,\n\nLamento informar que, devido à baixa satisfação no trabalho, peço demissão do meu cargo de {skillKey} com efeito imediato.\n\nAtenciosamente,\n{employeeName} ({businessName})", "employee_contact_message_retire": "<PERSON><PERSON>, chefe!\n\n<PERSON>ssa, o tempo voa! Hoje é o meu último dia de trabalho como {skillKey}. Cheguei aos 65 anos e posso finalmente me aposentar da atividade repetitiva.\n\nObri<PERSON> pelo emprego!\n\nMuitas felicidades,\n{employeeName} ({businessName})", "common_add_item_to_storage": "Adicionar item ao armazem", "menu_options_fps_vsync_x": "Vsync (máx {x}FPS)", "menu_options_controller_mode_confirm": "O modo controlador ainda está em desenvolvimento e não tem suporte total. Tem certeza?", "interior_designer_cancel_confirm": "Existem alterações não aplicadas que serão perdidas. Tem certeza?", "employee_contact_message_retirement_notice": "O<PERSON>, chefe!\n\nNo dia {days}, finalmente vou me aposentar e deixarei de trabalhar para você como {skillKey}.\n\nAgradeço!\n\nAtenciosamente,\n{employeeName} ({businessName})", "notification_vehicle_to_damaged_to_drive": "O veículo está muito danificado para dirigir.", "gender_male": "<PERSON><PERSON><PERSON><PERSON>", "gender_female": "Feminino", "itemname_waitingroomchairs": "Cadeiras de sala de espera", "itemname_framedcertificate": "Certificado emold<PERSON>do", "itemname_roundtable": "Mesa redonda", "itemname_moderncounter": "<PERSON><PERSON><PERSON>", "itemname_moderncountercorner": "Gabinete de Canto Moderno", "itemname_officelockers": "Armário de escritório", "itemname_exitsign": "Placa de saída", "itemname_beltbarrier": "Barreira de fita", "itemname_ropebarrier": "Barreira de corda", "itemname_glassbarrier": "Divisória de vidro", "itemname_clothingrackangled": "<PERSON><PERSON><PERSON><PERSON> inclinado", "itemname_counterwithglass": "Balcão com vidro", "itemname_counterwithglasscornerright": "Balcão com vidro de canto direito", "itemname_counterwithglasscornerleft": "Balcão com canto esquerdo de vidro", "itemname_paperbin": "Lixeira de papel", "itemname_modernshelf": "Estante moderna", "itemname_modernshelfhorizontal": "Estante moderna horizontal", "itemname_fitball": "Fitball", "itemname_fitnesstrampoline": "Trampolim de ginástica", "itemname_boxingbag": "Saco de boxe", "itemname_treadmill": "<PERSON><PERSON><PERSON>", "itemname_gymmat": "Tapete de Ginástica", "itemname_workoutbench": "Banco de exercício", "itemname_squatsstation": "Estação de agachamento", "item_customizer_waiting_line_add_point": "Adicione posição na fila de espera pressionando [{interactKey}]", "notification_save_not_allowed": "Não é possível salvar o jogo no momento", "itemname_largedrinksfridge": "Refrigerador de bebidas grande", "help_itemname_largedrinksfridge_content": "**Geladeira de Bebidas (Grande)** pode ser usado para vender:\n\n* [Lata de Refrigerante](products-sodacan)\n\n**Capacidade de Produtos:** 360\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "menu_options_graphics_lowdetailcitymap": "Mapa da cidade com poucos detalhes", "npc_expression_no_available_cash_registers": "<PERSON><PERSON> as filas estão cheias!", "logistics_manager_product_runs_out_in_empty": "<PERSON><PERSON><PERSON>", "logistics_manager_product_runs_out_in_never": "Nunca", "logistics_manager_product_runs_out_in_days": "{days} dias", "input_key_openbugreport": "Abrir relatório dos <PERSON>", "input_key_openhelp": "<PERSON><PERSON><PERSON>", "input_key_quicksave": "Salvamento rápido", "input_key_openmap": "<PERSON><PERSON>r <PERSON>", "dialog_recruitment_agency_no_businesses": "<PERSON><PERSON><PERSON><PERSON>, mas você precisa de pelo menos um negócio estabelecido para usar nossos serviços.", "input_key_reversecarcam": "Câmera de ré", "input_key_showallitemoverlays": "<PERSON>rar todas as sobreposições dos itens", "transactiontype_compatibilityfix": "{text}", "transactiontype_compatibilityfix_label": "Correção de compatibilidade", "menu_options_controls_vehicle_mouse_input": "Ativar controle do carro pelo mouse", "dialog_uncle_fred_cancel_tutorial_start_dialog": "Ei, jovem! De que você precisa?", "dialog_uncle_fred_cancel_tutorial_confirm_dialog": "Ah... você quer se livrar de mim, então? Não vou mais te guiar. Sem objetivos, sem mais ligações de incentivo... Tem certeza?", "dialog_uncle_fred_cancel_tutorial_end_dialog": "<PERSON>á certo, jove<PERSON>. Se mudar de ideia, me chame de novo. Até mais!", "dialog_uncle_fred_first_cancel_dialog_button": "<PERSON><PERSON>", "dialog_uncle_fred_second_cancel_dialog_button": "Deixe para lá", "dialog_uncle_fred_cancel_tutorial_button": "Cancelar tutorial", "dialog_uncle_fred_reenable_tutorial_start_dialog": "<PERSON><PERSON><PERSON>, jovem, precisa de ajuda?", "dialog_uncle_fred_reenable_tutorial_end_dialog": "Maravilhoso! Vou voltar a falar algumas dicas!", "dialog_uncle_fred_cancel_reenable_button": "<PERSON><PERSON><PERSON>", "dialog_uncle_fred_reenable_tutorial_button": "Ativar tutorial", "help_itemname_clothingrackangled_content": "**<PERSON><PERSON><PERSON><PERSON> (Angular)** pode ser usado para vender:\n\n* [<PERSON><PERSON><PERSON> (Feminino Clássico e Barato)](products-classiccheapfemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Clássico e Barato)](products-classiccheapmaleclothing)\n* [<PERSON><PERSON><PERSON> (Feminino Clássico e Caro)](products-classicexpensivefemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Clássico e Caro)](products-classicexpensivemaleclothing)\n* [<PERSON><PERSON><PERSON> (Feminino Moderno e Barato)](products-moderncheapfemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Moderno e Barato)](products-moderncheapmaleclothing)\n* [<PERSON><PERSON><PERSON> (Feminino Moderno e Caro)](products-modernexpensivefemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Moderno e Caro)](products-modernexpensivemaleclothing)\n\n**Capacidade de Produtos:** 25\n**Capacidade de Clientes:** 5\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "notifications_title": "Notificações", "notifications_remove_all": "Remover todas as notificações", "notifications_remove_all_confirm": "Tem certeza? Não é possível desfazer essa ação", "notifications_no_notifications": "Não há notificações para exibição", "bizman_settings_save_logo": "Salvar alterações do logo", "bizman_settings_sign_appearance_info": "Exibição de placa", "transactiontype_furnituredeliveryrefund": "{businessName} cancelou a entrega para {address}", "transactiontype_furnituredeliveryrefund_label": "Reembolso da entrega do móvel", "playeractivityui_timemachine": "Avançar o tempo", "playeractivityui_fast_forward": "Avançar tempo rapidamente", "playeractivityui_time_remaining": "{hours} horas, {minutes} minutos restantes", "playeractivityui_decrease_slider": "<PERSON><PERSON><PERSON><PERSON>", "playeractivityui_increase_slider": "Aumentar", "workoutui_slider_label": "Faça exercícios por {workoutHours} horas e {workoutMinutes} minutos.\nIsso proporcionará um aumento de felicidade de {boostPercentage}% durante {boostHours} horas", "workoutui_start": "Começar gin<PERSON>", "workoutui_stop": "<PERSON><PERSON> <PERSON>", "workouttype_jump": "<PERSON><PERSON>", "workouttype_run": "<PERSON><PERSON>", "workouttype_situps": "Abdominais", "workouttype_boxing": "Boxe", "workouttype_squats": "Agachamento", "workouttype_benchpressing": "<PERSON><PERSON><PERSON>", "happinessmodifiertype_exercised": "Atividade física", "entertainui_stop_play": "<PERSON><PERSON>", "entertainui_stop_watchtv": "Parar de assistir TV", "entertainui_slider_label_play": "Jogue por {entertainingHours} horas, {entertainingMinutes} minutos.\nResulta em um aumento de felicidade de {boostPercentage}% por {boostHours} horas", "entertainui_slider_label_watchtv": "Assista TV por {entertainingHours} horas, {entertainingMinutes} minutos.\nResulta em um aumento de felicidade de {boostPercentage}% por {boostHours} horas", "workui_headline_label_default": "<PERSON><PERSON><PERSON><PERSON>", "workui_headline_label_with_job": "Trabalhe como {jobName}", "workui_start": "Começar a trabalhar", "workui_stop": "<PERSON><PERSON> <PERSON>", "workui_no_shifts_for_today": "Não há turnos atribuídos para hoje", "sleepui_bed_headline": "Dormir na cama", "sleepui_car_headline": "Dorm<PERSON> no veículo", "sleepui_bench_headline": "Descansar no banco", "sleepui_boat_headline": "Relaxar no barco", "sleepui_slider_label_bed": "Durma por {sleepHours} horas, {sleepMinutes} minutos.\nAcordará à(s) {time}", "sleepui_slider_label_car": "Durma por {sleepHours} horas, {sleepMinutes} minutos.\nAcordará à(s) {time}", "sleepui_slider_label_bench": "Descanse por {sleepHours} horas, {sleepMinutes} minutos.\nAcordará à(s) {time}", "sleepui_slider_label_boat": "Relaxe por {sleepHours} horas, {sleepMinutes} minutos.\nAcordará à(s) {time}", "sleepui_start_bed_button": "<PERSON><PERSON><PERSON> a dormir", "sleepui_start_car_button": "<PERSON><PERSON><PERSON> a dormir", "sleepui_start_bench_button": "Começar a descansar", "sleepui_start_boat_button": "<PERSON><PERSON><PERSON> a relaxar", "sleepui_stop_bed_button": "<PERSON><PERSON> de dormir", "sleepui_stop_car_button": "<PERSON><PERSON> de dormir", "sleepui_stop_bench_button": "Parar de descansar", "sleepui_stop_boat_button": "<PERSON><PERSON>", "studyui_slider_label": "Estude por {studyHours} horas, {studyMinutes} minutos. Terminará às {time}", "studyui_stop_button": "Parar de estudar", "studyui_studying_until": "Estudando até à(s) {time}", "input_key_sliderleft": "Controle deslizante para a esquerda", "input_key_sliderright": "Controle deslizante para a direita", "exitzonedespawner_notification_cant_leave_without_paying_playerowned": "Você não pode sair com uma cesta de compras. Você precisa pagar pelos produtos.", "notification_cashregister_no_employee": "Não há funcionários para te atender neste momento", "workui_do_order": "Realizar ped<PERSON> (grátis)", "menu_options_game_speed": "Velocidade do jogo", "elevatoroverlay_header": "Elevador", "business_description_gym": "Corpos poderosos. Mentes elevadas. Levantar, suar, repetir", "gym_enter_button": "Entrada (${price})", "gym_exit_button": "<PERSON><PERSON>", "transactiontype_gymentrance": "Entrada de {businessName}", "transactiontype_gymentrance_label": "Entrada da Academia", "contacts_remove_contact_confirm": "Quer mesmo remover este contato?", "contacts_header_clear_chat_button": "<PERSON><PERSON> bate-papo", "contacts_clear_chat_confirm": "Quer mesmo remover todas as mensagens deste contato?", "contacts_remove_message_confirm": "Quer mesmo remover esta mensagem?", "input_key_performactionwithoutconfirm": "Executar a ação sem confirmar", "help_vehicletypename_anselmoaf90_content": "**Anselmo AF90** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 3\n* Capacidade do Tanque de Combustível: 68\n* Suporte para Estacionamento Automático: Sim\n* Velocidade Máxima: 100\n* Preço Total: $520,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_bima320_content": "**Bima 320** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 10\n* Capacidade do Tanque de Combustível: 57\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 70\n* Preço Total: $48,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_ferdinand112_content": "**<PERSON> 112** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 3\n* Capacidade do Tanque de Combustível: 77\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 90\n* Preço Total: $130,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_freighttruckt1_content": "**Freight Truck T1** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 60\n* Capacidade do Tanque de Combustível: 378\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 50\n* Preço Total: $98,000\n* Dedutível de Impostos: Sim\n\nO veículo pode ser comprado nos seguintes locais:\n* [General US Trucks](address: 78 3s)", "help_vehicletypename_honzamimic_content": "**Honza Mimic** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 8\n* Capacidade do Tanque de Combustível: 55\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 60\n* Preço Total: $2,500\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [City Cars](address: 76 3s)", "help_vehicletypename_mersaidimgagt_content": "**Mersaidi MGA GT** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 5\n* Capacidade do Tanque de Combustível: 66\n* Suporte para Estacionamento Automático: Sim\n* Velocidade Máxima: 100\n* Preço Total: $220,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_mersaidis500_content": "**Mersaidi S500** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 8\n* Capacidade do Tanque de Combustível: 80\n* Suporte para Estacionamento Automático: Sim\n* Velocidade Máxima: 80\n* Preço Total: $95,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_missamvillian_content": "**Lytte L6** é um tipo de veículo que você pode comprar.\n\nEspecificações do veículo:\n* Capacidade de carga: 8\n* Capacidade do tanque de combustível: 80\n* Suporte para estacionamento automático: Sim\n* Velocidade máxima: 80\n* Preço total: $156,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_petrollsfanton_content": "**Petrolls <PERSON>** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 8\n* Capacidade do Tanque de Combustível: 80\n* Suporte para Estacionamento Automático: Sim\n* Velocidade Máxima: 80\n* Preço Total: $720,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_umcdesert_content": "**UMC Desert** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 14\n* Capacidade do Tanque de Combustível: 140\n* Suporte de Estacionamento Automático: Não\n* Velocidade Máxima: 50\n* Preço Total: $8,000\n* Dedutível de Impostos: Sim\n\nO veículo pode ser adquirido nos seguintes locais:\n* [General US Trucks](address: 78 3s)", "help_vehicletypename_umcnunavut_content": "**UMC Nunavut** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 18\n* Capacidade do Tanque de Combustível: 110\n* Suporte para Estacionamento Automático: Sim\n* Velocidade Máxima: 80\n* Preço Total: $65,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_vordpony_content": "**Vord Pony** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 5\n* Capacidade do Tanque de Combustível: 72\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 80\n* Preço Total: $187,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [Manhattan Luxury Cars](address: 15 bw)", "help_vehicletypename_vordtiaravic_content": "**Vord Tiara Vic** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 12\n* Capacidade do Tanque de Combustível: 72\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 70\n* Preço Total: $11,000\n* Dedutível de Impostos: Não\n\nO veículo pode ser comprado nos seguintes locais:\n* [City Cars](address: 76 3s)", "help_vehicletypename_vordv150_content": "**Vord V150** é um tipo de veículo que você pode comprar.\n\nEspecificações do Veículo:\n* Capacidade de Carga: 20\n* Capacidade do Tanque de Combustível: 118\n* Suporte para Estacionamento Automático: Não\n* Velocidade Máxima: 80\n* Preço Total: $44,000\n* Dedutível de Impostos: Sim\n\nO veículo pode ser comprado nos seguintes locais:\n* [City Cars](address: 76 3s)", "help_taxes_pay_item_content": "A Receita Federal cobra impostos anuais com base na renda que você ganhou naquele ano com negócios e aluguel, menos quaisquer despesas dedutíveis de impostos.\n\nOs impostos devem ser pagos no final de cada ano (60 dias, por padrão), desde que você tenha ganhado dinheiro suficiente para que eles percebam você.\n\nVocê receberá uma mensagem de texto da Receita Federal quando seus impostos estiverem prontos e terá 20 dias para pagá-los.\n\nSe você não pagar seus impostos a tempo, a Receita Federal começará a confiscar seus bens (estoque, veículos, móveis) até que seu saldo seja coberto.\n\nSeus impostos podem ser pagos indo para o seguinte local:\n* [Receita Federal](address: 70 5a)", "common_movement": "Controle básico de personagem", "common_bizphone": "BizPhone", "common_watchtv": "Assistir à TV", "common_playcomputer": "<PERSON><PERSON> jogos de computador", "common_exercise": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "help_common_movement_content": "**Movimento**\n\n* <PERSON><PERSON> padr<PERSON>, seu personagem é controlado usando ponto e clique com o mouse, e os veículos são controlados com o teclado, mas você pode ajustar essas opções nas configurações.\n* Mantenha a tecla Shift pressionada para alternar entre correr e andar.\n* Pressione o botão do meio do mouse para alternar o modo de corrida automática.\n\n**Interagindo com Objetos**\n\n* Para interagir com um objeto, clique com o botão esquerdo nele.\n* Para pegar um objeto que você colocou, clique com o botão direito no objeto. Em seguida, clique com o botão direito (ou Shift + clique com o botão direito) para girá-lo e clique com o botão esquerdo para colocá-lo novamente no chão.\n\n**Pausando o Jogo**\n\n* Pressione a tecla de espaço para pausar ou despausar o jogo.\n\n**Se você ficar preso**\n\n* Se estiver em um veículo, chame o Caminhão de Reboque pelo aplicativo Contatos do seu [BizPhone](general-bizphone)\n* Se estiver a pé, clique em Esc > Opções > Outros > Liberar", "help_common_watchtv_content": "**Assistir TV** para aumentar a felicidade. Você pode usar:\n* [TV ELGE 8467NW-43283833 43\"](furniture-wallmountedtv)\n* [TV P&S Sympathy 88\"](furniture-expensivetv)", "help_common_playcomputer_content": "**Jogar no Computador** para aumentar a felicidade. Você pode usar:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n* [Setup de Jogos Scorpio](furniture-scorpiogamingsetup)", "help_common_exercise_content": "**Exercício** para ganhar felicidade. Você pode se exercitar usando [Máquinas de treino](furniture-itemgroupworkoutmachine):\n* Em qualquer [academia](businesstypes-gym)\n* Em casa\n\n**Plano de condicionamento físico personalizado**\n\nUm plano de condicionamento físico diário e pessoal é criado por um [Treinador de academia](skill-gymtrainer) quando você clica em um [Quadro de planejamento de condicionamento físico](furniture-fitnessplanningboard) em uma academia.\n\nA cada dia, o plano é diferente e é baseado no equipamento disponível naquela academia. Se você concluir todas as tarefas com sucesso antes de sair, você receberá um aumento extra de felicidade!", "help_common_bizphone_content": "Seu **BizPhone** permite que você conduza negócios rapidamente de onde estiver.\n\nAplicativos do BizPhone:\n\n* **Pessoa** - Confira suas estatísticas e metas pessoais.\n* **Contatos** - Ligue para empresas com as quais você já interagiu, como o banco ou agência de empregos!\n* [**Rivais**](rivals-overview) - Monitore o desempenho dos seus rivais e acompanhe quaisquer ataques/rivalidades contra você.\n* [**Meus Funcionários**](skill-myemployees) - Gerencie contratações, atribuições, treinamentos e satisfação dos funcionários\n* **BizMan** - Gerencie os horários, uniformes, preços e marketing de seus negócios.\n* [**Econoview**](finance-econoview) - Visualize seus Empréstimos, Investimentos, Últimas Transações e Demonstrativos de Resultados.\n* **Voogle Maps** - Tenha uma visão geral da cidade, use filtros de mapa para destacar locais e definir rotas.\n* **MarketInsider** - Tenha uma visão detalhada da concorrência, demanda, índice de preços de importação e eventos de mercado.", "common_finance": "Finança", "finance_banks": "Empréstimos/Investimentos", "finance_negativeinterest": "Juros negativos", "finance_taxes": "Receita Federal / Impostos", "help_finance_banks_content": "**Bancos** podem oferecer empréstimos ou oportunidades de investimento. Gerencie seus empréstimos e investimentos no aplicativo [Econoview](finance-econoview) em seu [BizPhone](general-bizphone).\n\n**Empréstimos**\n\nOs bancos podem estar dispostos a emprestar dinheiro se você estiver ganhando uma renda estável e não estiver pedindo emprestado demais daquele banco.\n\nAlém de pagar o empréstimo, você também será cobrado uma taxa de juros diária, com base no valor do empréstimo.\n\n**Investimentos**\n\nCada opção de investimento tem uma faixa de ganhos possíveis que flutua entre cada dia.\n* Investimentos de Baixo Risco quase sempre renderão dinheiro, mas eles renderão muito lentamente.\n* Investimentos de Alto Risco podem potencialmente ganhar dinheiro mais rápido, mas também têm o risco de perder dinheiro.\n\nVisite ou ligue para qualquer um dos bancos:\n* [Jensen Capital](address: 17 4a)\n* [Vantander Bank](address: 6 2a)", "help_finance_negativeinterest_content": "**Juros Negativos** é uma taxa semanal que o banco cobra se você acumular muito dinheiro no banco em vez de gastá-lo ou investi-lo.\n\nToda segunda-feira de manhã, logo após a meia-noite, você será cobrado uma taxa com base no saldo atual da sua conta bancária. Isso não está relacionado a empréstimos ou investimentos que você possa ter.\n\nPara minimizar essa taxa, gaste seu dinheiro em:\n* Seus Negócios\n* [Investimentos](finance-banks)\n* Imóveis\n* Itens de Luxo", "help_finance_taxes_content": "O **IR** cobra impostos anuais com base na renda que você obteve naquele ano com negócios e aluguel, menos quaisquer despesas dedutíveis de impostos.\n\nOs impostos vencem no final de cada ano (60 dias, por padrão), desde que você ganhe dinheiro suficiente para que eles o notem.\n\nVocê vai receber uma mensagem de texto do IR quando seus impostos estiverem prontos e você terá 20 dias para pagar.\n\nSe você não pagar seus impostos a tempo, o IRS vai apreender seus objetos de valor (estoque, veículos, móveis) até que seu saldo seja coberto.\n\nSeus impostos podem ser pagos indo para o seguinte local:\n* [IRS](morada: 70 5a)", "help_businesstype_fruitandvegetablestore_content": "**Quitandas** são empresas varejistas. \n\nOs clientes servem a si mesmos.\n\nPara operar, a empresa precisa dos seguintes equipamentos:\n\n* [Pilha de cestas de compras](furniture-stackofshoppingbaskets)\n* [Balança digital](furniture-standingdigitalscale)\n* [Ponto de vendas](furniture-itemgrouppointofsale)\n* Pelo menos um produto à venda (veja abaixo)\n\nEmpresas desse tipo vendem principalmente:\n\n* [Ma<PERSON><PERSON>](products-apple)\n* [Banana](products-banana)\n* [Cenoura](products-carrot)\n* [<PERSON>ce](products-lettuce)\n* [Pêra](products-pear)\n* [<PERSON><PERSON>](products-tomato)\n\nAlém disso, podem vender:\n\n* [Salada](products-salad)\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_graphicdesigner_content": "**Designers Gráficos** são negócios baseados em escritórios.\n\nOs clientes são atendidos digitalmente e não estão fisicamente presentes nos prédios.\n\nO negócio requer os seguintes móveis para funcionar:\n\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n\nNegócios desse tipo podem vender:\n\n* [Taxa de Designer Grá<PERSON>](fees-hourlygraphicdesignerfee)\n\nFuncionários com as seguintes habilidades podem ser designados:\n\n* [Designer <PERSON><PERSON><PERSON><PERSON><PERSON>](skill-graphicdesigner)\n* [Limpeza](skill-cleaning)", "help_businesstype_warehouse_content": "**Depósitos** são usados principalmente para armazenar o estoque importado de seu[Agente de compra](skill-purchasingagent).\n\nEles são gerenciados por seu [Gerente de logística](skill-logisticsmanager).\n\nO negócio exige pelo menos um veículo para funcionar. Pode ser qualquer veículo, embora veículos maiores sejam mais eficazes.\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Motorista de entrega](skill-deliverydriver)", "help_itemname_apple_content": "**Maçã** é um tipo de produto vendido principalmente em [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nAlém disso, pode ser vendido em [Cafeterias](businesstypes-coffeeshop) e [Restaurantes de Fast Food](businesstypes-fastfoodrestaurant) e [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Caixa de Produtos de Madeira](furniture-woodenproductcrate)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Total Produce Trading](address:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_banana_content": "**Banana** é um tipo de produto vendido principalmente em [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nAlém disso, pode ser vendido em [Cafeterias](businesstypes-coffeeshop) e [Restaurantes de Fast Food](businesstypes-fastfoodrestaurant) e [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Caixa de Produtos de Madeira](furniture-woodenproductcrate)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Total Produce Trading](address:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_carrot_content": "**Cenoura** é um tipo de produto vendido principalmente em [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nAlém disso, pode ser vendido em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Caixa de Produtos de Madeira](furniture-woodenproductcrate)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Total Produce Trading](address:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_lettuce_content": "**Sacola de Alface** é um tipo de produto majoritariamente vendido em [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nTambém pode ser vendido em [Supermercados](businesstypes-supermarket).\n\n**Nota:** Uma [Balança Digital de Piso](furniture-standingdigitalscale) é necessária para vender Frutas e Vegetais\n\nO produto pode ser colocado nas seguintes mobílias:\n* [Caixa de Produto de Madeira](furniture-woodenproductcrate)\n\nO produto pode ser comprado nas seguintes localizações:\n* [Comércio Total de Produtos](address:6 6a)\n\nO produto pode ser importado nas seguintes localizações:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_pear_content": "**Pera** é um tipo de produto vendido principalmente em [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nAlém disso, pode ser vendido em [Supermercados](businesstypes-supermarket).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Caixa de Produtos de Madeira](furniture-woodenproductcrate)\n\nO produto pode ser adquirido nos seguintes locais:\n* [Total Produce Trading](address:6 6a)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_tomato_content": "**Saco de Tomates** é um tipo de produto vendido principalmente nas [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore).\n\nAdicionalmente, pode ser, também, vendido a partir de [Supermercados](businesstypes-supermarket).\n\n**Nota:** Uma [Balança Digital de Piso](furniture-standingdigitalscale) é necessária para vender Frutas e Vegetais.\n\nO produto pode ser colocado nas seguintes mobílias:\n* [Caixa de Produto de Madeira](furniture-woodenproductcrate)\n\nO produto pode ser comprado nas seguintes localizações:\n* [Comércio Total de Produtos](address:6 6a)\n\nO produto pode ser importado nas seguintes localizações:\n* [SeaSide Internationais](address: 2 pier)\n* [Envios de Maré Lunar](address: 6 pier)", "itemname_cabinet": "Armário / Balcão", "furniture_restaurantbooth": "Mesa de restaurante", "furniture_diningtable": "Mesa de jantar", "help_itemname_angledwoodendisplaystand_content": "**Expositor angu<PERSON> de madeira** pode ser usado para armazenar os seguintes itens:\n* [Caixa de produtos de madeira](furniture-woodenproductcrate)\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_cabinet_content": "**Armários/Balcões** têm uma variedade de estilos:\n\n* Balcão com Vidro\n* Balcão com Vidro (Canto)\n* Armário com Portas\n* Armário com Gavetas\n* Armário de Canto\n* Extremidade do Armário\n* Armário Moderno\n* Armário <PERSON>o (Canto)\n\nOs Balcões com Vidro podem ser usados para acomodar os seguintes itens:\n* [Caixa Registradora](furniture-cashregister)\n\nOs Balcões com Vidro podem ser adquiridos nos seguintes locais:\n* [Lux Concept](address:68 5a)\n\nOs Armários podem ser usados para acomodar os seguintes itens:\n\n* [Caixa Registradora](furniture-cashregister)\n* [Churrasqueira de Cachorro-Quente](furniture-hotdoggrill)\n* [Máquina de Café Industrial](furniture-industrialcoffeemachine)\n* [Churrasqueira Industrial](furniture-industrialgrill)\n* [Balança Digital](furniture-standingdigitalscale)\n\nOs Armários podem ser adquiridos em Nos seguintes locais:\n* [Ika Bohag](address:50 4s)\n\nO Armário com Gavetas também pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)\n* [AJ Pederson & Son](address:13 5a)", "help_furniture_diningtable_content": "**Mesa de jantar** pode ser usada para acomodar clientes durante refeições.\n\nÉ possível acomodar 1 cliente por cadeira.\n\nÉ preciso ter cadeiras e uma Mesa padrão\n\nHá tipos diferentes de cadeira: \n* Cadeira de escritório\n* Cadeira de escritório de malha\n* Cadeira comum\n* Cadeira multiuso\n* Poltrona de verão\n* Poltrona antiga\n* Cadeira gamer\n\nÉ possível comprar os equipamentos nestes locais: \n* [Ika Bohag](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:11 1a)\n* [Lux Concept](address:68 5a)", "help_itemname_flatwoodendisplaystand_content": "**Expositor (Plano em Madeira)** pode ser usado para armazenar até dois dos seguintes itens:\n* [Caixa para Produtos em Madeira](furniture-woodenproductcrate)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_graphictablet_content": "**Tablet Gráfico** pode ser adicionado a uma [Estação de Trabalho de Computador](furniture-computerworkstation) para satisfazer a \"Tablet Gráfico\" [Demanda de Equipamentos de Funcionários](employees-demands-equipment).\n\nOs móveis podem ser comprados nos seguintes locais: \n* [Ika Bohag](endereço:50 4s)\n* [Mr. <PERSON>'s Office Supplies](endereço:39 4a)", "help_itemname_graphictabletwithscreen_content": "**Tablet gráfico (com tela)** pode ser adicionado a um [Computador](furniture-computerworkstation) para satisfazer a \"Tablet gráfico com tela\" [Demanda de equipamento do funcionário](employees-demands-equipment) \n\nO mobiliário pode ser comprado nos seguintes locais: \n* [<PERSON><PERSON>g](address:50 4s)\n* [Suprimentos de Escritório do Sr. Scott](address:39 4a)", "help_itemname_standingdigitalscale_content": "**Balança Digital** é necessária em todas as [Lojas de Frutas e Vegetais](businesstypes-fruitandvegetablestore)\n\n**Capacidade do cliente:** 15\n\n**Requer:** [Armário](móveis-armário)\n\nO móvel pode ser adquirido no seguinte local:\n* [Eletrodomésticos Quadrados](endereço:16 4a)", "help_furniture_loudspeaker_content": "**Alto-falante** pode ser usado para tocar música em lojas e apartamentos. Há diversos tamanhos, desde os pequenos, que cabem numa mesa, até os grandes e luxuosos.\n\nÉ possível comprar o equipamento nestes locais:\n* [<PERSON><PERSON>](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_mousepad_content": "**Mouse Pad** pode ser adicionado a uma [Estação de Trabalho de Computador](furniture-computerworkstation) para satisfazer a \"Demanda de Equipamento de Funcionário\" por \"Mouse Pad\".\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Suprimentos de Escritório do Sr. Scott](address:39 4a)\n* [<PERSON><PERSON>](address:50 4s)", "help_furniture_restaurantbooth_content": "**Sofá de Restaurante** pode ser usado para acomodar os clientes enquanto eles comem.\n\n4 clientes podem se sentar por sofá.\n\nO móvel pode ser adquirido no seguinte local:\n* [AJ <PERSON>rson & Son](endereço: 13 5a)", "help_itemname_woodenproductcrate_content": "**Caixa para Produtos em Madeira** pode ser usado para vender:\n\n* [<PERSON><PERSON><PERSON>](products-apple)\n* [Banana](products-banana)\n* [<PERSON>noura](products-carrot)\n* [<PERSON><PERSON>](products-lettuce)\n* [Pera](products-pear)\n* [Tomate](products-tomato)\n\n**Capacidade de Produtos:**\n* Alface: 25\n* Demais: 50\n**Capacidade de Clientes:** 15\n\n**Requer:** [Expositor (Angular em Madeira)](furniture-angledwoodendisplaystand) ou [Expositor (Plano em Madeira)](furniture-flatwoodendisplaystand)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "customers_title": "Clientes", "customers_demands": "Demandas dos clientes", "customers_traffic": "Tráfego/Marketing", "customers_buildinglimit": "Limite de construção", "help_customers_speaker_content": "**Alto-falante** é necessário para tocar música.\n\nVocê pode usar:\n* Qualquer alto-falante JayBeeel\n* P&S Pelolab 90\n\nEle pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](endereço: 50 4s)\n* [<PERSON><PERSON><PERSON>](endereço:68 5a)", "help_customers_traffic_content": "Quando você combina **Índice de Tráfego e Marketing**, obtém a promoção geral de sua empresa. Você deseja atingir 100% de promoção para maximizar seus clientes em potencial.\n\nO Índice de Tráfego não pode ser alterado, por isso é importante escolher um prédio com alto índice na hora de alugar um prédio para seu novo negócio. Usando seus aplicativos [Bizphone](common_bizphone), você pode ver o índice de tráfego do prédio no BizMan ou no Voogle Maps\n\nO marketing pode ser usado para complementar seu índice de tráfego para aumentar sua promoção. Diferentes tamanhos de edifícios exigirão quantidades diferentes de marketing, portanto, você precisará encontrar a combinação certa de marketing para cada negócio.\n\nVocê pode obter campanhas de marketing nos seguintes locais:\n* [McCain's eMarketing](endereço: 17 3a)\n* [CityAds](endereço: 5 2a)", "help_skillname_graphicdesigner_content": "Funcionários com habilidade de **Designer Gráfico** são usados para designers gráficos.\n\nOs Designers Gráficos trabalham com clientes digitais para ganhar a [Taxa Horária do Designer Gráfico](fees-hourlygraphicdesignerfee).\n\nO nível de habilidade (%) define o quanto os clientes estão dispostos a pagar pelos serviços.\n\nEles podem ser designados para:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n\nEles podem ser contratados em:\n* [City Workforce Inc.](address: 41 4a)", "importers_overview": "Visão geral de importação", "importertypename_jetcargo": "Importações Jet Cargo", "importertypename_seaside": "Seaside Internationals", "importertypename_unitedocean": "Importação United Ocean", "importertypename_bluestone": "Bluestone Imports", "help_importers_overview_content": "**Importando para seu armazém**\n\nSeu [Representante de Compras](skill-purchasingagent) gerencia pedidos de estoque no app BizMan do seu [BizPhone](general-bizphone). O estoque que você faz o pedido depende do seu [contrato de importação](importers-contract).\n\n**Escolha os tipos de entrega**\n\n*Única Vez:*\nO representante entregará a quantidade inserida, independentemente da quantidade de estoque existente no armazém.\n\n*Estoque automático:*\nO representante ajustará o valor da entrega para garantir que o estoque total do armazém seja igual ao valor alvo. Se a meta já for atingida, nenhum produto será entregue.\n\n* *Examplo:* Se você definir uma quantidade de 500 hambúrgeres e seu armazém possui 300 hambúrgeres seu Representante de Compras irá fazer o pedido de mais 200 unidades totalizando os 500.\n\nVocê pode selecionar a frequência que deseja o estoque automático: a cada 1, 3, or 7 dias.\n\n**Garanta Entregas Bem-Sucedidas:**\n\nPara receber com sucesso pedidos de importação em seus armazéns às 02:00 (2 AM):\n* Cada armazém designado precisa de pelo menos uma  [Prateleira de Paletes](furniture-palletshelf) com espaço livre suficiente para receber o estoque\n* Seu pedido total precisa exceder o valor de importação mínima de compra\n* Você precisa ter a quantidade de dinheiro para pagar pelo pedido.", "help_importertypename_jetcargo_content": "**Jet Cargo Imports** é um local de importação.\n\nEles importam os seguintes itens:\n* [Telefone Arty Fish](products-smartphone1)\n* [Smartwatch Arty Fish](products-smartwatch2)\n* [Flor (Barata)](products-cheapflower)\n* [Presente (Barato)](products-cheapgift)\n* [<PERSON>du<PERSON>](products-haircareproduct)\n* [Joia (Barata)](products-cheapjewelry)\n* [Fones de Ouvido Noize Boss](products-earbuds01)\n* [Saco de Papel](products-paperbag)\n* [Rhythm By Tre](products-headphones01)\n* [Lata de Refrigerante](products-sodacan)\n* [Guarda-chuva](products-umbrella)\n* [Telefone ZanaMan](products-smartphone2)\n* [Smartwatch ZanaMan](products-smartwatch1)\n\nEles estão localizados em:\n* [Jet Cargo Imports](address: 1 pier)", "help_importertypename_seaside_content": "**Seaside Internationals** é um local de importação.\n\nEles importam os itens a seguir:\n* [<PERSON><PERSON><PERSON>](products-apple)\n* [Banana](products-banana)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](products-burger)\n* [<PERSON><PERSON><PERSON>](products-carrot)\n* [Croissant](products-croissant)\n* [Xícara de café](products-cupofcoffee)\n* [Cupcake](products-cupcake)\n* [Donut](products-donut)\n* [Batatas fritas](products-frenchfries)\n* [Alimentos frescos](products-freshfood)\n* [Alimentos congelados](products-frozenfood)\n* [Cachorro-quente](products-hotdog)\n* [Sorvete](products-icecream)\n* [Espeto](products-kabob)\n* [<PERSON>ce](products-lettuce)\n* [Saco de papel](products-paperbag)\n* [Pêra](products-pear)\n* [Pizza](products-pizza)\n* [Salada](products-salad)\n* [Lata de refrigerante](products-sodacan)\n* [Tomate](products-tomato)\n\nEles estão localizados aqui:\n* [Seaside Internationals](address: 2 pier)", "help_importertypename_unitedocean_content": "**United Ocean Import** é um local de importação.\n\nEles importam os seguintes itens:\n* [Cerveja](products-beer)\n* [Garrafa de Vinho](products-bottleofwine)\n* [<PERSON>lor Barata](products-cheapflower)\n* [Presente <PERSON>ato](products-cheapgift)\n* [<PERSON><PERSON>](products-cheapjewelry)\n* [<PERSON><PERSON><PERSON>](products-cigar)\n* [Flor Cara](products-expensiveflower)\n* [Presente Caro](products-expensivegift)\n* [Joia Cara](products-expensivejewelry)\n* [Margarita](products-margarita)\n* [Martini](products-martini)\n* [Whisky](products-whisky)\n\nEles estão localizados aqui:\n* [United Ocean Import](address: 3 pier)", "help_importertypename_bluestone_content": "**BlueStone Importações** is an importer location.\n\nThey import the following items:\n* [<PERSON><PERSON><PERSON> (Feminino Clássico e Barato)](products-classiccheapfemaleclothing)\n* [<PERSON><PERSON>pas (Masculino Clássico e Barato)](products-classiccheapmaleclothing)\n* [<PERSON><PERSON>pas (Feminino Clássico e Caro)](products-classicexpensivefemaleclothing)\n* [<PERSON><PERSON><PERSON> (Masculino Clássico e Caro)](products-classicexpensivemaleclothing)\n* [<PERSON><PERSON><PERSON> (Feminino Moderno e Barato)](products-moderncheapfemaleclothing)\n* [<PERSON><PERSON>pas (Masculino Moderno e Barato)](products-moderncheapmaleclothing)\n* [Roupas (Feminino Moderno e Caro)](products-modernexpensivefemaleclothing)\n* [Roupas (Masculino Moderno e Caro)](products-modernexpensivemaleclothing)\n* [Livro Edição Limitada](products-limitededitionbook)\n* [Livro Motivacional](products-motivationalbook)\n* [Romance](products-novel)\n* [Livro Ilustrado](products-picturebook)\n* [Livro Técnico](products-technicalmanual)\n* [Quadrinhos](products-youngnovel)\n\nEles estão localizados aqui:\n* [BlueStone Importações](address: 4 pier)", "vehicles_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "store_page_description_long": "Seu saldo bancário é insignificante, mas suas ambições são enormes. \n\nCom uma ajudinha do seu tio, você consegue um apartamento e seu primeiro emprego. Ma<PERSON>, depois, cabe a você conquistar o sucesso e dominar a cidade de Nova York do jeito que desejar. \n\nConstrua lentamente várias empresas pequenas, transforme-as em grandes corporações ou crie seu próprio caminho. As possibilidades são ilimitadas neste vasto ambiente de negócios. Você nunca jogou nada igual!\n\nVá de um apartamento pequeno e uso do transporte público a suítes luxuosas, carros velozes e móveis de design. Aplique seu dinheiro em fundos de investimento e imóveis. Conquiste o sucesso, amargue o fracasso, reconstrua. Mas não esqueça de se cuidar, dormir e ficar feliz na sua ascensão ao topo. \n\nVocê tem as habilidades necessárias para construir seu próprio império?\"", "store_page_description_short": "Big Ambitions é um simulador de negócios revolucionário. Saia do nada até se tornar o maior empreendedor de Nova York, abrindo pequenos negócios ou construindo lentamente grandes corporações como quiser.\"", "store_page_paragraph_1": "Saia do nada até se tornar o magnata mais importante de Nova York. Comece um negócio, trabalhe de forma inteligente, faça o negócio prosperar e aproveite sua vida. Em qual ramo você quer ser bem sucedido? Até onde você consegue ir? Quanto tempo você consegue manter isso? \"", "store_page_paragraph_2": "Comece sua própria loja de presentes, supermercado, cafeteria, escritório de advocacia, loja de roupas, loja de bebidas, florista e muito mais. Você decide o que é sucesso: seja uma única loja, uma cadeia de restaurantes fast-food, uma grande corporação ou ganhar milhões desenvolvendo sites em um porão. \"", "store_page_paragraph_3": "Faça um empréstimo, alugue um edifício, crie o logotipo, reforme a loja, faça um estoque, administre seu dinheiro, contrate funcionários e construa sua infraestrutura. Logo você terá que começar a importar produtos do porto e distribuí-los por uma rede de armazéns. \"", "store_page_paragraph_4": "Um negócio maior significa ter gerentes. Contrate gerentes de RH, gerentes de Logística e representantes de Compras. Todos eles precisam de mesas e computadores, então você vai precisar de uma sede chique.  \"", "store_page_paragraph_5": "Precisa de um caminhão de entrega? Vá até um revendedor e compre. Precisa de comida? Vá ao supermercado, mas certifique-se de ter uma geladeira em casa. Big Ambitions é um simulador da vida real onde seu personagem tem que interagir fisicamente com o mundo para sobreviver e ter sucesso. \"", "store_page_paragraph_6": "Entre e compre qualquer edifício em Nova York. Vá de um pequeno apartamento até uma cobertura dos sonhos em Midtown, uma luxuosa casa num condomínio em Hell's Kitchen ou uma série de arranha-céus. Coloque móveis em cada propriedade de acordo com seu estilo e posição. Invista seu dinheiro suado em imóveis para aumentar seu império. Domine a cidade pedaço por pedaço. \"", "store_page_paragraph_7": "Gaste sua riqueza como quiser. Compre um grande SUV, um carro esportivo veloz ou o novo e luxuoso Mersaidi S500. Talvez arrisque tudo nos cassinos ou coloque seu dinheiro em fundos de investimento e colha os frutos.", "store_page_paragraph_8": "Conforme vai crescendo, você precisa se preocupar sempre em dormir o suficiente, bem como ter saúde e felicidade. Aproveite a vida, pois ao envelhecer, você descobrirá que dinheiro não consegue comprar tempo... ou será que consegue?\"", "store_page_paragraph_9": "Big Ambitions reúne tudo o que você curte no 'Startup Company', o jogo que viralizou e tornou-se uma grande sensação, e leva a um patamar ainda mais alto.", "itemname_angledwoodendisplaystand": "Expositor an<PERSON><PERSON>", "itemname_flatwoodendisplaystand": "Estande de exposição de madeira plana", "itemname_standingdigitalscale": "Balança Digital", "itemname_woodenproductcrate": "Caixa de madeira para produtos", "itemname_apple": "Caixa de Maçãs", "itemname_pear": "Caixa de Peras", "itemname_banana": "Caixa de Bananas", "itemname_carrot": "Caixa de Cenouras", "itemname_lettuce": "Caixa de Alface", "itemname_tomato": "Caixa de Tomates", "itemname_laptop": "Notebook", "itemname_mousepad": "MousePad", "itemname_graphictablet": "Mesa digitalizadora", "itemname_graphictabletwithscreen": "Mesa digitalizadora com tela", "itemname_shopentranceleft": "Abertura da entrada pela esquerda", "itemname_shopentranceright": "Abertura da entrada pela direita", "itemname_kettlebell1kg": "Kettlebell 1 kg", "itemname_kettlebell2kg": "Ke<PERSON>bell 2 kg", "itemname_kettlebell5kg": "<PERSON><PERSON><PERSON> 5 kg", "itemname_kettlebell10kg": "<PERSON><PERSON><PERSON> 10 kg", "itemname_kettlebell15kg": "<PERSON><PERSON>bell 15 kg", "itemname_kettlebell25kg": "Kettlebell 25 kg", "itemname_pullupbar": "Barra de exercício", "businesstype_fruitandvegetablestore": "<PERSON><PERSON><PERSON>", "business_description_wholesalestore_3": "Todo o tipo de produtos frescos, da fazenda para o seu negócio", "businesstype_graphicdesigner": "Designer <PERSON><PERSON><PERSON><PERSON><PERSON>", "skillname_graphicdesigner": "Designer <PERSON><PERSON><PERSON><PERSON><PERSON>", "itemname_hourlygraphicdesignerfee": "Taxa por hora do designer grá<PERSON>o", "job_demand_priority_low": "Legal de ter", "job_demand_priority_medium": "Importante", "job_demand_priority_high": "Fundamental", "jobdemand_hasmousepad": "MousePad", "jobdemand_hasgraphictablet": "Mesa digitalizadora", "jobdemand_hasgraphictabletwithscreen": "Mesa digitalizadora com tela", "jobdemand_hasmousepad_description": "Funcionário exige ter um MousePad na mesa", "jobdemand_hasgraphictablet_description": "Funcionário exige ter uma Mesa digitalizadora em sua mesa", "jobdemand_hasgraphictabletwithscreen_description": "Funcionário exige ter uma Mesa digitalizadora com tela em sua mesa", "bizman_purchasingagents_managed_by": "Gerenciado por {purchasingAgent}", "bizman_purchasingagents_total": "Total:", "bizman_purchasingagents_one_time_delivery_explanation": "O representante entregará o valor informado, independente da quantidade em estoque existente no armazém.", "bizman_purchasingagents_repeating_delivery_explanation": "O agente garante que o valor inserido seja o total disponível no depósito. <b>Se a meta já for atingida, nenhum produto será entregue.</b>", "business_description_car_dealership_3": "Velocidade. Potência. Percorra a sua cidade com estilo com os nossos veículos de luxo", "tutorial_interior_designer_objective_1": "Melhore sua pontuação de paredes e piso para 20%", "uncle_fred_tutorial_interior_designer": "<PERSON>ito bem! Ma<PERSON> isso não é apenas o suficiente para fazer eles felizes! Como você pode perceber, eles acham que suas paredes e pisos são feios. Eu meio que concordo.", "uncle_fred_tutorial_improve_employee": "<PERSON><PERSON>, de volta aos seus clientes. Seus clientes sempre reclamando, certo? haha. <PERSON><PERSON><PERSON>, não se preocupe. Eventualmente, eles ficam felizes e sua conta bancária também. Nós temos dois problemas: o nível de atendimento ao cliente de seu funcionário e a falta de uniforme!", "tutorial_improve_employee_objective_1": "Treine seu funcionário para aumentar o nível de atendimento ao cliente em 50% usando o aplicativo Meus Funcionarios", "tutorial_improve_employee_objective_2": "Defina um uniforme para todos os funcionários do seu negócio na página Configurações do aplicativo BizMan", "tutorial_12_objective_2": "Abra o app MarketInsider e ordene a lista por \"Exigência\" para encontrar o produto mais procurado", "uncle_fred_tutorial_delivery_contract": "Espero que esteja se divertindo, hein? É disso que se trata! E aproveitando, acho que você está começando a ficar entediado de empilhar as prateleiras em suas lojas manualmente, né? Be<PERSON>, você deve visitar o gerente em uma das lojas de atacado. Tenho certeza que eles podem fornecer entregas também!", "tutorial_delivery_contract_objective_1": "Compre uma estante de depósito", "interior_designer_score": "Pontuação: {percentage}%", "dialog_health_insurance_manager_npc_name": "Gerente de Plano de Saúde", "dialog_health_insurance_manager_start": "<PERSON><PERSON><PERSON>. Como posso te ajudar?", "dialog_health_insurance_manager_no_hr_managers": "Precisamos de pelo menos um Gerente de RH disponível para começarmos uma parceria.", "dialog_health_insurance_manager_new_partnership": "Começar uma nova parceria", "dialog_health_insurance_partnership_header": "Parceria de plano de saúde", "dialog_health_insurance_manager_select_hr_manager": "Selecionar Gerente de RH", "dialog_health_insurance_manager_select_plan_type": "Selecionar Tipo de plano", "healthinsurancemanagerdialog_notification_select_manager": "Por favor, selecione um Gerente de RH", "healthinsurancemanagerdialog_notification_select_plan_type": "Por favor, selecione um Tipo de plano", "dialog_health_insurance_manager_on_partnership_settings_set_player": "Gostaria de iniciar uma parceria com nosso gerente de RH {selectedEmployee} para o plano {healthPlanType}", "dialog_health_insurance_manager_on_partnership_settings_set_manager": "Perfeito. Vamos prepara a parceria e enviar os detalhes da oferta o mais rápido possível. Espere uma resposta em um ou dois dias.", "dialog_health_insurance_partnership_hr_manager": "<PERSON><PERSON><PERSON>", "dialog_health_insurance_manager_hr_manager_not_found": "Não foi possível entrar em contato com o gerente de RH que você selecionou. Não podemos te enviar uma oferta.", "dialog_health_insurance_manager_initial_offer": "Olá. Após algumas discussões internas, esta é a melhor oferta que conseguimos elaborar. Note que temos um requisito mínimo de 10 clientes para os nossos serviços. Portanto, mesmo que seu gerente esteja gerenciando menos de 10 funcionários, ainda assim cobraremos por 10.", "hospital_health_insurance_manager": "Gerente de Planos de Saúde", "hospital_health_insurance_manager_description": "Garantindo que seus funcionários tenham o melhor Plano de saúde do mercado", "dialog_health_insurance_plan_offer_price": "Preço por dia por funcionário: <b>{price}</b>", "dialog_negotiate_button": "Negociar", "dialog_send_offer_button": "Enviar of<PERSON>a", "healthinsuranceplantype_bronze": "Bronze", "healthinsuranceplantype_silver": "Prata", "healthinsuranceplantype_gold": "Ouro", "dialog_health_insurance_negotiation_start": "Olá. Parece que você não gostou da oferta. O que você propõe?", "dialog_health_insurance_player_offer": "Eu proponho {amount} por dia, por funcionário.", "dialog_health_insurance_declined_player_offer": "<PERSON><PERSON> muito, mas não estamos interessados.", "dialog_health_insurance_accepted_player_offer": "Temos um acordo!", "dialog_player_offer_input": "<PERSON><PERSON>a", "dialog_negotiation_impossible_1": "Estamos muito distantes um do outro nesta negociação.", "dialog_negotiation_impossible_2": "Esta oferta não faz o menor sentido. O que você acha que somos?", "dialog_negotiation_bad_1": "Não sei bem se estamos chegando a algum lugar aqui...", "dialog_negotiation_bad_2": "Você pode subestimar o serviço que estamos prestando. Esta oferta não é realista.", "dialog_negotiation_acceptable_1": "Ah, hmm, interessante.", "dialog_negotiation_acceptable_2": "Gostei. Mas não é perfeita.", "common_accepted": "<PERSON><PERSON>", "common_declined": "<PERSON><PERSON><PERSON>", "uniform_customizer_create_new_preset": "Criar uma nova predefinição", "notifications_needs_wooden_product_case": "Este produto precisa ser colocado em uma Caixa de madeira para produtos", "dialog_health_insurance_partnership_employee_skill": "Habilidade do gerente de RH", "dialog_health_insurance_partnership_plan_type": "Tipo de plano", "jobdemand_bronzehealthinsurance": "Plano de Saúde Bronze", "jobdemand_silverhealthinsurance": "Plano de Saúde Prata", "jobdemand_goldhealthinsurance": "Plano de Saúde Ouro", "bizman_hrmanager_health_insurance_label": "Plano de saúde para este gerente:", "bizman_hrmanager_health_insurance_info": "{planType} ({pricePerDayAndEmployee} por dia, por funcionário, mínimo de {minimumEmployees} funcionários)", "bizman_hrmanager_no_health_insurance_info": "Entre em contato com o Hospital para preparar um plano para este Gerente de RH", "bizman_hrmanager_cancel_insurance_button": "Cancelar plano de saúde", "bizman_hrmanager_cancel_insurance_confirm": "Quer mesmo cancelar o Plano de saúde atual deste Gerente de RH?", "transactiontype_healthinsurance_label": "Plano de saúde", "transactiontype_healthinsurance": "{healthInsurancePlanType} Plano de Saúde ({employee}) - {numberOfEmployees} Funcionários", "educationdoorcontroller_closed": "A Escola está fechada no momento. Todos os cursos foram encerrados. Volte depois.", "workout_machine_already_in_use": "Esta máquina já está sendo usada.", "menu_options_local_radio_songs": "Músicas da Rádio Local", "menu_options_open_folder": "<PERSON><PERSON><PERSON>", "menu_options_song_files_found": "arquivos encontrados: {count}", "businessrequirement_scale": "Balança digital", "workout_machine_gym_need_sport_clothes": "Você precisa de roupas esportivas para usar este aparelho. Troque de roupa no vestiário", "bizman_temporarily_closed": "Te<PERSON><PERSON><PERSON><PERSON> fechado", "npc_expression_no_scales": "Ihh, como eu faço para medir o peso disso?", "dialog_health_insurance_manager_more_help_offered": "Posso te ajudar em alguma outra coisa?", "jobdemand_bronzehealthinsurance_description": "Ser designado a um Gerente de RH com uma parceria de Plano de saúde nível Bronze (ou superior)", "jobdemand_silverhealthinsurance_description": "Ser atribuído a um gerente de RH com uma parceria de plano de saúde Prata (ou superior)", "jobdemand_goldhealthinsurance_description": "Ser atribuído a um gerente de RH com uma parceria de Plano de Saúde Ouro", "gymentrance_notification_cant_enter_with_vehicle_or_item": "Você não pode entrar na academia usando um veículo ou com um item nas mãos", "workout_machine_cant_use_with_vehicle_or_item": "Para usar este aparelho, você precisa estar com as mãos vazias", "itempanelui_notification_cant_grab_while_in_activity": "Não é possível pegar um item enquanto faz uma atividade", "tutorial_improve_employee_objective_3": "Coloque o seu funcionário na sua loja de presentes", "tutorial_13_objective_7": "Abra o novo negócio desativando \"Temporariamente fechado\"", "notification_citymap_interiordesigner_open": "Designer de interiores está aberto. <PERSON>iro, você precisa fechá-lo", "tutorial_delivery_contract_objective_2": "Coloque a estante de depósito em sua loja de presentes", "tutorial_delivery_contract_objective_3": "Prepare um contrato de entrega para a sua loja de presentes em NY Distro Inc", "tutorial_delivery_contract_objective_4": "Configure o contrato de entrega para entregar pelo menos 1 caixa de Presentes baratos por semana usando \"BizMan Entregas\"", "common_gross_profit": "<PERSON><PERSON> bruto", "itemname_itemgroupscale": "Balanças", "bizman_hrmanager_insurance_demand": "Exigência por seguro", "studyui_diploma_price": "{pricePerHour} por hora", "contacts_manage_employee_button": "Gerenciar funcionário", "employee_contact_message_new_demand": "<PERSON><PERSON>, chefe, estou curtindo trabalhar aqui até o momento, mas, a partir de agora, tenho uma nova exigência: {jobDemandName}", "alert_business_temporarily_closed": "<PERSON><PERSON><PERSON> tempo<PERSON> fechado", "main_menu_custom_game_all_courses_unlocked_tooltip": "Todos os cursos da escola de negócios foram concluídos automaticamente", "main_menu_custom_game_tax_percentage_tooltip": "Impacta a taxa anual de IR", "main_menu_custom_game_market_price_multiplier_tooltip": "Impacta o custo de várias despesas públicas, como produtos importados/por atacado, taxas hospitalares, etc.", "main_menu_custom_game_bank_interest_multiplier_tooltip": "Afeta pagamentos de juros em empréstimos e investimentos", "main_menu_custom_game_bank_negative_interest_rate_tooltip": "Afeta a taxa semanal de juros negativos do banco", "employee_demands": "Exigências de funcionário", "employee_demands_overview": "Visão geral das exigências", "employee_demands_schedule": "Cronograma de funcionário", "employee_demands_environment": "Ambiente de funcionário", "employee_demands_equipment": "Equipamento de funcionário", "employee_demands_benefits": "Benefícios de funcionário", "employee_demands_benefits_healthinsurance": "Plano de saúde", "help_employee_demands_overview_content": "Cada funcionário tem até 3 demandas. Atender às demandas garante que o funcionário fique satisfeito, mas se você ignorar as demandas, o funcionário vai se demitir.\n\nAs demandas vêm em três níveis de urgência:\n* Crítico\n* Importante\n* Bom ter\nQuanto maior a prioridade da demanda, mais impacto ela terá na satisfação do funcionário.\n\nExistem diferentes demandas que os funcionários podem ter:\n* [Benefícios a Empregados](funcionários-exigências-benefícios)\n* [Exigências ambientais](funcionários-exigências-ambiente)\n* [Demandas de equipamentos](funcionários-demandas-equipamentos)\n* [Exigências de Agenda](funcionários-exigências-agenda)", "help_employee_demands_benefits_content": "Como parte de seus desejos e demandas, alguns funcionários exigirão **Benefícios para funcionários**\n\nOs Benefícios dos Empregados incluem:\n* [Seguro de saúde Ouro](employee_demands_benefits_healthinsurance)\n* [Seguro de saúde Prata](employee_demands_benefits_healthinsurance) ou superior\n* [Seguro de Saúde Bronze](employee_demands_benefits_healthinsurance) ou superior", "help_employee_demands_environment_content": "Como parte de seus desejos e demandas, alguns funcionários pedem um determinado ambiente.\n* Ambiente de trabalho limpo: Edifício com uma limpeza de pelo menos 80%\n* Ambiente de trabalho tranquilo: o jogador tem uma felicidade de 50% ou mais", "help_employee_demands_equipment_content": "Como parte de seus Desejos e Demandas, alguns funcionários exigirão determinados equipamentos.\n* [Mesa Executiva (Esquerda)](furniture-officedesk2left)\n* [Mesa Executiva (Direita)](furniture-officedesk2right)\n* [Cafeteira Barata](furniture-cheapcoffeemachine)\n* [Telefone](furniture-classicphone)\n* [Monitor de Computador](furniture-computermonitor)\n* [Mesa Digitalizadora](furniture-graphictablet)\n* [Mesa Digitalizadora (com Tela)](furniture-graphictabletwithscreen)\n* [Mesa de Reunião (Grande)](furniture-largemeetingtable)\n* [Mouse Pad](furniture-mousepad)\n* [Cadeira Multiuso](furniture-multipurposechair)\n* [Cadeira de Escritório](furniture-officechair)\n* [Telefone Comercial](furniture-officephone)\n* [Sofá](furniture-sofagroup)\n* [Geladeira Padrão](furniture-standardfridge)\n* [Mesa de Escritório Padrão](furniture-officedesk1)\n* [Cadeira <PERSON>ump Mesh](furniture-officechair2)\n* [<PERSON><PERSON>our<PERSON>](itemname_watercooler)", "help_employee_demands_schedule_content": "**Definindo o Horário dos Empregados**\n\nUma vez que você designou o empregado para uma empresa no app [MeusEmpregados](skill-myemployees) no seu [BizFone](general-bizphone), visite a página BizMan para a empresa em questão e clique na aba \"Horários\".\n\nA partir dela, clique e arraste o nome do empregado para a estação de trabalho do empregado abaixo do horário. <PERSON><PERSON> isso, você consegue ajustar as horas e os dias que você deseja que eles trabalhem.\n\n**Demandas de Agenda**\nComo parte dos Desejos e Demandas deles, alguns empregados irão demandar certos horários.\n\n* Meio-Período: Entre 10 e 30 horas por semana\n* Tempo Integral: Entre 30 e 50 horas por semana\n* Semana de quatro dias: Designado para trabalhar 4 dias por semana\n* Semana de cinco dias: Designado para trabalhar 5 dias por semana\n* Sem turnos matutinos: Sem turnos designados entre 06:00 e 10:00\n* Sem turnos vespertinos: sem turnos atribuídos entre 14:00 e 16:00\n* Sem turnos noturnos: Sem turnos atribuídos entre 18:00 e 22:00\n* Sem turnos de noite: Sem turnos atrubuídos entre 22:00 e 04:00\n* Fins de semana livres: Sem turnos aos sábados e domingos\n* Sem turnos de limpeza: Sem turnos de limpeza atribuídos", "help_employee_demands_benefits_healthinsurance_content": "**Plano de Saúde**\n\nPara configurar o Plano de Saúde, siga as seguintes etapas:\n* Visite o Hospital e fale com o [Gerente de Plano de Saúde](address: 0 7s)\n* Escolha um [Gerente de RH](skill-hrmanager) e um nível do Plano de Saúde: Ouro, Prata, Bronze\n* O Gerente de Plano de Saúde em breve enviará uma mensagem de texto com uma oferta.\n* Você pode Aceitar, Rejeitar ou Negociar esta oferta.\n\nDepois de aceitar, os funcionários designados para o Gerente de RH estarão cobertos pelo plano escolhido.\n\nNota:\n* Habilidade do Gerente de RH determina que nível de seguro eles podem solicitar\n* Você será cobrado por um mínimo de 10 funcionários\n* Você será cobrado diariamente por este plano\n* Seu Gerente de RH podem atualizar seu plano a qualquer momento em seu App BizMan do seu [BizPhone](general-bizphone)", "tutorial_12_objective_1": "Conclua o curso \"Fundamentos da administração comercial\"", "workouttype_pullups": "Barra fixa", "dialog_health_insurance_manager_hr_manager_skill_too_low": "O Gerente de RH selecionado não possui a habilidade mínima necessária para este tipo de plano", "cashregister_no_paperbags": "Não há sacos de papel disponível", "itemname_djbooth": "<PERSON><PERSON><PERSON>", "itemname_coatcheckfee": "Taxa do guarda-volumes", "itemname_discoball": "Bola <PERSON>", "itemname_barstool": "<PERSON><PERSON><PERSON>", "itemname_cocktailbar": "Coquetelaria", "itemname_cocktailbarcorner": "Coquetelaria de canto", "itemname_cocktailbarbeershelf": "Coquetelaria com prateleira de cerveja", "itemname_cocktailbardrinkscounter": "Balcão de bebidas e coquetéis", "itemname_stagelights": "Luzes de palco", "itemname_decorativedrinksshelf": "Prateleira de bebidas decorativas", "itemname_dancefloor1x1": "Pista de dança modular 1x1", "itemname_dancefloor2x2": "Pista de dança modular 2x2", "itemname_dancefloor4x4": "Pista de dança modular 4x4", "itemname_dancefloor6x6": "Pista de <PERSON>ça <PERSON>", "itemname_cactusneon": "Cacto néon", "itemname_circleneon": "<PERSON><PERSON><PERSON><PERSON>", "itemname_irisneon": "Arco-<PERSON>ris <PERSON>", "itemname_cocktailneon": "<PERSON><PERSON><PERSON> n<PERSON>", "itemname_beer": "Cerveja", "itemname_margarita": "<PERSON><PERSON><PERSON>", "itemname_martini": "<PERSON><PERSON>", "itemname_whisky": "Whisky", "itemname_windowedroomdivider": "Divisória com janela", "itemname_pouf": "Otomano florido", "itemname_decorativeshampoo": "Xampu decorativo", "itemname_hightable": "Mesa alta", "itemname_classicloudspeakersmall": "Alto-falante clássica pequeno", "itemname_classicloudspeakermedium": "Alto-falante clássica médio", "itemname_classicloudspeakerbig": "Alto-falante clássica grande", "itemname_hangingproductsignrectangular": "Placa de produto suspensa retangular", "itemname_hangingproductsignsquare": "Placa de produto suspensa quadrada", "itemname_wallproductsign": "Placa de produto de parede", "itemname_previewterminal": "Terminal de visualização", "itemname_hairshampooingfee": "Taxa de lavagem e escova de cabelo", "itemname_haircuttingfee": "Taxa de corte de cabelo", "itemname_hairstylingfee": "Taxa de penteado", "itemname_hairchemicalfee": "Taxa de química/coloração de cabelo", "itemname_hairdresserchair": "Cadeira de cabeleireiro", "itemname_hairdresserchairmodern": "Cadeira de cabeleireiro moderna", "itemname_hairdresserheadwash": "Lavatório de Cabeleireiro", "itemname_hairdressermirror": "Espelho de cabeleireiro", "itemname_hairdressershelf": "Prateleira de cabeleireiro", "itemname_hairdressersign": "Placa de cabeleireiro", "itemname_haircareproduct": "<PERSON><PERSON><PERSON> cap<PERSON>", "itemname_pictureframe2": "Moldura 2", "itemname_pictureframe3": "Moldura 3", "itemname_pictureframe4": "Moldura 4", "itemname_pictureframe5": "Moldura 5", "itemname_pictureframe6": "Moldura 6", "itemname_securitycamera": "Câmera de segurança", "itemname_securitycameraroof": "Câmera de segurança dome", "itemname_securitypanel": "Painel de segurança", "itemname_securityguardlocker": "Armário do vigilante", "itemname_securitysign": "Placa de segurança", "itemname_icecreamcounter": "Balcão de sorvete", "itemname_icecream": "Sorve<PERSON>", "itemname_classicphone": "Telefone clássico", "itemname_officephone": "Telefone comercial", "itemname_drinksshelf": "Prateleira de bebidas", "phone_wholesale_store_product_on_backorder": "Sentimos informar que estamos sem {itemName} no momento, excluímos o item de sua entrega para {businessName}", "phone_import_product_on_backorder": "Sentimos informar que estamos sem {itemName} no momento, excluímos o item de sua entrega para {businessName}", "wholesale_store_item_out_of_stock": "Este produto está em falta no momento.", "common_undo": "<PERSON><PERSON><PERSON>", "common_redo": "<PERSON><PERSON><PERSON>", "input_key_interiordesignerundo": "Desfazer designer de interiores", "input_key_interiordesignerredo": "Re<PERSON>zer designer de interiores", "input_key_interiordesignerpicker": "<PERSON><PERSON><PERSON> de designer de interiores", "businesstype_nightclub": "Casa noturna", "businesstype_hairdresser": "Cabeleireiro", "transactiontype_entrancefee": "Taxa de entrada de {businessName}", "buildingmanager_entrance_fee_confirm": "Para entrar no negócio {businessName} é preciso pagar uma taxa de {entranceFee}. Quer continuar?", "bizman_product_shortage_warning": "No momento, este produto está escasso. Espere preços altos.", "bizman_product_backorder_warning": "Este produto está com encomenda atrasada. Espere estoques esgotados.", "skillname_dj": "DJ", "skillname_hairstylist": "Est<PERSON><PERSON> capilar", "skillname_securityguard": "Vigilante", "skillname_headhunter": "caçadores de cabeças", "vehiclespawner_rent_confirm": "Quer mesmo alugar este veículo por {price}?", "vehicletypename_electricscooter": "Patinete elétrico", "vehiclespawner_rent_price": "Preço do aluguel: {price}", "escooter_cant_carry_item": "<PERSON>ó <PERSON> possível levar um saco de papel enquanto dirige este veículo.", "input_key_selectmultipleelements": "Selecionar vários elementos", "contacts_taxes_tax_others": "Outros:", "contacts_taxes_gambling": "Ganhos de apostas", "common_blueprints": "Plantas", "common_building_size": "Tamanho do edifício", "common_all": "<PERSON><PERSON>", "blueprints_author": "Por {author}", "blueprints_author_you": "Você", "blueprints_addtoyourlibrary": "Adicionar à sua biblioteca", "blueprints_uploadtosteamworkshop": "Enviar para a Steam Workshop", "blueprints_no_downloads": "Sem downloads", "blueprints_one_download": "1 download", "blueprints_downloads_amount": "{downloadsAmount} downloads", "blueprints_downloads_amount_thousands": "{downloadsInThousands} milhares de downloads", "blueprints_downloads_amount_millions": "{downloadsInMillions} milhões de downloads", "blueprints_list_element_no_steam_info": "Salvo localmente", "blueprintcategory_gallery": "Galeria", "blueprintcategory_mylibrary": "Minha biblioteca", "requirement_placed_at_correct_height": "Este item ficará inutilizável nesta altura", "workout_machine_requirements_not_met": "Este aparelho carece dos requisitos e não pode ser usado", "blueprints_ui_invalid_name": "Insira um nome válido", "blueprints_ui_name_overwrite_confirm": "Já existe um layout com este nome, quer mesmo substituí-lo?", "blueprints_ui_save_layout_as_blueprint": "Salvar layout como projeto", "blueprints_ui_blueprint_name_placeholder": "Nome do modelo", "blueprints_ui_save_blueprint_button": "<PERSON><PERSON> projeto", "blueprints_removefromyourlibrary": "Remover da sua biblioteca", "blueprints_ui_confirm_deleting_from_library": "Tem certeza de que deseja excluir este modelo da sua biblioteca?", "main_menu_delete_character_confirm": "Quer mesmo excluir o personagem \"{characterName}\"?", "main_menu_delete_save_game_confirm": "Quer mesmo excluir o jogo salvo \"{saveGameName}\"?", "blueprints_ui_shelf_stock_disclaimer": "Não inclui o inventário", "blueprints_ui_workshop_item_uploaded": "Modelo enviado para o Workshop com sucesso!", "npc_expression_no_coat_checks": "Não consigo achar um lugar para guardar meu casaco!", "npc_expression_no_available_coat_checks": "Não vou esperar tudo isso só para guardar meu casaco!", "npc_expression_no_dance_floors": "Ah, não tem pista de dança! Que tristeza", "npc_expression_no_available_dance_floors": "Eu queria muito da<PERSON><PERSON><PERSON>, mas a pista está lotada!", "blueprints_ui_workshop_item_upload_failed": "Não foi possível enviar o projeto para a Oficina Steam. Tente novamente e, se o erro persistir, relate-o usando o menu \"Reportar um problema (F2)", "blueprints_ui_upload_to_workshop_headline": "Enviar item para o Workshop", "blueprints_ui_upload_to_workshop_confirm": "Ao enviar este item, você concorda com os <u>Termos de Serviço da Oficina Steam</u>", "blueprints_ui_workshop_terms_of_service": "Termos de Serviço da Steam Workshop", "blueprint_info_ui_downloads": "Downloads", "blueprint_info_ui_released": "Liberado", "blueprint_info_ui_rating": "Avaliação", "blueprintdata_price": "Preço", "blueprintdata_buildingtype": "Tipo de edifício", "blueprintdata_buildingsize": "Tamanho do edifício", "blueprintdata_interiorscore": "Pontuação de interior", "blueprintdata_workstations": "Estações de trabalho", "blueprintdata_pointsofsales": "Pontos de venda", "blueprintdata_palletshelves": "Prateleiras de palete", "blueprintdata_storageshelves": "<PERSON><PERSON><PERSON>", "blueprints_ui_workshop_item_upload_failed_file_not_found": "Não foi possível enviar o modelo para o Steam Workshop: a pasta de **Modelos** foi removida ou não está acessível", "dancetype_dance1": "Dança", "dancetype_dance2": "<PERSON><PERSON><PERSON>", "dancetype_dance3": "Swing", "dancetype_dance4": "Macarena", "dancetype_dance5": "<PERSON><PERSON> tímida", "dancetype_dance6": "<PERSON><PERSON><PERSON>", "dancetype_dance7": "Swim", "dancetype_dance8": "Twist", "dancetype_dance9": "Smooth Operator", "blueprints_synctoworkshop": "Sincronizar com o Workshop", "businesstype_interiorinstallationfirm": "Firma de instalação de interior", "specialbuilding_interiorinstallationfirm": "Firma de instalação de interior", "preview_terminal_preview_button": "Visualizar design", "preview_terminal_select_building_type": "Selecione o tipo do edifício", "preview_terminal_select_building_size": "Selecione o tamanho do edifício", "preview_terminal_select_business_type": "Selecione o tipo do negócio", "preview_terminal_select_design": "Selecione o projeto", "happinessmodifiertype_wenttonightclub": "<PERSON><PERSON> <PERSON> boate", "itemname_loudspeaker": "Alto-falante", "dialog_interior_installation_firm_npc_name": "Agente da firma de instalação de interior", "dialog_interior_installation_firm_start": "<PERSON><PERSON><PERSON>, e boas-vindas a {businessName}. Como posso te ajudar hoje?", "dialog_interior_installation_firm_no_buildings": "Olá! <PERSON><PERSON><PERSON><PERSON>, mas só podemos oferecer nossos serviços a pessoas com edifícios alugados", "dialog_interior_installation_firm_design_settings_header": "Configurações de design", "dialog_interior_installation_firm_accept_sell_items_in_building": "Você aceita vendermos todo o interior existente no edifício e subtrairmos do seu preço total?", "dialog_interior_installation_firm_contract_set_player": "Eu gostaria de instalar o design \"{text}\" em {address} no dia {day}", "dialog_interior_installation_firm_contract_set_manager": "<PERSON><PERSON> o<PERSON>. Começaremos a instalação do seu interior no dia {day}. Observe que o endereço não estará acessível até meia-noite.", "dialog_interior_installation_firm_select_notification_select_installation_day": "Selecione um dia de instalação", "dialog_interior_installation_firm_select_notification_select_design": "Por favor selecione um desenho", "dialog_interior_installation_cant_install_in_unrented_building": "<PERSON><PERSON><PERSON>, tínhamos uma instalação planejada para o prédio em {address}, mas parece que o prédio não está mais registrado em seu nome. Cancelamos a instalação.", "dialog_interior_installation_couldnt_find_layout": "Sentimos muito mesmo, mas o design que você escolheu para a instalação em seu edifício em {address} não está mais disponível. Se ainda tiver interesse, entre em contato conosco de novo e escolha um design diferente.", "dialog_interior_installation_not_enough_money": "<PERSON><PERSON><PERSON>, parece que não conseguimos cobrar o custo da instalação de interiores em {address}. Cancelamos o pedido. Por favor, certifique-se de ter fundos suficientes para nossos serviços.", "dialog_interior_installation_done": "Olá! A instalação no edifício em {address} foi concluída. Agradecemos por usar nossos serviços!", "dialog_interior_installation_cant_install_while_inside_building": "Não foi possível realizar a instalação em {address} devido a uma atividade inesperada no interior do local. Tentaremos novamente amanhã.", "transactiontype_interiorinstallation": "Instalação de interior por {businessName}", "transactiontype_interiorinstallation_label": "Instalação de interior", "common_retry": "Tentar novamente", "common_accept": "Aceitar", "blueprints_ui_illegal_characters_warning": "Removemos caracteres ilegais. Novo nome: \"{fixedName}\"", "interior_design_cheap": "<PERSON><PERSON>", "interior_design_love": "<PERSON><PERSON>", "interior_design_expensive": "<PERSON><PERSON>", "interior_design_industrial": "Industrial", "interior_design_modern": "Moderno", "interior_design_rustic": "Rús<PERSON><PERSON>", "interior_design_classic": "Clássico", "interior_design_pizza": "Pizza", "interior_design_lemon": "Limão", "interior_design_lime": "Lima", "interior_design_orange": "<PERSON><PERSON>", "interior_design_nature": "Natureza", "interior_design_dark": "Escuro", "interior_design_sky": "<PERSON><PERSON><PERSON>", "businessrequirement_loudspeaker": "Alto-falante", "dialog_interior_installation_fee": "Taxa de instalação", "dialog_interior_installation_layout_price": "Preço do layout", "dialog_interior_installation_day": "Dia de instalação", "dialog_interior_installation_design": "Design", "dialog_interior_select_address": "Selecionar endereço", "dialog_interior_select_day": "Selecionar dia de instalação", "dialog_interior_select_design": "Selecione o projeto", "cant_enter_building_while_interior_installation": "Não é possível entrar devido a obras no edifício", "common_total": "Total", "econoview_row_theft": "R<PERSON><PERSON>", "bizman_security": "Segurança atual:", "bizman_security_level": "{securityLevel} ({securityPercentage}%)", "bizman_security_level_poor": "<PERSON><PERSON><PERSON>", "bizman_security_level_average": "Mediana", "bizman_security_level_good": "Bo<PERSON>", "bizman_security_level_perfect": "Per<PERSON>o", "itemoverlay_security_info_protected": "Este item é monitorado por câmeras de segurança", "itemoverlay_security_info_not_protected": "Este item não é monitorado por câmeras de segurança", "businessrequirement_djbooth": "<PERSON><PERSON><PERSON>", "npc_expression_no_djs": "Não há DJs! Quem está tocando a música? Que golpe!", "requirement_placed_in_the_entrance": "Colocado na entrada", "blueprints_workshop_cant_upload_not_connected": "Não consigo carregar o projeto na Oficina Steam. Isso se deve provavelmente à falta de conexão com o Steam ou com a internet.", "blueprints_workshop_cant_load_blueprints": "Não foi possível carregar projetos na Oficina Steam. Isso se deve provavelmente à falta de conexão com o Steam ou com a internet.", "requirement_placed_in_order_based_business": "Feito em empresas com base em pedidos", "requirement_placed_in_retail_business": "Feito em empresas varejistas", "djbooth_notification_cant_use": "Não é possível interagir com este item.", "entertain_panel_dj_headline": "DJ", "entertain_panel_start_dj_button": "Comece a tocar", "click_to_dj": "Clique para discotecar", "entertainui_stop_dj": "<PERSON><PERSON> <PERSON>", "entertainui_slider_label_dj": "Faça discotecagem por {entertainingHours} horas, {entertainingMinutes} minutos. Isso dará um reforço de felicidade de {boostPercentage}% por {boostHours} horas", "happinessmodifiertype_djed": "Discotecar", "npc_expression_no_scales_retail_business": "Como esperam que eu pese estas frutas e vegetais sem nenhuma balança?", "myemployees_employees_tab": "Funcionários", "myemployees_candidates_tab": "<PERSON><PERSON><PERSON><PERSON>", "myemployees_mass_action_assignbusiness": "Atribuir negócio", "myemployees_mass_action_trainprimaryskill": "Treinar habilidade principal", "myemployees_mass_action_assignuniform": "Atribuir uniforme", "myemployees_mass_action_hirecandidate": "<PERSON><PERSON><PERSON> candidato", "myemployees_mass_action_fire": "<PERSON><PERSON><PERSON>", "order_based_self_service_no_stock": "Nenhum dos produtos selecionados em estoque.", "myemployees_mass_action_discardcandidate": "Des<PERSON><PERSON> candidato", "myemployees_hourlywage": "<PERSON><PERSON><PERSON>", "myemployees_schedule": "Cronograma", "myemployees_recruitmentsource": "Pontuação de recrutamento", "myemployees_expiresin": "Expira em", "myemployees_assignbusiness": "Atribuir ao negócio", "myemployees_hire": "<PERSON><PERSON>r", "myemployees_candidates_source_headhunter_default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myemployees_candidates_source_headhunter": "Recrutador: {headhunterName}", "common_hours": "{hours} horas", "myemployees_hirecandidate_button": "<PERSON><PERSON>r", "myemployees_discardcandidate_button": "Descar<PERSON>", "recruitmentagencydialog_notification_select_schedule": "Selecione pelo menos um tipo de cronograma", "dialog_recruitment_agency_schedule_types": "Tipos de cronograma", "bizman_menu_headhunters": "Recrutadores", "bizman_headhunters_hint_seated_only": "Somente recrutadores sentados podem ser acessados", "bizman_menu_headhunters_recruiting_header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bizman_menu_headhunters_automatic_replacement_header": "Substituição automática", "npc_expression_no_hair_care_products": "Não vou conseguir arrumar meu cabelo se não houver produtos disponíveis!", "jobdemand_hasphone": "Qualquer telefone", "jobdemand_hasofficephone": "Telefone comercial", "phone_recruitment_agency_campaign_finished": "Olá! Concluímos a campanha. Todos os candidatos foram enviados para seu aplicativo Meus Funcionarios. Agradeço a você por fazer negócios conosco!", "npc_expression_no_head_washers": "Parece que não vou conseguir lavar o meu cabelo!", "npc_expression_no_hairdresser_chairs": "Ah... Não há estilistas disponíveis...", "bizman_headhunters_start_recruiting_button": "Começar o recrutamento", "bizman_headhunters_view_current_recruits_button": "Ver candidatos atuais", "bizman_headhunters_stop_recruiting_button": "<PERSON>re de recrutar", "bizman_headhunters_type_of_employee_title": "Tipo de funcionário", "bizman_headhunters_type_of_employee_description": "Selecione o tipo de funcionário que você está recrutando.", "bizman_headhunters_currently_recruiting_title": "<PERSON><PERSON><PERSON><PERSON>do no momento", "bizman_headhunters_employee_type_title": "Tipo de funcionário", "bizman_headhunters_skill_level_title": "Nível de habilidade", "bizman_headhunters_skill_level_value": "{minSkill}% – {maxSkill}%", "bizman_headhunters_salary_range_title": "Faixa salarial", "bizman_headhunters_salary_range_value": "{minWagePerHour} - {maxWagePerHour}/hora", "bizman_headhunters_points_used_value": "Pontos usados: {pointsUsed}", "bizman_headhunters_headhunter_skill_value": "Habilidade: {skillPercentage}%", "bizman_headhunters_wage_and_skill_title": "Salário e habilidade", "bizman_headhunters_wage_and_skill_description": "Defina a habilidade exigida dos candidatos", "bizman_headhunters_wage_per_hour_value": "entre {minWagePerHour} e {maxWagePerHour} por hora", "bizman_headhunters_deal_breakers_title": "Impeditivos", "bizman_headhunters_deal_breakers_description": "Use Pontos de Recrutamento (RP) para remover demandas que você não deseja atender. Recrutadores recebem um Ponto de Recrutamento para cada nível de habilidade (%), então certifique-se de treiná-los bem.", "bizman_headhunters_deal_breakers_clear_all_button": "<PERSON><PERSON> tudo", "bizman_headhunters_automatic_replacement_not_enough_skill_title": "Habilidade insuficiente", "bizman_headhunters_automatic_replacement_not_enough_skill_description": "Este recrutador precisa de pelo menos 75% de habilidade para realizar a Substituição Automática", "bizman_headhunters_assigned_hr_managers_title": "Gerentes de RH atribuídos", "bizman_headhunters_assigned_hr_managers_description": "Você pode atribuir gerentes de RH a este recrutador. Ao fazer isso, o recrutador substituirá automaticamente todos os funcionários que se demitirem ou se aposentarem e estiverem atualmente atribuídos a esse Gerente de RH.", "bizman_headhunters_no_dealbreakers_for_selected_skill": "Não há impeditivos disponíveis para o tipo de funcionário selecionado", "headhunter_deal_breaker_not_enough_rp": "O recrutador não tem Pontos de Recrutamento suficientes", "headhunter_deal_breaker_group_label": "{dealBreakerType} ({rpCost} RP)", "headhuntersdealbreakertype_fulltime": "Tempo integral", "headhuntersdealbreakertype_parttime": "<PERSON><PERSON> expediente", "headhuntersdealbreakertype_noweekends": "Sem finais de semana", "headhuntersdealbreakertype_fivedaysaweek": "5 dias por semana", "headhuntersdealbreakertype_fourdaysaweek": "4 dias por semana", "headhuntersdealbreakertype_nomorningshifts": "Sem turnos matinais", "headhuntersdealbreakertype_noafternoonshifts": "Sem turnos da tarde", "headhuntersdealbreakertype_noeveningshifts": "Sem turnos vespertinos", "headhuntersdealbreakertype_nonightshifts": "Sem turnos noturnos", "headhuntersdealbreakertype_nocleaningshifts": "Sem turnos de limpeza", "headhuntersdealbreakertype_environmentdemand": "Exigência de ambiente", "headhuntersdealbreakertype_equipmentdemand": "Exigência de equipamento", "headhuntersdealbreakertype_benefitdemand": "Exigência de benefício", "headhunter_couldnt_find_employee_with_requirements": "<PERSON><PERSON><PERSON>, chefe, sinto muito em informar que não consegui encontrar ninguém que atendesse aos requisitos definidos para o último processo de recrutamento. Teremos que reduzir os requisitos. <PERSON><PERSON> enquanto, parei de procurar candidatos.", "jobdemand_noafternoons": "Sem turnos da tarde", "jobdemand_noafternoons_description": "Nenhum turno atribuído entre 14h00 e 16h00", "headhunter_select_employee": "Selecionar funcionário", "headhunter_selected_employee_already_selected": "O funcionário selecionado já está atribuído a outro espaço", "bizman_headhunters_automatically_replace_on_resign_label": "Substituir automaticamente os funcionários quando eles se demitirem", "bizman_headhunters_automatically_replace_on_retire_label": "Substituir automaticamente os funcionários quando eles se aposentarem", "bizman_headhunters_employees_close_to_needing_replacement": "{amount}/{maxAmount} funcionários perto de precisarem de substituição", "bizman_headhunters_replacement_reason": "Motivo da substituição", "bizman_headhunters_replace_now": "Substituir agora", "bizman_headhunters_retiring_in_days": "Aposentadoria: {daysBeforeRetiring} dias", "bizman_headhunters_low_satisfaction": "Satisfação: {percentage}%", "bizman_headhunters_replacement_fee": "Taxa da substituição: {replacementFee}", "transactiontype_replacementfee_label": "Taxa da substituição", "transactiontype_replacementfee": "Substituição de funcionário por {employee}", "savegame_compatibility_panel_headline": "Boas-vindas ao acesso antecipado 0 de Big Ambitions.{eaVersion}", "savegame_compatibility_panel_first_line": "Desde a última vez que você jogou, uma nova versão foi lançada. Para atualizar seus jogos salvos existentes para a nova versão, leia e aceite o registro de alterações abaixo:", "savegame_compatibility_panel_accept_button": "Aceitar alterações e atualizar os jogos salvos", "savegame_compatibility_panel_skip_button": "Pular atualização", "savegame_compatibility_panel_last_line": "Não quer jogar a nova versão? Lembre-se de que você sempre pode voltar para a versão antiga usando a aba \"Betas\" nas propriedades do jogo no <PERSON>.", "savegame_compatibility_panel_successful": "{saveGamesUpgraded} jogos salvos foram atualizados com sucesso. Esperamos que você goste da atualização!", "change_character_hair_title": "<PERSON><PERSON> cabelo", "change_character_hair_save_button": "<PERSON><PERSON> cabelo", "change_character_hair_not_paid_for_warning": "Mudanças não pagas não foram realizadas", "businessrequirement_hairdresserchair": "Cadeira de cabeleireiro", "businessrequirement_headwasher": "La<PERSON><PERSON>", "businessrequirement_shelfwithhaircareproducts": "<PERSON><PERSON><PERSON> cap<PERSON>", "notifications_headhunter_employee_replaced": "O recrutador {employeeName} substituiu {fromname} por {toname}", "main_menu_upgrade_savegames": "Atualizar jogos salvos da versão anterior", "main_menu_savegames_upgraded_notification": "Jogos salvos atualizados!", "itempanel_click_sell_first_quest_not_completed": "Não é possível vender itens até concluir a primeira missão", "employees_skill_with_percentage": "{skillName} ({percentage}%)", "notification_no_items_available": "Sem {itemname} disponível", "shop_sign_perderson_security_title": "Segurança", "shop_sign_perderson_security_secondtitle": "Preserve a segurança", "shop_sign_perderson_hairdresser_title": "Cabeleireiro", "shop_sign_perderson_hairdresser_secondtitle": "<PERSON><PERSON><PERSON> come<PERSON>", "shop_sign_perderson_clothingstore_title": "Loja de r<PERSON>", "shop_sign_perderson_clothingstore_secondtitle": "Pela moda", "shop_sign_perderson_nightclub_title": "Casa noturna", "shop_sign_perderson_nightclub_secondtitle": "Novos produtos!", "shop_sign_perderson_supermarket_title": "Supermercado", "shop_sign_perderson_supermarket_secondtitle": "0% de desconto", "shop_sign_perderson_producers_title": "Produtores", "shop_sign_perderson_producers_secondtitle": "Para seu negócio", "shop_sign_perderson_shelves_title": "Prateleiras", "shop_sign_perderson_shelves_secondtitle": "Para seu depósito", "tutorial_objective_hire_customer_service": "Contrate um funcionário de atendimento ao cliente", "djbooth_notification_need_free_hands": "Suas mãos precisam estar livres para usar a Cabine de DJ", "dialog_furniture_store_on_contract_settings_set_player_business_name": "Gostaria que esses {amount} itens fossem entregues a {businessName} ({address}) no dia {day} às {hour}:<br> {text}", "notification_delivery_contract_arrived_business_name": "Sua remessa de {fromname} para {businessName} ({toname}) chegou", "character_customization_notification_name_invalid_characters": "Não é possível usar caracteres inválidos para o nome do personagem", "bizman_hrmanager_health_insurance_wait_for_hospital": "Espere o hospital enviar uma oferta", "bizphone_econoview": "A<PERSON>con<PERSON>", "help_bizphone_econoview_content": "O aplicativo *EconoView* mostra todas as suas informações financeiras.\n\n**Últimas Transações**\n\nEsta seção mostrará suas transações recentes. Clique em \"Visualização Completa de Transações\" para pesquisar ou exportar suas transações mais recentes.\n\n**Empréstimos / Investimentos**\n\nEsta seção permite que você gerencie seus [Empréstimos e Investimentos](finance-banks).\n\n**Demonstrativo de Receitas**\n\nEsta seção mostra o desdobramento de suas receitas e despesas dos últimos dias. Se você clicar no nome de um negócio, poderá ver o desdobramento específico das receitas desse negócio.\n\nObservação: o demonstrativo de receitas mostra o quão lucrativo é o seu negócio, incluindo o custo do estoque. No entanto, como você já pagou por esse estoque anteriormente, esse valor não é deduzido novamente de sua receita, ele é usado apenas para mostrar a lucratividade geral.", "building_title": "Gestão de edifícios", "building_types": "Tamanhos/Tipos", "building_traffic": "Trânsito / Promoção", "building_limit": "Capacidade de clientes", "building_interiordesigner": "Designer de interiores", "building_blueprints": "Modelos", "building_installationfirms": "Firmas de instalação", "building_security": "Segurança/Roubo", "building_customerdemands": "Satistação do Cliente", "help_building_types_content": "Cada layout de edifício possui um código que indica seu layout, tamanho e capacidade de clientes/veículos.\n\n**Varejo**\n* **A1**: 75m (807ft) / 15 Capacidade de Clientes\n* **A2**: 75m (807ft) / 15 Capacidade de Clientes\n* **C1**: 225m (2422ft) / 30 Capacidade de Clientes\n* **C2**: 225m (2422ft) / 30 Capacidade de Clientes\n* **D2**: 285m (3068ft) / 40 Capacidade de Clientes\n* **M1**: 1000m (10764ft) / 75 Capacidade de Clientes\n\n**Escritório**\n* **A3**: 75m (807ft) / 4 Capacidade de Clientes\n* **C1**: 225m (2422ft) / 8 Capacidade de Clientes\n* **C2**: 225m (2422ft) / 8 Capacidade de Clientes\n* **D2**: 285m (3068ft) / 10 Capacidade de Clientes\n* **J1**: 285m (3068ft) / 10 Capacidade de Clientes\n* **K1**: 660m (7104ft) / 50 Capacidade de Clientes\n\n**Armazém**\n* **H1**: 690m (7427ft) / 1 Capacidade de Veículos\n* **H2**: 690m (7427ft) / 1 Capacidade de Veículos\n* **H3**: 690m (7427ft) / 1 Capacidade de Veículos\n* **I1**: 1292m (13907ft) / 2 Capacidade de Veículos\n* **I2**: 1292m (13907ft) / 2 Capacidade de Veículos\n* **I3**: 1292m (13907ft) / 2 Capacidade de Veículos\n\n**Residencial**\n* **B1**: 54m (581ft)\n* **F1**: 96m (1033ft)\n* **L1**: 204m (2196ft)\n* **N1**: 589m (6340ft)", "help_building_traffic_content": "Quando você combina o **Índice de Tráfego e Marketing**, você obtém a promoção geral para o seu negócio. Você deseja alcançar uma promoção de 100% para maximizar seus potenciais clientes.\n\nO Índice de Tráfego não pode ser alterado, portanto, é importante escolher um prédio com um índice alto ao alugar um edifício para o seu novo negócio. Usando os aplicativos do seu [BizPhone](general-bizphone), você pode ver o Índice de Tráfego do edifício no BizMan ou no Voogle Maps.\n\nO Marketing pode ser usado para complementar o Índice de Tráfego e aumentar sua Promoção. Tamanhos diferentes de edifícios requererão quantidades diferentes de marketing, então você precisará encontrar a combinação certa de marketing para cada negócio.\n\nVocê pode obter campanhas de marketing nos seguintes locais:\n* [McCain's eMarketing](address:17 3a)\n* [CityAds](address:5 2a)", "help_building_limit_content": "**Capacidade de Clientes**\n\nCada prédio tem um limite máximo de capacidade de clientes, que é o número máximo de clientes que você pode atender por hora.\n* Dependendo do [Tipo de Edifício](building-types), a Capacidade de Clientes pode variar de 4 a 75.\n\nNo entanto, cada item de mobília adiciona apenas a sua própria Capacidade de Clientes. Portanto, para atingir o Limite do Edifício, você pode precisar de vários itens de mobília iguais.\n* Por exemplo, se um item de mobília tem uma capacidade de clientes de 10 e o limite do edifício é 30, você precisará de 3 desses itens de mobília para atingir o limite do edifício.\n\nLembre-se de que, se a capacidade de qualquer item de mobília estiver abaixo da capacidade máxima do edifício, isso limitará a capacidade máxima de clientes de toda a loja.", "help_building_interiordesigner_content": "Clique no botão **Designer de Interiores** para entrar no modo de Design de Interiores.\n\nNeste modo, você pode:\n\n**<PERSON><PERSON><PERSON> os designs dos pisos e paredes.**\n\nEscolha um estilo de piso ou parede e depois \"pinte\" esse design onde você desejar. Se você segurar a tecla \"Alt\" e clicar em um piso ou peça de parede existente, ele selecionará automaticamente o design que foi usado ali. O custo total depende dos estilos que você escolher.\n\n**Recolorir Móveis**\n\nSe você clicar com o botão direito na maioria dos móveis, poderá recolori-los mediante uma taxa. A maioria dos itens possui várias seções que você pode recolorir.\n\n**Definir filas de clientes para pontos de venda e guarda-roupas**\n\nClique com o botão direito no seu item para configurar sua fila de clientes. Você pode clicar no cilindro alto para mover a posição da fila. Você pode adicionar mais posições para fazer a fila seguir um caminho mais complicado.\n\n**Desfazer/Refazer & Aplicar/Reverter**\n\nSe você cometer um erro, pode usar os botões de desfazer e refazer para voltar atrás e avançar. Ou se quiser começar de novo, clique em Reverter. Certifique-se de aplicar as alterações e pagar antes de sair!\n\n**Salvar Layout Como Blueprint**\n\nDepois de terminar de decorar sua loja, você pode salvar um blueprint do prédio em que está atualmente. Clique neste botão, dê um nome ao blueprint e salve-o!", "help_building_blueprints_content": "**Modelos** podem ser encontradas no menu Esc ou no Menu Principal.\n\n**Minha Biblioteca**\n\nQuando você salva um modelo, ela vai para a aba Minha Biblioteca. É aqui que você pode gerenciar/excluir seus modelos locais, bem como qualquer modelo que você tenha baixado da comunidade.\n\nSe você quiser compartilhar seus modelos com a comunidade **Big Ambitions**, você pode fazer o upload delas na Galeria do Steam.\n\n**Galeria do Steam Workshop**\n\nA partir daqui, você pode navegar e baixar modelos de outros membros da comunidade para sua própria Biblioteca. Você precisará baixar um modelo antes de usar uma [Empresa de Instalação](building-interiorinstallationfirms) para instalá-la em seu próprio negócio.\n\n*Observação:* Cada modelo só pode ser usada em prédios com o mesmo layout e tamanho exato, ou seja, um modelo A1 não pode ser usada em um prédio A2.", "help_building_installationfirms_content": "**Empresas de Instalação** são usadas para configurar e instalar layouts em seus prédios. Você pode usar os layouts padrão delas ou um [Modelo](building-blueprints) da sua biblioteca.\n\n* [Kingdom Office](address: 43 4a) implenta os modelos em seus prédios de escritório\n* [<PERSON><PERSON>](address: 2 1a) implenta os modelos em seus prédios comerciais e de armazém\n* [Nugem Interiors](address: 11 1a) implenta os modelos em seus prédios residenciais\n\nVisite o agente na empresa de instalação para agendar a instalação do interior.", "help_building_security_content": "**Segurança e Roubo** começam a se tornar um problema conforme você cresce.\n\nInfelizmente, se deixar suas lojas sem proteção, você começará a perder parte do seu estoque todos os dias - especialmente para suas lojas de joias, o que pode ser bastante devastador!\n\nVerifique o aplicativo BizMan no seu [BizPhone](general-bizphone) para ver seu nível de segurança atual. Felizmente, você pode aumentar esse índice usando esses recursos de segurança:\n\n**Painéis de Segurança**\n\n[Painéis de Segurança](furniture-securitypanel) podem ser colocados em ambos os lados das portas da frente.\n\nUm par de painéis será suficiente para proteger lojas menores com estoque de menor valor.\n\n**Câmeras**\n\nCâmeras de Segurança montadas na parede [Security Cameras](furniture-securitycamera) e Câmeras de Segurança [Dome Security Cameras](furniture-securitycameraroof) podem ser colocadas ao redor da sua loja para proteger itens dentro de sua área de visão. Itens no campo de visão das câmeras têm uma chance muito baixa de serem roubados, mesmo que o prédio não esteja 100% seguro.\n\nLojas maiores com estoque mais valioso podem precisar de uma combinação de Câmeras e Painéis de Segurança.\n\n**Guardas de Segurança**\n\nColoque um [Guarda-Volumes de Guarda de Segurança](furniture-securityguardlocker) em algum lugar da sua loja para designar um [Guarda de Segurança](skill-securityguard) para a programação da sua loja.\n\nPara lojas com o estoque mais valioso, como [Joalherias](businesstypes-jewelrystore) ou grandes [Lojas de Roupas](businesstypes-clothingstore), você precisará pagar por hora pelo menos 1 Guarda de Segurança para manter seus objetos de valor protegidos.", "help_building_customerdemands_content": "Clientes começarão a fazer demandas em suas lojas. Se você não quer arriscar perder clientes, preste atenção no que eles estão falando.\n\nVocê pode monitorar Satisfação do Cliente no App BizMan na página Insights do seu [BizPhone](general-bizphone). Lembre-se que pode levar alguns dias para que os clientes se adaptem às suas alterações.\n\n**Atendimento ao Cliente**:\nEsta é uma medição de quão bem seus funcionários estão cuidando dos clientes.( Funcionários com maior habilidade e satisfação proporcionam uma melhor experiência ao cliente.\n\nUse seu [App MyEmployees](skill-myemployees) para treinar seus funcionários e ver suas demandas.\n\n**Precificação:**\nSe você alterar seus preços, poderá monitorar a reação dos clientes aqui.\n\nO preço mais alto aceitável depende do bairro em que você está, do preço mais baixo atual no bairro e da habilidade e dos níveis de satisfação do seu funcionário.\n\n**Interior:**\n* **Design de Interiores**: Clientes gostam de uma loja com belos designs de paredes e pisos. Use o modo de [Design de Interiores](building-interiordesigner) em sua loja para melhorá-lo\n* **Música**: Os clientes gostam de ouvir música enquanto fazem compras. Existem vários [Opções Musicais](furniture-musicgroup) para você tocar música\n* **Lixo**: Se os clientes tiverem lixo, eles precisam de um lugar para jogá-lo fora! Coloque uma [Lixeira](furniture-trashbin) em sua loja para resolver este problema.\n* **Assentos**: Se os clientes tiverem comida para comer, eles vão querer um lugar para se sentar. Coloque [Cabine de Restaurante](furniture-restaurantbooth) ou [Mesa de Jantar](furniture-diningtablegroup) suficientes para fornecer-lhes assentos.\n* **Uniformes**: Definir uniformes dos funcionários no [App MyEmployees](skill-myemployees) do seu [BizPhone](general-bizphone)\n\n**Limpeza:**\nQuanto mais clientes e funcionários você tiver circulando pela loja, mais suja ela ficará.\n\nUse o [Ponto de Limpeza](furniture-cleaningstation) para limpar ou atribua um funcionário de limpeza.", "help_businesstype_hairdresser_content": "**Salões de Cabeleireiro** são negócios varejistas.\n\nOs clientes são atendidos por funcionários.\n\nO negócio requer os seguintes móveis para funcionar:\n\n* [Caixa Registradora](furniture-cashregister)\n* [Prateleira de Cabeleireiro](furniture-hairdressershelf)\n* [<PERSON><PERSON> de Cabeleireiro](furniture-hairdresserchair) ou [Cadeira de Cabeleireiro Moderna](furniture-hairdresserchairmodern)\n* [Lavatório de Cabeleireiro](furniture-hairdresserheadwash)\n\nOs negócios desse tipo vendem principalmente:\n\n* [Taxa de Químicos / Coloração de Cabelo](fees-hairchemicalfee)\n* [Taxa de Corte de Cabelo](fees-haircuttingfee)\n* [Taxa de Lavagem e Secagem de Cabelo](fees-hairshampooingfee)\n* [Taxa de Estilização de Cabelo](fees-hairstylingfee)\n\nE podem também vender:\n\n* [Produto para Cuidado do Cabelo](products-haircareproduct)\n\nFuncionários com as seguintes habilidades podem ser designados:\n\n* [<PERSON><PERSON><PERSON>ire<PERSON>](skill-hairstylist)\n* [Atendimento ao Cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_businesstype_nightclub_content": "**Boates** são negócios de varejo. \n\nOs clientes são atendidos por funcionários.\n\nO negócio requer os seguintes móveis para funcionar:\n\n* [Caixa registradora](furniture-cashregister)\n* [Coat Check (Left)](furniture-coatcheckleft) ou [Coat Check (Right)](furniture-coatcheckright)\n* [DJ Booth](furniture-djbooth)\n* [Speaker](furniture-musicgroup)\n* Pelo menos um produto para vender (veja abaixo)\n\nNegócios desse tipo vendem principalmente:\n\n* [Beer](products-beer)\n* [Coat Check Fee](fees-coatcheckfee)\n* [Nightclub Cover Charge](fees-nightclubcovercharge)\n* [Margarita](products-margarita)\n* [<PERSON><PERSON>](products-martini)\n* [Whisky](products-whisky)\n\nE também pode vender:\n\n* [Soda Pode](products-sodacan)\n\nFuncionários com as seguintes habilidades podem ser designados:\n\n* [DJ](skill-dj)\n* [Atendimento ao Cliente](skill-customerservice)\n* [Lim<PERSON><PERSON>](skill-cleaning)", "itemname_covercharge": "Couvert", "help_itemname_beer_content": "**Cerveja** é um tipo de produto vendido principalmente em [Casas Noturnas](businesstypes-nightclub) and [Lojas de Bebidas](businesstypes-liquorstore).\n\nO produto pode ser colocado à venda nos seguintes móveis, com base no tipo de loja.\n\n**[Casas Noturnas](businesstypes-nightclub)**:\n* [Coquetelaria (Prateleira de Cerveja)](furniture-cocktailbarbeershelf)\n* [Coquetelaria (Prateleira de Cerveja em Madeira)](furniture-woodencocktailbarbeershelf)\n\n**[Lojas de Bebidas](businesstypes-liquorstore)**:\n* [Prateleira de Bebidas](furniture-drinksshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [Importação United Ocean](address: 3 pier)", "help_itemname_coatcheckfee_content": "**Taxa de guarda-volumes** é coletada dos clientes quando eles entram em [Casas noturnas](businesstypes-nightclub) e entregam casacos ou jaquetas a um funcionário para armazenamento numa área de Guarda-volumes.\n\nPara coletar essa taxa, é preciso o seguinte:\n* [Guarda-volumes esquerdo](furniture-coatcheckleft) ou [Guarda-volumes direito](furniture-coatcheckright)\n* [Funcionário de atendimento ao cliente](skill-customerservice)", "help_itemname_covercharge_content": "A **Taxa de Entrada** é uma taxa cobrada dos clientes quando eles entram em [Boates](businesstypes-nightclub).\n\nEssa taxa possui duas variações:\n* Taxa de Entrada em Dias de Semana\n* Taxa de Entrada em Fins de Semana\n\nNada é necessário para cobrar essa taxa, ela é cobrada automaticamente quando os clientes entram no estabelecimento.", "help_itemname_haircareproduct_content": "**Produto de Cuidados Capilar** é um tipo de produto vendido principalmente em [Salão de Cabeleireiro](businesstypes-hairdresser).\n\n**Nota:** Produto de Cuidados Capilar são usados por [Cabeleireiro](skill-hairstylist) para realizar qualquer serviço.\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [Prateleira de Cabeleireiro](furniture-hairdressershelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [JetCargo Importações](address: 1 pier)", "help_itemname_hairchemicalfee_content": "A **Taxa de Produtos Químicos para o Cabelo** é cobrada dos clientes quando eles entram em um negócio de [Salão de Cabeleireiro](businesstypes-hairdresser) e escolhem esse serviço.\n\nPara cobrar essa taxa, são necessários os seguintes itens:\n* [<PERSON><PERSON> de Cabeleireiro](furniture-hairdresserchair) ou [Cadeira de Cabeleireiro Moderna](furniture-hairdresserchairmodern)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-hairstylist)", "help_itemname_haircuttingfee_content": "A **Taxa de Corte de Cabelo** é cobrada dos clientes quando eles entram em um negócio de [Salão de Cabeleireiro](businesstypes-hairdresser) e escolhem esse serviço.\n\nPara cobrar essa taxa, são necessários os seguintes itens:\n* [<PERSON><PERSON> de Cabeleireiro](furniture-hairdresserchair) ou [Cadeira de Cabeleireiro Moderna](furniture-hairdresserchairmodern)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-hairstylist)", "help_itemname_hairshampooingfee_content": "**Taxa de Lavagem & Secagem do Cabelo** é cobrada dos clientes quando eles entram num negócio de [Salão de Cabeleireiro](businesstypes-hairdresser) e escolhem esse serviço.\n\nPara cobrar essa taxa, são necessários os seguintes itens:\n* [Lavatório de Cabeleireiro](furniture-hairdresserheadwash)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-hairstylist)\n* [Produto de Cuidados Capilar](products-haircareproduct)", "help_itemname_hairstylingfee_content": "A **Taxa de Estilo de Cabelo** é cobrada dos clientes quando eles entram em um negócio de [Salão de Cabeleireiro](businesstypes-hairdresser) e escolhem esse serviço.\n\nPara cobrar essa taxa, são necessários os seguintes itens:\n* [<PERSON><PERSON> de Cabeleireiro](furniture-hairdresserchair) ou [Cadeira de Cabeleireiro Moderna](furniture-hairdresserchairmodern)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-hairstylist)", "help_itemname_hourlygraphicdesignerfee_content": "A **Taxa de Estilo de Cabelo** é cobrada dos clientes quando eles entram em um negócio de [Salão de Cabeleireiro](businesstypes-hairdresser) e escolhem esse serviço.\n\nPara cobrar essa taxa, são necessários os seguintes itens:\n* [<PERSON><PERSON> de Cabeleireiro](furniture-hairdresserchair) ou [Cadeira de Cabeleireiro Moderna](furniture-hairdresserchairmodern)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-hairstylist)", "help_itemname_hourlylawyerfee_content": "A **Taxa Horária do Advogado** é cobrada dos clientes quando eles se conectam digitalmente com um negócio de [Escritório de Advocacia](businesstypes-lawfirm) e utilizam seus serviços.\n\nPara cobrar essa taxa, os seguintes itens são necessários:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n* [<PERSON><PERSON><PERSON>](skill-lawyer)", "help_itemname_hourlyprogrammerfee_content": "A **Taxa Horária do Programador** é cobrada dos clientes quando eles se conectam digitalmente com um negócio de [Desenvolvimento Web](businesstypes-webdev) e utilizam seus serviços.\n\nPara cobrar essa taxa, os seguintes itens são necessários:\n* [Estação de Trabalho de Computador](furniture-computerworkstation)\n* [Programador](skill-programmer)", "help_itemname_icecream_content": "**Sorvete** é um tipo de produto vendido principalmente em [Restaurantes de Fast Food](businesstypes-fastfoodrestaurant) e [Supermercados](businesstypes-supermarket).\n\nAlém disso, pode ser vendido em [Cafeterias](businesstypes-coffeeshop).\n\nO produto pode ser colocado nos seguintes móveis, com base no tipo de loja:\n* [Restaurante de Fast Food](businesstypes-fastfoodrestaurant) ou [Cafeteria](businesstypes-coffeeshop) utilizam o [Balcão de Sorvete](furniture-icecreamcounter)\n* [Supermercado](businesstypes-supermarket) utiliza o [Freezer Industrial](furniture-industrialfreezer) ou [Freezer Industrial Pequeno](furniture-smallindustrialfreezer)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_margarita_content": "**Margarita** é um tipo de produto vendido principalmente em [Casas Noturnas](businesstypes-nightclub).\n\nAlém disso, pode ser vendido em:\n* [Lojas de Bebidas](businesstypes-liquorstore).\n\nO produto pode ser colocado à venda nos seguintes móveis, com base no tipo de loja.\n\n**[Casas Noturnas](businesstypes-nightclub)**:\n* [Coquetelaria (Balcão de Bebidas)](furniture-cocktailbardrinkscounter)\n* [Coquetelaria (Balcão de Bebidas em Madeira)](furniture-woodencocktailbardrinkscounter)\n\n**[Lojas de Bebidas](businesstypes-liquorstore)**:\n* [Prateleira de Bebidas](furniture-drinksshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [Importação United Ocean](address: 3 pier)", "help_itemname_martini_content": "**Martini** é um tipo de produto vendido principalmente em [Casas Noturnas](businesstypes-nightclub).\n\nAlém disso, pode ser vendido em:\n* [Lojas de Bebidas](businesstypes-liquorstore).\n\nO produto pode ser colocado à venda nos seguintes móveis, com base no tipo de loja.\n\n**[Casas Noturnas](businesstypes-nightclub)**:\n* [Coquetelaria (Balcão de Bebidas)](furniture-cocktailbardrinkscounter)\n* [Coquetelaria (Balcão de Bebidas em Madeira)](furniture-woodencocktailbardrinkscounter)\n\n**[Lojas de Bebidas](businesstypes-liquorstore)**:\n* [Prateleira de Bebidas](furniture-drinksshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [Importação United Ocean](address: 3 pier)", "help_itemname_whisky_content": "**Uísque** é um tipo de produto vendido principalmente em [Casas Noturnas](businesstypes-nightclub) and [Lojas de Bebidas](businesstypes-liquorstore).\n\nO produto pode ser colocado à venda nos seguintes móveis, com base no tipo de loja.\n\n**[Casas Noturnas](businesstypes-nightclub)**:\n* [Coquetelaria (Balcão de Bebidas)](furniture-cocktailbardrinkscounter)\n* [Coquetelaria (Balcão de Bebidas em Madeira)](furniture-woodencocktailbardrinkscounter)\n\n**[Lojas de Bebidas](businesstypes-liquorstore)**:\n* [Prateleira de Bebidas](furniture-drinksshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [Importação United Ocean](address: 3 pier)", "employee_types": "Tipos de funcionários", "help_skillname_dj_content": "Funcionários com a habilidade **DJ** são usados para boates.\n\nOs DJs tocam música e mantêm os clientes dançando e gastando mais dinheiro.\n\nO nível de habilidade (%) define por quanto tempo os clientes permanecem na boate.\n\nEles podem ser designados para:\n* [<PERSON><PERSON><PERSON>](furniture-djbooth)\n\nEles podem ser contratados em:\n* [Anderson Recruitment Corp.](address: 16 5a)", "help_skillname_hairstylist_content": "Funcionários com a habilidade **Cabeleireiro** são usados em salões de cabeleireiro.\n\nCabeleireiros ganha dinheiro com os clientes oferecendo uma variedade de tratamentos capilares:\n* [Taxa de Química / Coloração do Cabelo](fees-hairchemicalfee)\n* [Taxa de Corte do Cabelo](fees-haircuttingfee)\n* [Taxa de Lavagem & Secagem do Cabelo](fees-hairshampooingfee)\n* [Taxa de Penteado do Cabelo](fees-hairstylingfee)\n\nO nível de habilidade (%) define o quão rápido eles conseguem estilizar cada cliente.\n\nEles podem ser atribuídos em:\n* [Cadeira de Cabeleireiro](furniture-hairdresserchair)\n* [<PERSON><PERSON> de Cabeleireiro (Moderno)](furniture-hairdresserchairmodern)\n* [Lavatório de Cabeleireiro](furniture-hairdresserheadwash)\n\nEles podem ser contratados em:\n* [Anderson Recruitment Corp.](address: 16 5a)", "help_skillname_headhunter_content": "Funcionários com a habilidade **Caçador de Talentos** são utilizados em sedes.\n\nSemelhante às Agências de Emprego, os caçadores de talentos podem recrutar funcionários, com mais opções de filtros de acordo com seu nível de habilidade. Você também pode definir um número-alvo de candidatos a serem recrutados ou deixá-los recrutando continuamente até que decida interromper a busca.\n\nVocê pode gerenciar seus caçadores de talentos pelo [App Meus Funcionários](skill-myemployees) no seu [BizPhone](general-bizphone).\n\nAlém disso, é possível vincular os Caçadores de Talentos aos Gerentes de RH. Para cada funcionário sob responsabilidade desse Gerente, o Caçador de Talentos fornecerá automaticamente um substituto nas seguintes situações: contratação por um rival (de 3 a 5 dias), pedido de demissão (em até 1 dia) ou aposentadoria (imediatamente).\n\nSeu nível de habilidade (%) determina a velocidade com que pode recrutar, quantos Gerentes de RH pode supervisionar e a diferença de habilidade dos substitutos em relação aos funcionários que estão sendo substituídos.\n\nEles podem ser atribuídos em:\n* [Estação de Trabalho com Computador](furniture-computerworkstation)\n\nEles podem ser contratados em:\n* [City Workforce Inc.](address: 41 4a)", "help_skillname_securityguard_content": "Funcionários com a habilidade **Guarda de Segurança** são usados em qualquer local de varejo.\n\nGuardas de segurança gerenciam cada um 1 Armário de Guarda de Segurança e previnem ocorrências de roubo.\n\nO nível de habilidade (%) define a eficácia da segurança deles.\n\nEles podem ser designados para:\n* [Armário de Guarda de Segurança](furniture-securityguardlocker)\n\nEles podem ser contratados em:\n* [Anderson Recruitment Corp.](address: 16 5a)", "employee_management": "Gestão de funcionários", "bizphone_myemployees": "App MyEmployees", "help_bizphone_myemployees_content": "O aplicativo *Meus Funcionários* permite que você gerencie seus funcionários e candidatos.\n\n**Funcionários**\n\nVocê pode gerenciar todos os seus funcionários contratados neste menu. Aqui, é possível visualizar a idade atual, sal<PERSON>rio, níveis de habilidade, satisfação, atribuição na empresa, exigências e muito mais.\n\n**Candidatos**\n\nNesta aba, você pode gerenciar seus candidatos.\n\nVocê pode atribuí-los a um negócio, contratá-los ou rejeitar possíveis funcionários. Se você não agir antes que a oferta expire, eles serão automaticamente removidos da lista.\n\nCandidatos podem vir das seguintes fontes:\n\n* [Quadro de Empregos](furniture-jobboard)\n* [Anderson Recruitment Corp.](address: 16 5a)\n* [City Workforce Inc.](address: 41 4a)\n* [Caçador de Talentos](skill-headhunter)\n\n**Ações para Funcionários**\n\nSelecione um funcionário para obter mais detalhes sobre ele.\n\nA partir daí, você pode pagar um bônus ao funcionário, designar o negócio atual ou enviar o funcionário para treinamento.\n\n**Ações em Massa para Funcionários**\n\nVocê pode selecionar vários Funcionários ou Candidatos clicando na marca de seleção ao lado do nome deles. Pressione Shift e clique para selecionar vários funcionários de uma só vez.\n\nApós selecionar seus funcionários, você pode usar o menu suspenso no topo para aplicar uma ação a todos os funcionários selecionados.", "employee_stations": "Estações de funcionários", "itemname_computerworkstation": "Computador", "help_itemname_computerworkstation_content": "**Computador de Trabalho** é uma *estação para funcionários* especial para funcionários de escritório.\n* [Designer <PERSON><PERSON><PERSON><PERSON><PERSON>](skill-graphicdesigner)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](skill-headhunter)\n* [<PERSON><PERSON><PERSON>](skill-hrmanager)\n* [<PERSON><PERSON><PERSON>](skill-lawyer)\n* [<PERSON><PERSON><PERSON> Logís<PERSON>](skill-logisticsmanager)\n* [Representante de Compras](skill-purchasingagent)\n* [Programador](skill-programmer)\n\n<PERSON><PERSON> um<PERSON> [Mesa](furniture-deskgroup) , uma [<PERSON><PERSON>](furniture-chairgroup) e um [Computador](furniture-computergroup)\n\nPara acomodar algumas demandas dos funcionários, você pode colocar itens adicionais, como:\n* [Telefone](furniture-classicphone)\n* [Monitor de Computador](funiture-computermonitor)\n* [Mesa Digitalizadora](furniture-graphictablet)\n* [Mesa Digitalizadora (com Tela)](furniture-graphictabletwithscreen)\n* [Mouse Pad](furniture-mousepad)\n* [Telefone Comercial](furniture-officephone)", "help_itemname_djbooth_content": "**<PERSON><PERSON><PERSON> de <PERSON>** é uma *estação para funcionários* especial que requer funcionários com habilidade de [DJ](skill-dj).\n\nÉ necessário para uma casa noturna.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_hairdresserchair_content": "A **Cadeira de Cabeleireiro** é uma estação especial para funcionários com habilidade em [Cabeleireiro](skill-hairstylist).\n**Capacidade de Clientes:** 2\n\nNesta estação, os funcionários podem realizar as seguintes atividades:\n\n* [Cobrança de Produtos Químicos / Coloração Capilar](fees-hairchemicalfee)\n* [Cobrança de Corte de Cabelo](fees-haircuttingfee)\n* [Cobrança de Estilização Capilar](fees-hairstylingfee)\n\n*Observação:* se não houver [Produtos para Cuidados Capilares](products-haircareproduct) disponíveis em uma [Prateleira de Cabeleireiro](furniture-hairdressershelf), o funcionário não poderá realizar a tarefa.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_hairdresserchairmodern_content": "A **Cadeira de Cabeleireiro Moderna** é uma estação especial para funcionários com habilidade em [Cabeleireiro](skill-hairstylist).\n**Capacidade de Clientes:** 3\n\nNesta estação, os funcionários podem realizar as seguintes atividades:\n\n* [Cobrança de Produtos Químicos / Coloração Capilar](fees-hairchemicalfee)\n* [Cobrança de Corte de Cabelo](fees-haircuttingfee)\n* [Cobrança de Estilização Capilar](fees-hairstylingfee)\n\n*Observação:* se não houver [Produtos para Cuidados Capilares](products-haircareproduct) disponíveis em uma [Prateleira de Cabeleireiro](furniture-hairdressershelf), o funcionário não poderá realizar a tarefa.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_hairdresserheadwash_content": "A **Estação de Lavagem para Cabeleireiros** é uma estação especial para funcionários com habilidade em [Cabeleireiro](skill-hairstylist).\n**Capacidade de Clientes:** 5\n\nNesta estação, os funcionários podem realizar a seguinte atividade:\n\n* [Cobrança de Shampoo e Secagem de Cabelo](fees-hairshampooingfee)\n\n*Observação:* se não houver [Produtos para Cuidados Capilares](products-haircareproduct) disponíveis em uma [Prateleira de Cabeleireiro](furniture-hairdressershelf), o funcionário não poderá realizar a tarefa.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_securityguardlocker_content": "**Armário do Segurança** é uma *estação para funcionários* especial que requer funcionários com habilidade de [Segurança](skill-securityguard).\n\nPode ser usado em qualquer negócio de varejo para adicionar um alto nível de [Segurança](building-security).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "itemname_musicgroup": "Opções de música", "itemname_diningtablegroup": "Opções de mesa de jantar", "itemname_computergroup": "Opções de computador", "itemname_deskgroup": "Opções de mesa", "itemname_chairgroup": "Opções de cadeira", "itemname_sofagroup": "Opções de sofá", "help_itemname_chairgroup_content": "A **Cade<PERSON>** é necessária para configurar uma [Estação de Trabalho de Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nAs opções incluem:\n* [<PERSON><PERSON> Jogos](furniture-gamingchair)\n* [<PERSON><PERSON>](furniture-armchair2)\n* [<PERSON><PERSON> Lawn<PERSON>](furniture-eameschair)\n* [Cadeira Multipurpose](furniture-multipurposechair)\n* [<PERSON><PERSON> de Escritório](furniture-officechair)\n* [<PERSON><PERSON> Regular](furniture-regularchair)\n* [<PERSON><PERSON>mmer<PERSON>](furniture-armchair1)\n* [<PERSON>ira de Escritório Stump Mesh](furniture-officechair2)", "help_itemname_computergroup_content": "**Computador** é necessário para configurar um [Computador de Trabalho](furniture-computerworkstation).\n\nAs opções incluem:\n* [Computador Básico Gamer](furniture-gamingcomputer)\n* [Computador](furniture-computer)\n* [Computa<PERSON>](furniture-desktopcomputer)\n* [Notebook](furniture-laptop)", "help_itemname_deskgroup_content": "Uma **Mesa** é necessária para configurar uma [Estação de Trabalho](furniture-computerworkstation).\n\nAs opções incluem:\n* [Mesa de Escritório Executiva Esquerda](furniture-officedesk2left)\n* [Mesa de Escritório Executiva Direita](furniture-officedesk2right)\n* [Mesa de Escritório Padrão](furniture-officedesk1)", "help_itemname_diningtablegroup_content": "Uma **Mesa de Jantar** pode ser usada para acomodar clientes enquanto eles comem para atender a certas [Demandas dos Clientes](building-customerdemands).\n\n1 cliente pode sentar por cadeira.\n\nEla requer pelo menos uma [Cadeira](furniture-chairgroup) e uma das seguintes opções:\n* [Mesa Redonda](furniture-roundtable)\n* [Mesa Padrão](furniture-table1)\n\nAlternativamente, você pode usar pelo menos um [Banquinho de Bar](furniture-barstool) e uma [Mesa Alta](furniture-hightable).", "help_itemname_musicgroup_content": "**Alto-falantes** são necessários para reproduzir música em apartamentos e lojas para atender a certas [Demandas dos Clientes](building-customerdemands).\n\nExistem vários taman<PERSON>, desde pequenos alto-falantes que podem ser colocados em uma mesa ou montados na parede até alto-falantes grandes e luxuosos que você deixa no chão.\n\nPara atender às demandas dos clientes por música, você pode usar:\n* [Alto-falante JayBeeel 1](furniture-loudspeaker1)\n* [Alto-falante JayBeeel 2](furniture-loudspeaker2)\n* [Alto-falante JayBeeel 3](furniture-loudspeaker4)\n* [Alto-falante Clássico Grande](furniture-classicloudspeakerbig)\n* [Alto-falante Clássico Médio](furniture-classicloudspeakermedium)\n* [Alto-falante Clássico Pequeno](furniture-classicloudspeakersmall)\n* [Alto-falante P&S Peolab 90](furniture-loudspeaker3)", "help_itemname_sofagroup_content": "É possível usar qualquer **Sofá** para atender a certas [Exigências de funcionários](employees-demands-equipment). Há várias opções de sofá possíveis:\n* [Sofá modular 1 E](furniture-modularsofa1l)\n* [Sofá modular 1 M](furniture-modularsofa1m)\n* [Sofá modular 1 D](furniture-modularsofa1r)\n* [Sofá preben](furniture-cornersofa01)\n* [Sofá preben com chaise esquerdo](furniture-cornersofa02left)\n* [Sofá preben com chaise direito](furniture-cornersofa02right)", "help_itemname_barstool_content": "**Bancos de bar** podem ser usados para servir de assentos em casas noturnas. Eles podem ser anexados aos seguintes móveis:\n* [Mesa alta](furniture-hightable)\n* [Bar de coquetéis (canto)](furniture-cocktailbarcorner)\n* [Bar de coquetéis (canto de madeira)](furniture-woodencocktailbarcorner)\n* [Bar de coquetéis (prateleira de cerveja)](furniture-cocktailbarbeershelf)\n* [Bar de coquetéis (prateleira de cerveja de madeira)](furniture-woodencocktailbarbeershelf)\n* [Bar de coquetéis (balcão de bebidas)](furniture-cocktailbardrinkscounter)\n* [Bar de coquetéis (balcão de bebidas de madeira)](furniture-woodencocktailbardrinkscounter)\n\nOs móveis podem ser comprados nos seguintes locais:\n* [Eletrodomésticos essenciais](endereço:16 11s)\n* [Ika Bohag](endereço:50 4s)", "help_itemname_gamingcomputer_content": "**Computador de Jogos Básico** pode ser usado para configurar uma [Estação de Trabalho de Computador](furniture-computerworkstation).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_cheapcoffeemachine_content": "A **Máquina de Café Barata** pode ser usada para satisfazer a \"Demanda de Equipamento de Máquina de Café Barata\" [Demanda de Equipamento de Funcionário](employees-demands-equipment).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](address:50 4s)\n* [Mr. Scott's Office Supplies](address:39 4a)", "help_itemname_cocktailbar_content": "**Coquetelaria** pode ser usado para armazenar os seguintes itens:\n\n* [Caixa Registradora](furniture-cashregister)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_cocktailbarcorner_content": "**Coquetelaria (Canto de Bebidas)** pode acoplar um [<PERSON><PERSON>a](furniture-barstool) para os clientes sentarem e beberem.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_cocktailbardrinkscounter_content": "**Coquetelaria (Balcão de Bebidas)** pode ser usado para vender:\n\n* [Margarita](products-margarita)\n* [Martini](products-martini)\n* [Uísque](products-whisky)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_cocktailbarbeershelf_content": "**Coquetelaria (Prateleira de Cerveja)** pode ser usado para vender:\n\n* [Cerveja](products-beer)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_classicloudspeakerbig_content": "**Alto-Falante Clássico (Grande)** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_classicloudspeakermedium_content": "**Alto-Falante Clássico (Médio)** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_classicloudspeakersmall_content": "**Alto-Falante Clássico (Pequeno)** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_classicphone_content": "**Telefone** pode ser adicionado a um [Computador de Trabalho](furniture-computerworkstation) para satisfazer a [Exigência de Equipamento](employees-demands-equipment) por \"Telefone\".\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_itemname_desktopcomputer_content": "**Computador <PERSON>** pode ser usado para montar um [Computador de Trabalho](furniture-computerworkstation).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](address:50 4s)", "help_itemname_drinksshelf_content": "A **Prateleira de Bebidas** pode ser usada para vender:\n\n* [Cerveja](products-beer)\n* [Margarita](products-margarita)\n* [Martini](products-martini)\n* [Whisky](products-whisky)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 10\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address: 16 11s)", "help_itemname_securitycameraroof_content": "**Câmera de Segurança (Domo)** fornece um nível médio de [Segurança](building-security) para o seu edifício quando colocado no teto.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_wallmountedtv_content": "A **TV ELGE 8467NW-43283833 de 43\"** pode ser usada para assistir TV e aumentar a [Felicidade](general-happiness).\n\nO móvel pode ser adquirido nos seguintes locais:\n\n* [<PERSON><PERSON>](address:50 4s)", "help_itemname_gamingchair_content": "A **Cadeira para Jogos** pode ser usada para montar uma [Estação de Trabalho com Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](address:50 4s)", "help_itemname_armchair2_content": "A **<PERSON><PERSON> G<PERSON>mel Arm** pode ser usada para montar uma [Estação de Trabalho com Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_hairdressershelf_content": "**Prateleira de Produtos para Cabelo** pode ser usada para vender:\n\n* [Produto para Cuidado Capilar](products-haircareproduct)\n\n**Capacidade do Produto:** 40\n**Capacidade do Cliente:** 30\n\nO móvel pode ser comprado nos seguintes locais:\n* [AJ <PERSON>rson & Son](address:13 5a)", "help_itemname_hightable_content": "**Mesa Alta** pode acoplar um [<PERSON><PERSON><PERSON>](furniture-barstool) para os clientes sentarem e beberem.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_icecreamcounter_content": "**Balcão de Sorvete** pode ser usado para vender:\n\n* [Sorvete](products-icecream)\n\n**Capacidade de Produtos:** 150\n**Capacidade de Clientes:** 30\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)", "help_itemname_eameschair_content": "**<PERSON><PERSON>s** pode ser usada para montar um [Posto de Trabalho de Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Lux Concept](address:68 5a)", "help_itemname_kingsizebed_content": "**Cama King Size** pode ser usada para dormir e recuperar [Energia](general-energy).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](address:50 4s)", "help_itemname_laptop_content": "**Laptop** pode ser usado para montar uma [Estação de Trabalho de Computador](furniture-computerworkstation).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_itemname_loudspeaker1_content": "**Auto-Falante Jay<PERSON>el (Médio)** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON>ka Bohag](address:50 4s)", "help_itemname_loudspeaker2_content": "**Auto-Falante <PERSON> (Grande)** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON>ka Bohag](address:50 4s)", "help_itemname_loudspeaker4_content": "**Auto-Falante JayB<PERSON>el (Pequeno)** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bohag](address:50 4s)", "help_itemname_officephone_content": "**Telefone Comercial** pode ser adicionado a um [Computador de Trabalho](furniture-computerworkstation) para satisfazer a [Exigência de Equipamento](employees-demands-equipment) por \"Telefone Comercial\".\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Mr. Scott's Office Supplies](address:39 4a)", "help_itemname_largemeetingtable_content": "**Mesa de Reunião Grande** pode ser usada para atender à demanda por \"Mesa de Reunião Grande\" [Demanda de Equipamento de Funcionário](employees-demands-equipment).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_itemname_modularsofa1l_content": "**Modular Sofa 1 L** pode ser usado para atender à demanda por \"Qualquer Sofá\" [Demanda de Equipamento de Funcionário](employees-demands-equipment).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bo<PERSON>g](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_modularsofa1m_content": "**Modular Sofa 1 M** pode ser usado para atender à demanda por \"Qualquer Sofá\" [Demanda de Equipamento de Funcionário](employees-demands-equipment).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_modularsofa1r_content": "**Modular Sofa 1 R** pode ser usado para atender à demanda por \"Qualquer Sofá\" [Demanda de Equipamento de Funcionário](employees-demands-equipment).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_multipurposechair_content": "**Multipurpose Chair** pode ser usado para montar uma [Estação de Trabalho de Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_itemname_officechair_content": "**Cadeira de Escritorio** pode ser usado para montar uma [Estação de Trabalho de Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](address:50 4s)\n* [Mr. Scott's Office Supplies](address:39 4a)", "help_itemname_officedesk1_content": "**Mesa de escritório padrão** pode ser usado para montar uma [Estação de Trabalho de Computador](furniture-computerworkstation).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Lux Concept](address:68 5a)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_itemname_loudspeaker3_content": "**P&S Peolab 90** pode ser usado em um negócio ou em casa para ouvir música e satisfazer certas [Demandas dos Clientes](building-customerdemands) por música.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Lux Concept](address:68 5a)", "help_itemname_expensivetv_content": "**P&S Sympathy 88\"** pode ser usado para assistir TV e aumentar a [Felicidade](general-happiness).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Lux Concept](address:68 5a)", "help_itemname_palletshelf_content": "**Prateleira de Palete** é utilizada em [Armazéns](businesstypes-warehouse) para armazenar o inventário. \n\n**Capacidade de Produto:** 60 Caixas\n\nA mobília pode ser comprada nas seguintes localizações:\n* [AJ <PERSON> & Filho](address:13 5a)\n* [Depósito de Suprimentos da Fábrica](address:57 5a)", "help_itemname_pullupbar_content": "A **Barra fixa** pode ser usada em uma [Academia](businesstypes-gym) para [exercícios](common_exercise).\n\n**Capacidade de Clientes:** 2\n(a capacidade de todos os equipamentos de ginástica é combinada)\n\n*Observação:* Para usar este móvel na academia, você deve vestir a roupa de ginástica através de um [Armário da Academia](furniture-gymlockers), [Guarda-Roupa](furniture-wardrobe) ou [Cabideiro de parede](furniture-wardrobewall).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)\n* [Ika Bohag](address:50 4s)", "help_itemname_cornersofa01_content": "**Sofá Preben** pode ser usado para atender à demanda de equipamento \"Qualquer Sofá\" [Funcionários](employees-demands-equipment)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bo<PERSON>g](address:50 4s)", "help_itemname_regularchair_content": "**<PERSON><PERSON>** pode ser usado para montar um [Computador de Trabalho](furniture-computerworkstation) or [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_restaurantbooth_content": "**Restaurante Cabine** pode ser usado para acomodar os clientes enquanto eles comem, satisfazendo certas [Demandas dos Clientes](building-customerdemands).\n\n4 clientes podem se sentar por cabine, o que satisfaz a demanda deles por assentos.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_roundtable_content": "**Mesa Redonda** pode ser usada para montar uma [Mesa de Jantar] (móveis - conjunto de mesa de jantar).\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [<PERSON><PERSON>] (endereço: 50 4s)\n* [AJ <PERSON>rson & Son] (endereço: 13 5a)", "help_itemname_armchair1_content": "**Cadeira de Braço Sommerhus** pode ser usada para montar uma [Estação de Trabalho de Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON> Bohag](address:50 4s)", "help_itemname_treadmill_content": "**Esteira** pode ser usada em uma [Academia](businesstypes-gym) para [exercícios](common_exercise).\n\n**Capacidade de Clientes:** 2\n(a capacidade de clientes de todos os equipamentos de ginástica é combinada)\n\n*Observação:* Para usar este móvel na academia, você deve trocar de roupa de ginástica de um [Armário da Academia](móveis-armários), [Guarda-Roupa](móveis-guarda-roupa) ou [Parede do Guarda-Roupa](móveis-parede).\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [Eletrodomésticos Essenciais](endereço: 16 11s)\n* [Ika Bohag](endereço: 50 4s)", "help_itemname_securitypanel_content": "**Painel de Segurança** fornece um nível baixo de [Segurança](building-security) para o seu edifício quando colocado perto da entrada.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_securitycamera_content": "**Câmera de Segurança** fornece um nível médio de [Segurança](building-security) para o seu edifício quando colocado na parede.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_bed1_content": "**<PERSON><PERSON>** pode ser usada para dormir e recuperar [Energia](general-energy).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](address:50 4s)", "help_itemname_standardfridge_content": "**Geladeira <PERSON>dr<PERSON>** pode armazenar alimentos para uso pessoal. Também pode ser usada para satisfazer a demanda por \"Geladeira Padrão\" de equipamento de funcionário.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Square Appliances](address:16 4a)\n* [Ika Bohag](address:50 4s)", "help_itemname_storageshelf_content": "**Prateleira de Armazenamento** é usada em lojas de varejo para armazenar estoque. \n\n**Capacidade do Produto:** 16 Caixas\n\nOs móveis podem ser comprados nos seguintes locais:\n* [AJ Pederson & Son](endereço:13 5a)\n* [Essentials Appliances](endereço:16 11s)\n* [Factory Supply Depot](endereço:57 5a)\n* [Ika Bohag](endereço:50 4s)\n* [Square Appliances](endereço:16 4a)", "help_itemname_scorpiogamingsetup_content": "**Scorpio Gaming Setup** pode ser usado para jogar jogos de computador e ganhar [Felicidade](general-happiness).\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Lux Concept](address:68 5a)", "help_itemname_officechair2_content": "**Stump Mesh Office Chair** pode ser usada para montar uma [Estação de Trabalho de Computador](furniture-computerworkstation) ou [Mesa de Jantar](furniture-diningtablegroup).\n\nA mobília pode ser comprada nos seguintes locais:\n* [<PERSON><PERSON> Bohag](endereço:50 4s)\n* [Lux Concept](endereço:68 5a)\n* [Mr. Scott's Office Supplies](endereço:39 4a)", "help_itemname_trashbin_content": "**Trash Bins** podem ser usadas para que os clientes descartem o lixo após comerem.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ <PERSON> & Son](address:13 5a)", "help_itemname_wardrobe_content": "**Guarda-roupa** pode guardar roupas e ser usado para trocar de roupa.\n\n*Observação:* É necessário trocar de roupa para praticar exercícios antes de [exercícios](common_exercise)\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [<PERSON><PERSON> Bo<PERSON>g](endereço: 50 4s)", "help_importers": "Atacado/Importação", "wholesalers_overview": "Visão geral de atacado", "wholesalers_metro": "Metro Wholesale", "wholesalers_nydistro": "NY Distro Inc", "wholesalers_totalproduce": "Produção total", "wholesalers_shortages": "Escassez e Atrasos de encomenda", "help_wholesalers_shortages_content": "**Escassez and Atrasos** pode acontecer naturalmente de tempos em tempos ou pode ser criado artificialmente pelos [Rivais](rivals-overview) que estão competindo com você.\n\nSe um item estiver em *Escassez*, o preço desse item aumentará, mas você ainda poderá comprá-lo.\n\nSe um item estiver em *Atraso*, você não poderá comprar esse item até que o atraso termine.\n\nEm ambos os casos, a demanda dos clientes aumentará durante o período em que o item estiver em oferta limitada. Se você conseguir manter a venda desses itens sem falta de estoque durante este período poderá obter um bom aumento na renda!", "help_wholesalers_overview_content": "**Comprando de um Atacadista**\n\nPara vender produtos em sua loja, você deve primeiro comprar esses produtos de um atacadista ou importador. Visite qualquer local de atacado para comprar caixas de estoque a granel.\n\n**Entregando em Suas Lojas**\n\nPara receber entregas, primeiro certifique-se de que sua loja tenha pelo menos uma [Prateleira de Armazenamento](furniture-storageshelf).\n\nVocê pode agendar entregas de qualquer um dos atacadistas falando com o gerente no balcão da loja.\n\nDepois de fechar o contrato com o gerente, você pode gerenciar suas entregas semanais na aba Entregas do App BizMan do seu [BizPhone](general-bizphone).\n\nAtacadistas são:\n* [Metro Wholesale](wholesalers-metro)\n* [NY Distro Inc](wholesalers-nydistro)\n* [Total Produce Trading](wholesalers-totalproduce)\n* [Hudson Wholesale](address:13 12s)", "help_wholesalers_metro_content": "**Metro Wholesale** é um local de atacado.\n\nEles vendem os seguintes itens:\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](products-burger)\n* [Presente <PERSON>](products-cheapgift)\n* [Roupa Masculina Clássica Barata](products-classiccheapmaleclothing)\n* [<PERSON>mida Fr<PERSON>](products-freshfood)\n* [Comida <PERSON>gelada](products-frozenfood)\n* [<PERSON>cola de Papel](products-paperbag)\n* [Salada](products-salad)\n* [Lata de Refrigerante](products-sodacan)\n\nEles estão localizados aqui:\n* [Metro Wholesale](address:2 5a)", "help_wholesalers_nydistro_content": "**NY Distro Inc** é uma loja atacadista.\n\nA NY Distro é uma loja drive-in, o que significa que você pode carregar seu veículo com tudo o que quiser comprar, dirigir até o caixa e pagar de dentro do veículo!\n\nEles vendem os seguintes itens:\n* [Celular Arty Fish](produtos-smartphone1)\n* [Smartwatch Arty Fish](produtos-smartwatch2)\n* [Cerveja](produtos-cerveja)\n* [Garrafa de Vinho](produtos-garrafadevinho)\n* [<PERSON><PERSON><PERSON><PERSON><PERSON>](produtos-hambúrguer)\n* [Espeto de Espetinho de Frango](produtos-espetinho)\n* [Charuto](produtos-charuto)\n* [Cigarro](produtos-cigarro)\n* [Roupas (Clássicas Baratas Femininas)](produtos-roupasclássicasbaratasfemininas)\n* [Roupas (Clássicas Baratas Masculinas)](produtos-roupasclássicasbaratasmasculinas)\n* [Roupas (Clássicas Caras Femininas)](produtos-roupasclássicascarasfemininas)\n* [Roupas (Clássicas Caras Femininas) Masculino)](produtos-roupasmasculinasclássicascaras)\n* [Roupas (Feminino Moderno e Barato)](produtos-roupasfemininasmodernasbaratas)\n* [Roupas (Masculino Moderno e Barato)](produtos-roupasmasculinasmodernasbaratas)\n* [Roupas (Feminino Moderno e Caro)](produtos-roupasfemininasmodernascaras)\n* [Roupas (Masculino Moderno e Caro)](produtos-roupasmasculinasmodernascaras)\n* [Croissant](produtos-croissant)\n* [Xícara de Café](produtos-xícaradecafé)\n* [Cupcake](produtos-cupcake)\n* [Donut](produtos-donut)\n* [Flor (Barata)](produtos-florbarata)\n* [Flor (Cara)](produtos-florcara)\n* [Batata Frita](produtos-batata frita)\n* [Comida Fresca](produtos-comidafresca)\n* [Comida Congelada](produtos-comidacongelada)\n* [Presente (Barato)](produtos-presentebarato)\n* [Presente (Caro)](produtos-presentecaro)\n* [Produto para Cabelo](produtos-produtoparacabelo)\n* [Cachorro-Quente](produtos-cachorro-quente)\n* [Joias (Baratas)](produtos-joiasbaratas)\n* [Joias (Caras)](produtos-joiascaras)\n* [Margarita](produtos-margarita)\n* [Martini](produtos-martini)\n* [Fones de Ouvido Noize Boss](produtos-fones de ouvido01)\n* [Sacola de Papel](produtos-sacola de papel)\n* [Pizza](produtos-pizza)\n* [Rhythm By Tre](produtos-fones de ouvido01)\n* [Salada](produtos-salada)\n* [Lata de Refrigerante](produtos-lata de refrigerante)\n* [Guarda-chuva](produtos-guarda-chuva)\n* [Uísque](produtos-uísque)\n* [Celular ZanaMan](produtos-smartphone2)\n* [Smartwatch ZanaMan](produtos-smartwatch1)\n\nEles estão localizados aqui:\n* [NY Distro Inc](endereço: 37 1s)", "help_wholesalers_totalproduce_content": "**Total Produce Trading** é um local de atacado.\n\nEles vendem os seguintes itens:\n* [<PERSON><PERSON><PERSON>](products-apple)\n* [Banana](products-banana)\n* [<PERSON><PERSON><PERSON>](products-carrot)\n* [Flor Barata](products-cheapflower)\n* [Flor Cara](products-expensiveflower)\n* [<PERSON>mi<PERSON>](products-freshfood)\n* [Comi<PERSON>](products-frozenfood) \n* [<PERSON><PERSON>](products-lettuce)\n* [Pera](products-pear)\n* [Salada](products-salad)\n* [<PERSON><PERSON>](products-tomato)\n\nEles estão localizados aqui:\n* [Total Produce Trading](address:6 6a)", "vehicletypename_alternatetraveloptions": "Opções alternativas de deslocamento", "help_vehicletypename_alternatetraveloptions_content": "Se você não deseja comprar um carro e lidar com estacionamento e tráfego, existem algumas opções:\n\n**Táxis**\n\nTáxis circulam aleatoriamente pela cidade prontos para levá-lo aonde você desejar por uma taxa.\n\nQuando um táxi estiver próximo, clique nele e selecione o prédio para o qual você deseja ir.\n\n**Estações de Metrô**\n\nEstações de metrô estão localizadas em diversos pontos da cidade.\n\nQuando estiver perto de uma estação de metrô, clique nela e, em seguida, clique em outra estação para onde deseja viajar, mediante pagamento de uma taxa.\n\n**Patinetes Elétricos**\n\nPatinetes elétricos podem ser encontrados carregando em diferentes locais pela cidade.\n\nVocê pode alugar esses patinetes mediante pagamento de uma taxa.\n\n*Observação:* Enquanto estiver usando o patinete elétrico, você pode segurar uma sacola, mas não poderá carregar caixas ou usar carrinhos de mão ou plataformas.", "transactiontype_electricscooterfee": "Taxa de locação de patinete elétrico", "interior_installation_firm_no_designs_for_selected_address": "Não há designs disponíveis para o endereço selecionado.", "transactiontype_electricscooterfee_label": "Patinete elétrico", "interior_installation_firm_no_addresses_available": "Você não tem endereços disponíveis para prestação de serviço de {businessName}.", "business_description_installation_firm_residential": "É a sua casa. E você merece perfeição", "business_description_installation_firm_retail_warehouse": "De lojas a armazéns, designs feitos sob medida para você", "business_description_installation_firm_office": "Designs de escritório adequados à realeza", "jobdemand_hasphone_description": "O funcionário exige ter um telefone na mesa", "blueprintdata_businesstypename": "Tipo de negócio", "bizman_security_business_closed": "Fechado/Protegido", "jobdemand_hasofficephone_description": "Funcionário exige ter um telefone de escritório em sua mesa", "sortby_popularity": "Popularidade", "sortby_price": "Preço", "sortby_uploaddate": "Data do carregamento", "common_page": "Página\n{pageNo}", "interior_design_purple": "Roxo", "blueprint_notification_cannot_contain_special_gifts": "Não é possível salvar um projeto que contém presentes especiais", "loading_screen_christmasstreetdecorations": "Espí<PERSON>", "itempanelui_hud_confirm_discard_special_gift": "Tem certeza? Este é um item especial, e você não poderá recuperá-lo", "itemname_specialgift2023": "Presente especial de 2023", "bizman_presentation_hud_confirm_special_gift_inside": "Tem certeza? Há um item especial dentro que não será possível recuperar", "itemname_christmastree": "Árvore de Natal artificial pré-iluminada", "itemname_candycane": "Bengala doce decorativa gigante", "itemname_christmasgift01": "Presente de Natal especial", "itemname_christmasstockings": "<PERSON><PERSON> de <PERSON>", "itemname_christmaslights01": "<PERSON><PERSON><PERSON> de Natal", "itemname_christmasgift02": "Presente de Natal legal", "itemname_christmasgift03": "Presente de Natal caro", "itemname_christmasgift04": "Presente de Natal luxuoso", "itemname_christmasgifts01": "Conjunto de presentes de Natal pequeno", "itemname_christmasgifts02": "Conjunto de presentes de Natal médio", "itemname_christmasgifts03": "Conjunto de presentes de Natal grande", "itemname_christmaslights02": "Luzinhas de estrela de Natal", "itemname_christmaslights03": "Luzinhas de visco de Natal", "itemname_christmascookies": "Biscoitos e leite", "notification_specialgift_need_empty_hands": "Suas mãos têm que estar vazias para pegar o presente", "itemname_jackolanternsmall": "Lanterna de Abóbora Pequena Ligada", "itemname_jackolanternmedium": "Lanterna de Abóbora Média Ligada", "itemname_jackolanternbig": "Lanterna de Abóbora Grande Ligada", "streetname_twelfthstreet": "12th Street", "streetname_thirteenthstreet": "13th Street", "streetname_fourteenthstreet": "14th Street", "businesstype_gasstation": "Posto de combustível e mecânico", "businesstype_truckgarage": "Garagem de caminhões", "businesstype_irs": "Receita Federal", "businesstype_gym": "Academia", "businesstype_casino": "Cassino", "notification_cannot_open_interior_designer_in_work_station": "Você não pode abrir o designer de interiores enquanto interage com uma estação de trabalho", "cash_register_notifications_no_paperbags_needed_for_businesstype": "Este negócio não precisa de Sacos de papel para Caixas registradoras", "contacts_candidates_button": "<PERSON><PERSON><PERSON><PERSON>", "playerhud_fps_counter": "{fps} FPS", "itemname_watercooler": "<PERSON><PERSON><PERSON><PERSON>", "jobdemand_watercooler": "<PERSON><PERSON><PERSON><PERSON>", "jobdemand_watercooler_description": "O funcionário exige um bebedouro no edifício", "notification_fell_through_map": "Isso não deveria ter acontecido! Por favor, ajude-nos a melhorar o jogo relatando esse problema!", "itemname_tobaccoshelf1": "Prateleira de Tabaco (Grande)", "itemname_tobaccoshelf2": "Prateleira de Tabaco (Média)", "itemname_tobaccoshelf3": "Prateleira de Tabaco (Pequena)", "itemname_productdisplaytable": "Mesa de exposição de produto", "itemname_smartphone1": "Telefone Arty Fish", "itemname_smartphone2": "Telefone ZanaMan", "businesstype_electronicsstore": "Loja de eletrônicos", "itemname_smartwatch1": "Smartwatch ZanaMan", "itemname_smartwatch2": "Smartwatch Arty Fish", "itemname_checkoutcounterleft": "Balcão de caixa esquerdo", "itemname_officedesk2right": "Mesa de escritório executiva direita", "itemname_coatcheckright": "Guarda-volumes direito", "itemname_cornersofa02left": "Sofá preben com chaise esquerdo", "shop_sign_electronic_store_title": "Vende os eletrônicos mais recentes", "shop_sign_electronic_store_secondtitle": "Mesa de produtos", "jobdemand_seatedatofficechair": "Cadeira de escritório", "jobdemand_seatedatofficechair_description": "O funcionário exige sentar-se numa Cadeira de escritório", "jobdemand_seatedatofficedesk1": "Mesa de escritório padrão", "jobdemand_seatedatofficedesk1_description": "O funcionário exige sentar-se a uma Mesa de escritório padrão", "fridgecontroller_consume": "{word} {itemName} ({amount})", "action_consumefood": "Comer", "action_consumedrink": "<PERSON><PERSON>", "itemname_earbuds01": "Fones de ouvido <PERSON>ize <PERSON>", "itemname_headphones01": "Headphones Rhythm By Tre", "npc_expression_cannot_reach_item": "Não consigo alcançar <b>{itemname}</b>", "loading_screen_lowereastsidetemporalborder": "Construind<PERSON> parede de borda", "itemname_kabob": "Espeto de frango", "tooltip_right_click_to_remove": "Clique com o botão direito para remover", "dialog_negotiation_accepted_strange_tactic": "Tática estranha, mas aceitamos.", "dialog_negotiation_accepted_glad_you_accepted": "Estamos felizes por você ter aceitado nossa oferta.", "dialog_negotiation_declined_take_it_seriously": "Por favor, leve esta negociação a sério. Vamos tentar de novo.", "dialog_negotiation_accepted_accept_player_offer": "Aceitamos sua contraproposta.", "dialog_negotiation_counter_offer": "Gostaríamos de propor {counterOffer}.", "dialog_negotiation_final_counter_offer": "<PERSON>ão podemos ir abaixo de {counterOffer}. Esta é a nossa oferta final.", "dialog_negotiation_negotiate_counter_offer": "O que você propõe?", "dialog_negotiation_player_declined_counter_offer": "<PERSON><PERSON> muito, mas não estou mais interessado.", "dialog_negotiation_accepted_close_enough": "Parece um acordo justo, nós aceitamos.", "help_businesstype_electronicsstore_content": "**Lojas de eletrônicos** são empresas varejistas. \n\nOs clientes servem a si mesmos.\n\nPara operar, a empresa precisa dos seguintes equipamentos:\n\n* [Pilha de cestas de compras](furniture-stackofshoppingbaskets)\n* [Ponto de vendas](furniture-itemgrouppointofsale)\n* Pelo menos um produto à venda (veja abaixo)\n\nEmpresas desse tipo vendem principalmente:\n\n* [Telefone Arty Fish](products-smartphone1)\n* [Smartwatch Arty Fish](products-smartwatch2)\n* [Fones de ouvido Noize Boss](products-earbuds01)\n* [Headphones Rhythm By Tre](products-headphones01)\n* [Telefone ZanaMan](products-smartphone2)\n* [Smartwatch ZanaMan](products-smartwatch1)\n\nAlém disso, podem vender:\n\n* [Lata de refrigerante](products-sodacan)\n\nÉ possível empregar funcionários com as seguintes habilidades:\n\n* [Atendimento ao cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "help_itemname_smartphone1_content": "**Telefone Arty Fish** é um tipo de produto vendido principalmente em [Lojas de eletrônicos](businesstypes-electronicsstore).\n\nÉ possível colocar esse produto no seguinte equipamento:\n* [Mesa de exposição de produtos](furniture-productdisplaytable)\n\nÉ possível comprar o produto nestes locais:\n* [NY Distro Inc](address:37 1s)\n\nÉ possível importar o produto destes locais:\n* [JetCargo Imports](address: 1 pier)", "help_itemname_smartphone2_content": "**Telefone ZanaMan** é um tipo de produto vendido principalmente em [Lojas de eletrônicos](businesstypes-electronicsstore).\n\nÉ possível colocar esse produto no seguinte equipamento:\n* [Mesa de exposição de produtos](furniture-productdisplaytable)\n\nÉ possível comprar o produto nestes locais:\n* [NY Distro Inc](address:37 1s)\n\nÉ possível importar o produto destes locais:\n* [JetCargo Imports](address: 1 pier)", "help_itemname_smartwatch2_content": "**Smartwatch Arty Fish** é um tipo de produto vendido principalmente em [Lojas de Eletrônicos](businesstypes-electronicsstore).\n\nAlém disso, pode ser vendido em:\n* [Lojas de Presentes](businesstypes-giftshop)\n* [Joalherias](businesstypes-jewelrystore)\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [Mesa de Exposição (Produto)](furniture-productdisplaytable)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](address:37 1s)\n\nO produto pode ser importado nos seguintes locais:\n* [JetCargo Importações](address: 1 pier)", "help_itemname_smartwatch1_content": "**ZanaMan Smartwatch** é um tipo de produto vendido principalmente em [Lojas de eletrônicos](businesstypes-electronicsstore).\n\nAlé<PERSON> disso, ele pode ser vendido em:\n* [Lojas de presentes](businesstypes-giftshop)\n* [Lojas de joias](businesstypes-jewelrystore)\n\nO produto pode ser colocado nos seguintes móveis:\n* [Mesa de exposição (produto)](furniture-productdisplaytable)\n\nO produto pode ser comprado nos seguintes locais:\n* [NY Distro Inc](endereço:37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [JetCargo Imports](endereço: 1 pier)\n\nO produto pode ser fabricado usando as seguintes receitas:\n* [Receita do ZanaMan Smartwatch](recipes-smartwatch1recipe)", "help_itemname_earbuds01_content": "**Os Fones de Ouvido Noize Boss** são um tipo de produto vendido principalmente em [Lojas de Eletrônicos](businesstypes-electronicsstore).\n\nAlém disso, podem ser vendidos em [Livrarias](businesstypes-bookstore).\n\nO produto pode ser colocado nos seguintes móveis:\n* [Mesa Expositora (Produto)](furniture-productdisplaytable)\n\nO produto pode ser usado como um [Acessório de Personagem](common_accessories) para ouvir música.\nO acessório pode ser armazenado em um [Suporte para Fones de Ouvido](furniture-earbudsstand)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](endereço: 37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [JetCargo Imports](endereço: 1 pier)\n\nO produto pode ser fabricado usando as seguintes receitas:\n* [Receita dos Fones de Ouvido Noize Boss](recipes-earbuds01recipe)", "help_itemname_kabob_content": "**Espeto** é um tipo de produto vendido principalmente em [Restaurantes fast-food](businesstypes-fastfoodrestaurant).\n\nÉ possível colocar esse produto no seguinte equipamento:\n* [Grelha industrial](furniture-industrialgrill)\n\nÉ possível comprar o produto nestes locais:\n* [NY Distro Inc](address:37 1s)\n\nÉ possível importar o produto destes locais:\n* [SeaSide Internationals](address: 2 pier)", "help_itemname_tobaccoshelf1_content": "**Prateleira de Tabaco (Grande)** pode ser usada para vender:\n\n* [Cigar](products-cigar)\n* [Maço de Cigarros](products-cigarette)\n\n**Capacidade do Produto:** 600\n**Capacidade do Cliente:** 30\n\nOs móveis podem ser comprados nos seguintes locais:\n* [Essentials Appliances](endereço:16 11s)", "help_itemname_tobaccoshelf2_content": "**Prateleira de Tabaco (Média)** pode ser usada para vender:\n\n* [Cigar](products-cigar)\n* [Maço de Cigarros](products-cigarette)\n\n**Capacidade do Produto:** 500\n**Capacidade do Cliente:** 20\n\nOs móveis podem ser comprados nos seguintes locais:\n* [Essentials Appliances](endereço:16 11s)", "help_itemname_tobaccoshelf3_content": "**Prateleira de Tabaco (Pequena)** pode ser usada para vender:\n\n* [Cigar](products-cigar)\n* [Maço de Cigarros](products-cigarette)\n\n**Capacidade do Produto:** 400\n**Capacidade do Cliente:** 15\n\nOs móveis podem ser comprados nos seguintes locais:\n* [Essentials Appliances](endereço:16 11s)", "help_itemname_watercooler_content": "**Bebedouro** pode ser usado para atender a \"Bebedouro\" [Exigência de funcionário por equipamento](employees-demands-equipment). \n\nÉ possível comprar o equipamento nestes locais: \n* [<PERSON><PERSON>](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "itemname_coatcheckleft": "Guarda-volumes esquerdo", "help_itemname_coatcheckleft_content": "**Guarda-Volumes (Esquerdo)** é uma *estação para funcionários* especial que requer funcionários com habilidade de [Atendimento ao Cliente](skill-customerservice).\n\nVocê pode usar o Designer de Interiores para ajustar a fila de clientes.\n\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_coatcheckright_content": "**Guarda-Volumes (Direito)** é uma *estação para funcionários* especial que requer funcionários com habilidade de [Atendimento ao Cliente](skill-customerservice).\n\nVocê pode usar o Designer de Interiores para ajustar a fila de clientes.\n\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "itemname_cornersofa02right": "Sofá preben com chaise direito", "help_itemname_cornersofa02left_content": "**Sofá Preben com Chaise esquerdo** pode ser usado para atender a \"Qualquer sofá\" [Exigência de funcionário por equipamento](employees-demands-equipment). \n\nÉ possível comprar o equipamento nestes locais: \n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Lux Concept](address:68 5a)", "help_itemname_cornersofa02right_content": "**Sofá Preben com Chaise direito** pode ser usado para atender a \"Qualquer sofá\" [Exigência de funcionário por equipamento](employees-demands-equipment). \n\nÉ possível comprar o equipamento nestes locais: \n* [<PERSON><PERSON> Bohag](address:50 4s)\n* [Lux Concept](address:68 5a)", "itemname_officedesk2left": "Mesa de escritório executiva esquerda", "help_itemname_officedesk2left_content": "**Mesa de escritório executiva esquerda** pode ser usado para acomodar um [Computador](furniture-computerworkstation).\n\nÉ possível comprar o equipamento nestes locais: \n* [<PERSON><PERSON>](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "help_itemname_officedesk2right_content": "**Mesa de escritório executiva direita** pode ser usado para acomodar um [Computador](furniture-computerworkstation).\n\nÉ possível comprar o equipamento nestes locais: \n* [<PERSON><PERSON>g](address:50 4s)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "itemname_checkoutcounterright": "Balcão de caixa direito", "help_itemname_checkoutcounterleft_content": "**Balcão de caixa esquerdo** é uma *estação de funcionários* especial que exige funcionários com a habilidade [Atendimento ao cliente](skill-customerservice).\n\nÉ possível usar Designer de interior para ajustar a fila de clientes.\n\nPara operar, o equipamento precisa destes produtos:\n* [Saco de papel](products-paperbag)\n\n**Capacidade de clientes:** 1000\n**Capacidade de clientes:** 30\n\nÉ possível comprar o equipamento nestes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_checkoutcounterright_content": "**Balcão de caixa direito** é uma *estação de funcionários* especial que exige funcionários com a habilidade [Atendimento ao cliente](skill-customerservice).\n\nÉ possível usar Designer de interior para ajustar a fila de clientes.\n\nPara operar, o equipamento precisa destes produtos:\n* [Saco de papel](products-paperbag)\n\n**Capacidade de clientes:** 1000\n**Capacidade de clientes:** 30\n\nÉ possível comprar o equipamento nestes locais:\n* [AJ Pederson & Son](address:13 5a)", "happinessmodifiertype_gambling": "Jo<PERSON>", "happinessmodifiertype_playingvideogames": "Jogando video games", "happinessmodifiertype_watchingtv": "Assistindo TV", "happinessmodifiertype_djing": "DJing", "happinessmodifiertype_inanightclub": "Em uma boate", "happinessmodifiertype_walkinginthepark": "Caminhando no parque", "happinessmodifiertype_exercising": "Se exercitando", "notification_cant_interact_with_item_in_hand": "Você não pode interagir com {fromname} enquanto tiver {toname} em mãos", "hud_confirm_delivery_contract": "Tem certeza de que deseja cancelar este contrato de entrega?", "colors_darkblue": "A<PERSON>l escuro", "hud_confirm_return_items_to_shelf": "Tem certeza de que deseja devolver esses itens às suas prateleiras?", "hud_confirm_autofill_schedule": "Tem certeza de que deseja preencher automaticamente a programação? Isso substituirá a programação existente.", "dialog_more_help_new_order": "Nova Ordem", "credits_projectmanager": "Gestor do projeto", "notification_cant_use_while_using_item": "Você não pode usar um {fromname} enquanto carrega um {toname}", "priority_low": "Baixo", "priority_medium": "Médio", "priority_high": "Alto", "itemname_seasonalneon": "Néon sazonal", "itemname_seasonaldecoration": "Decoração sazonal", "interiordesigner_cant_use_while_driving": "Você não pode usar o Interior Designer enquanto dirige um veículo", "interiordesigner_cant_use_while_interacting_with_workstation": "Você não pode usar o Interior Designer enquanto interage com uma estação de trabalho", "loading_screen_lowermanhattan": "Baixa Manhattan", "notification_cant_open_interior_designer_in_vehicle": "Você não pode abrir o designer de interiores enquanto estiver em um veículo.", "notification_cant_open_interior_designer_while_on_workstation": "Você não pode abrir o designer de interiores enquanto interage com uma estação de trabalho", "contactcategoryname_general": "G<PERSON>", "contactcategoryname_business": "<PERSON>eg<PERSON><PERSON><PERSON>", "contactcategoryname_finance": "Financeiro", "contactcategoryname_furnitureandequipment": "Móvel / Equipamento", "contactcategoryname_importsandgoods": "Importações / Bens", "contactcategoryname_employees": "Funcionários", "contactcategoryname_rivals": "<PERSON><PERSON><PERSON>", "itemname_casinoslotmachine": "Caça-Níqueis", "itemname_casinoslotmachinechair": "Cadeira de Caça-Níqueis", "itemname_casinoblackjacktable": "Vinte e um", "itemname_casinoroulettetable": "Role<PERSON>", "itemname_specialemployeedesk": "Mesa", "itemname_healthinsurancemanagerdesk": "Mesa do Gerente de Plano de Saúde", "itemname_mrternitydesk": "Mesa do Senhor Ternity", "headhunter_amount_of_candidates": "Quantidade de Candidatos", "headhunter_recruit_continuously": "Recrutar Continuamente", "itemname_woodenSaladBar": "Buffet de Salada em Madeira", "itemname_handtruckspawner": "Gerador de Carrinho de <PERSON>ga", "itemname_flatbedspawner": "<PERSON><PERSON><PERSON>", "notification_slot_machine_is_occupied": "Este caça-níqueis está ocupado", "notification_blackjack_table_is_full": "Esta mesa de vinte e um está cheia", "notification_roulette_table_is_full": "Esta mesa de roleta está cheia", "myemployees_configure_pay_bonus": "<PERSON><PERSON><PERSON> ({bonusAmount})", "myemployees_configure_pay_bonus_tooltip_title": "<PERSON><PERSON><PERSON>", "myemployees_configure_pay_bonus_tooltip_description": "<PERSON><PERSON> bônus aos funcionários aumentará instantaneamente sua satisfação para 100%. Bônus só podem ser dados em intervalo de 30 dias.", "transactiontype_employeebonus": "Bônus de funcionário para {employee}", "transactiontype_employeebonus_label": "Bônus de Funcionário", "myemployees_mass_action_pay_bonuses_confirm": "<PERSON><PERSON> ({bonusesSum})", "myemployees_mass_action_paybonuses": "<PERSON><PERSON>", "myemployees_pay_bonus_cooldown": "Recarga: {days} dias", "bizman_insight_not_enough_data": "Sem dados suficiente", "bizman_hrmanager_upgrade_insurance_confirm": "Tem certeza de que deseja atualizar do {currentInsurance} para {newInsurance} para um total de {price} por funcionário por dia? A nova política entrará em vigor imediatamente.", "bizman_hrmanager_upgrade_insurance_button": "<PERSON><PERSON><PERSON><PERSON>", "neighborhood_lowermanhattan": "Baixa Manhattan", "dialog_bank_investment_risk_info": "{risk} (entre {min}% e {max}%)", "dialog_bank_investment_risk_info_tooltip": "Investimentos flutuarão aleatoriamente ao longo do tempo dentro desta faixa", "colors_lightred": "<PERSON><PERSON><PERSON><PERSON> claro", "colors_midnight": "<PERSON>a noite", "colors_gold": "Ouro", "streetname_eleventhstreet": "11ª Rua", "myemployees_skills_tooltip": "O nível de habilidade do funcionário indica o quão eficiente ele é em seu trabalho. Treinar um funcionário melhorará suas habilidades e eficiência, mas também aumentará seu salário por hora.", "itemname_novel": "Romance", "itemname_youngnovel": "Q<PERSON>rin<PERSON>", "itemname_motivationalbook": "Livro Motivacional", "itemname_technicalmanual": "<PERSON><PERSON>", "itemname_picturebook": "<PERSON><PERSON>", "itemname_limitededitionbook": "Livro Edição Limitada", "businesstype_bookstore": "Livraria", "itemname_computermonitor": "Monitor de Computador", "click_to_read": "Clique para ler", "entertain_panel_read_headline": "Ler livro", "entertainui_slider_label_read": "Leia livro por {entertainingHours} horas e {entertainingMinutes} minutos.\\nAumentará sua felicidade para {boostPercentage}% por {boostHours} horas", "entertain_panel_start_read_button": "Começar a ler livro", "entertainui_stop_read": "<PERSON>r de ler livro", "happinessmodifiertype_read": "Ler um livro", "local_songs_are_loading": "Carregando as músicas locais", "smartphone_rivals": "<PERSON><PERSON><PERSON>", "common_weekly_income": "<PERSON><PERSON>", "open_in_contacts": "Abrir em Contatos", "rivals_leaderboard": "Liderança", "happinessmodifiertype_wenttotheskatepark": "Foi numa pista de skate", "happinessmodifiertype_intheskatepark": "Numa pista de skate", "rivals_real_estate_properties": "Imóveis", "common_show_on_map": "<PERSON>ibir no <PERSON>", "common_business_name": "Nome da Empresa", "rivals_business_development": "Desenvolvimento de Negócios", "rivals_leaderboard_stat": "{numBusinesses} Negócios\n{weeklyIncome} por semana", "building_rival_owner_description": "{rivalName} é o dono do edifício", "business_rival_owner_description": "{rivalName} é o dono do negócio", "rivals_jessica_johnson_activation": "O que... <PERSON><PERSON><PERSON>, j<PERSON><PERSON>, eu achei que isso não ia acontecer. Eu gosto muito do <PERSON>, mas isso não vai me impedir de defender meu bairro. Saia de Hell's Kitchen. É meu.", "rivals_jessica_johnson_deactivation": "Tudo bem. Vi o que você fez, jovem. Respeito por recuar. Pode ficar tranquilo agora em Hell's Kitchen. Tudo bem? Existem muitos outros bairros.", "rivals_jessica_johnson_entrance": "<PERSON><PERSON>, jovem. <PERSON> assim que Fred sempre te chama, certo, jovem? Que bonitinho. Enfim, divirta-se com seu negócio, mas não me dê motivos para me preocupar com você, ok? Mantenha-o pequeno, jovem.", "rivals_jessica_johnson_hirebestemployees": "Fizemos algumas pesquisas ontem. Na verdade, estávamos procurando na internet quem trabalha pra você. Encontramos algumas pessoas realmente habilidosas. Talvez seja hora deles conseguirem um aumento e talvez dum novo empregador.", "rivals_jessica_johnson_lowdemand": "Acabei de ter uma ótima ideia e queria compartilhar com vocês. Se eu expandisse todo o estoque da dos meus negócios exatamente como as que você vende? Imagine o que acontecerá com a demanda. Imagine como seria difícil para você manter sua receita. É uma boa ideia, acredito.", "rivals_jessica_johnson_pricereduction": "Tive o trabalho de visitar seu negócio estúpido. Os preços devem estar gerando um bom lucro, certo? Imagine para onde seus clientes irão quando souberem dos meus preços. Beijinhos.", "rivals_jessica_johnson_productshortage": "Adivinha quem foi às compras ontem à noite? Eu. Adivinha o que eu comprei? Sim, todos os seus produtos favoritos. Sabe o que significa escassez de produto, certo? <PERSON><PERSON>, tenho certeza de que seus clientes o farão na próxima vez que for à loja de atacado.", "rivals_jessica_johnson_rentrivalbuilding": "<PERSON><PERSON><PERSON>, jove<PERSON>, você não é tão lento. <PERSON><PERSON><PERSON> alugar um dos meus edifícios para expandir seu pequeno império e competir comigo, sério? Vamos.", "rivals_jessica_johnson_surrender": "<PERSON><PERSON> bem, você venceu. <PERSON><PERSON>, você simplesmente não tem respeito, hein? Controlei Hell's Kitchen por dez anos. Então você simplesmente entra, age como dono. Até parece com o seu tio Fred. Até mais... por enquanto.", "rivals_ingrid_schneider_activation": "Realmente isto é inaceitável. Outro negócio? Você já se esqueceu quem é o dono deste bairro. Não estou procurando mais rivais. Prefiro destruí-los. Portanto, considere que é melhor encerrar seu próprio negócio.", "rivals_ingrid_schneider_deactivation": "Ahhh, sehr gut! <PERSON><PERSON>, às vezes você não sabe o que está enfrentando, até que te morda. Eu respeito que tenha mudado de ideia. Vamos continuar assim.", "rivals_ingrid_schneider_entrance": "Olá! Vejo que você abriu um negócio em nosso bairro, Garment District. <PERSON><PERSON><PERSON>, você é mais que bem-vindo aqui, mas lembre-se de quem é o dono deste bairro. Portanto, não tenha grandes ideias. Tschüss!", "rivals_ingrid_schneider_hirebestemployees": "Presumo que você esteja muito feliz com sua equipe mais qualificada, certo? <PERSON><PERSON>, mal schauen. Estou de olho neles. Então aposto que eles vão ficar muito interessados em saber o salário que estou oferecendo, comparado ao seu pequeno estabelecimento.", "rivals_ingrid_schneider_lowdemand": "Ah, sim, provavelmente está se sentindo muito inteligente abrindo seus pequenos negócios com base nas demandas, certo? Acha que não tenho capital para inundar o mercado com os produtos que você vende? Você deveria ficar de olho na demanda, mein liebling, porque talvez nem consiga ver quando eu acabar com você.", "rivals_ingrid_schneider_pricereduction": "<PERSON><PERSON>rio? Alguém acha que pode competir com meus preços? Da hast du dich aber geschnitten, mein <PERSON>. Vou reduzir meus preços e diminuir suas margens de lucro. Os clientes vão se amontoar em mim. Mach dich bereit, porque as coisas estão prestes a ficar realmente difíceis, Liebes.", "rivals_ingrid_schneider_productshortage": "Ei! <PERSON><PERSON> so sch<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>. Tenho uma surpresinha pra você. Você já visitou sua loja de atacados favorito recentemente? Se não, vai ver uma escassez repentina de todos os seus produtos favoritos. Boa sorte em obter lucro, mein <PERSON>.", "rivals_ingrid_schneider_rentrivalbuilding": "<PERSON><PERSON>, isso é ridículo. Está tentando alugar um dos meus edifícios para expandir seu empreendimento ridículo? Esqueça! Sei nicht albern mein liebling!", "rivals_ingrid_schneider_surrender": "Ah, você é tão chato. Você nunca vai desistir realmente, não é? Be<PERSON>, estou ligando para dizer que Garment District é seu, então parabéns, eu acho. Quer saber? Eu nem me importo. Meus negócios em Nova York sempre foram secundários. Meu império permanece em Berlim e espero nunca mais ter que te ver. <PERSON><PERSON> wied<PERSON>, für immer.", "rivals_thierry_laurent_moreau_activation": "Hummm! Achei que tinha dito claramente: não seja muito ambicioso em Murray Hill, compris? Achei que poderíamos continuar como bons amigos. Mas não se você estiver planejando assumir o controle do meu bairro. Espere sentir minha raiva até você fechar um de seus negócios!", "rivals_thierry_laurent_moreau_deactivation": "Mon ami, fico feliz em ver que mudou de ideia e podemos continuar bons amigos. Agora você não está mais me incomodando. Vou parar de te incomodar. Mm. Pas fait pour tout le monde. Bonne journée.", "rivals_thierry_laurent_moreau_entrance": "<PERSON><PERSON><PERSON>, mon ami, e seja bem-vindo a Murray Hill. Nós da TLM Enterprise sempre damos as boas-vindas aos novos negócios. Apenas lembre-se, não seja muito ambicioso e continuaremos bons amigos. Au revoir, a bientôt.", "rivals_thierry_laurent_moreau_hirebestemployees": "Ooh la la. Seu principal funcionário. <PERSON>u agora, hein? Eles verão a luz e saltarão do navio. Aproveite seus substitutos pouco qualificados enquanto eu seduzo sua equipe de alto nível.", "rivals_thierry_laurent_moreau_lowdemand": "Écoute. Você sabe por que consegue vender seus produtos por um preço tão alto? Du monde. Mon ami, veja, du monde. Você sabe o que acontece se eu continuar abrindo negócios semelhantes? Vendendo os mesmos produtos? Mm. Exactement. Bon voyage de monde. Bon voyage.", "rivals_thierry_laurent_moreau_pricereduction": "Vamos ver quão forte é realmente a sua conta bancária. Boa sorte em competir com os preços baixíssimos que estou oferecendo agora. Diga au revoir aos lucros, meu amigo.", "rivals_thierry_laurent_moreau_productshortage": "Ha<PERSON>hahaha! Incapaz de acompanhar a demanda, não é? Huh? Não é por acaso, pois a maioria dos produtos que você gosta de vender já estão no meu armazém. Divirta-se pagando preços excessivos nas lojas de atacado.", "rivals_thierry_laurent_moreau_rentrivalbuilding": "No no, no. Chega de alugar minhas propriedades para você, meu amigo. Considere isso um favor. Mm. E agora saia e encontre algum buraco decadente para se esconder. C'est moi qui contrôle les prime spots. Mon ami.", "rivals_thierry_laurent_moreau_surrender": "Ah, eu vejo o números. Não sou estúpido. Bravo! Bravo! Me expulsou do meu próprio bairro. De qualquer forma, nunca gostei desta cidade. Sempre sonhei em voltar para Paris e agora você finalmente me deu um motivo. E se algum dia eu te ver abrindo um food truck na França... Nem pense nisso. Au revoir.", "rivals_huang_guo_activation": "Ei! Um ato de guerra. É isso que acontece quando você continua abrindo negócios em Midtown. Está pronto para sentir a ira de <PERSON>? Deveria estar, porque é como um donkey no Kong. OK. Bom, muito bom.", "rivals_huang_guo_deactivation": "Talvez não seja tão estúpido quanto pensei inicialmente. Boa escolha recuando. Ok, bom, muito bom. Saia. Bom.", "rivals_huang_guo_entrance": "Ah, seja muito bem-vindo meu amigo. Bem-vindo à Midtown. Este é o bairro de maior prestígio da cidade de Manhattan. E nós da Guo Investments altamente recomendamos que evitemos quaisquer conflitos de interesse. Entende? Isso significa saber quem comanda as coisas na cidade. OK. Bom, muito bom.", "rivals_huang_guo_hirebestemployees": "Ah, meu colega. Hoje tenho alguns planos interessantes. Estou planejando enviar uma mensagem a todos os seus funcionários mais qualificados. Vou mandar uma mensagem para eles dizendo: Olá. Você quer o mesmo emprego, mas um salário muito melhor? Sim? Bom.", "rivals_huang_guo_lowdemand": "<PERSON><PERSON><PERSON>, meu amigo. <PERSON> o <PERSON> falando. Agora ele é o cara que vende todos os produtos que você também vende. E não me importo com a enorme queda na demanda. Mas tenho certeza que você se importará. OK. Criancinha, criancinha. Tchau. Bom.", "rivals_huang_guo_pricereduction": "Oh, olá meu amigo. Apenas um aviso rápido. OK. Temos um grande desconto em todas as minhas lojas e vendemos tudo o que você vende. Assim como canta os passarinhos: <PERSON><PERSON><PERSON>, bala<PERSON>. Mais balato. Bom.", "rivals_huang_guo_productshortage": "Sim. Sabe o que é realmente ruim para os negócios? Margens de lucro baixas. É por isso que minha equipe está percorrendo toda a cidade de Manhattan e comprando todos os produtos que você vende. Portanto, você pode esperar preços de atacado muito mais altos. OK, bom. Balance a cabeça para cima e para baixo. Bom.", "rivals_huang_guo_rentrivalbuilding": "Você quer alugar um dos meus edifícios no meu bairro, hein? Que tal eu te emprestar meu carro? Ou quer pegar minha esposa emprestada, hein? Ou talvez queira pegar meus sapatos emprestados. Oh, espere. Você não sobreviveria um dia. Principalmente com minha esposa. Esqueça tudo isso.", "rivals_huang_guo_surrender": "Ainda não entendo como isso aconteceu. Você conhece alguém da prefeitura? Do governo? Aposto que sim. Seu, trapaceiro! Vou voltar para Pequim e nem pense em me visitar, ok? Não é bom. Não é muito bom.", "marketinsider_lowest_market_price": "Menor Preço de Mercado", "common_sales_price": "Preços de venda", "itemname_jobboard": "Quadro de Empregos", "itemname_stackofbooks": "<PERSON><PERSON><PERSON>", "itemname_bookswithbookends": "Livros com Suporte para Livros", "itemname_decorativepallet01": "Palete Decorativa 1", "itemname_decorativepallet02": "Palete Decorativa 2", "itemname_decorativepallet03": "Palete Decorativa 3", "itemname_decorativepalletwithboxes01": "Palete Decorativa Com Caixas 1", "itemname_decorativepalletwithboxes02": "Palete Decorativa Com Caixas 2", "itemname_decorativepalletwithboxes03": "Palete Decorativa Com Caixas 3", "itemname_hangingsignhigh": "Placa Suspensa Alta", "rival": "Rival", "rivals_poach_employee_offer": "<PERSON><PERSON>, chefe. Não é fácil dizer isso, mas recebi uma oferta muito boa para trabalhar para {rivalName} e estou considerando muito a possibilidade. No entanto, se você conseguir igualar o meu salário por hora para {amount}, ficarei feliz em continuar com você. Tenho {days} dias para fazer esta decisão.", "rivals_poach_employee_accept": "Excelente! Estou ansioso para continuar trabalhando para você!", "rivals_poach_employee_decline": "É decepcionante ouvir essa decis<PERSON>, mas seguirei em frente! O<PERSON><PERSON> e adeus!", "rivals_poach_employee_expired": "<PERSON><PERSON>, como não recebi sua resposta vou interpretar isso como um sinal do universo de que é hora de continuar minha jornada em outro lugar.", "rivals_poach_employee_rival_surrender": "Ouvi dizer que {rivalName} está fora do mercado, então decidi continuar trabalhando para você. Mas eu não me importaria com esse aumento.", "rivals_poach_employee_raise_accepted": "<PERSON><PERSON>, eu não esperava que você realmente me desse aquele aumento! Estou muito feliz em continuar trabalhando para você. <PERSON><PERSON> obrigado!", "rivals_poach_employee_raise_declined": "<PERSON>u a pena tentar, mas estou feliz por poder continuar trabalhando para você. Te vejo no trabalho!", "notification_cannot_rent_building_owned_by_rival": "O proprietário do edifício, seu Rival {name}, recusou sua oferta do aluguel do edifício", "bizman_presentation_building_price": "Preço do imóvel: {price}", "bizman_presentation_daily_rent": "<PERSON><PERSON><PERSON>: {dailyRent}", "bizman_presentation_deposit": "Caução - o depósito caução será devolvido ao final do contrato", "bizman_presentation_electrical_appliances": "Eletrodomésticos - o custo dos eletrodomésticos inclusos no edifício", "bizman_presentation_first_day_rent": "Primeiro Dia de Aluguel - o pagamento do primeiro dia de aluguel", "bizman_presentation_total_initial_payment": "Pagamento total inicial: {amount}", "bizman_presentation_total_initial_payment_tooltip": "A soma do depósito caução, eletrodomésticos e do primeiro dia de aluguel", "jobdemand_hascomputermonitor": "Monitor de Computador", "rivals_jessica_johnson_hirebestemployees2": "Treinou alguns novos funcionários pra mim? <PERSON><PERSON><PERSON>, jovem. Parece que é hora de enviar mais algumas ofertas de negócios.", "rivals_jessica_johnson_lowdemand2": "Tem um gosto tão bom para lojas, querido, que tive que copiar novamente! Espero que isso não lhe cause muita dor.", "rivals_jessica_johnson_pricereduction2": "Ouvi dizer que está novamente cobrando demais dos seus clientes. <PERSON>elo menos, comparado com meus novos preços. Boa sorte.", "rivals_jessica_johnson_productshortage2": "Oops, fiz novamente. Acidentalmente comprei alguns de seus produtos favoritos. Tenho certeza que você pode lidar com outra escassez de produto!", "rivals_ingrid_schneider_hirebestemployees2": "Acho que está na hora de você conversar com alguns de seus funcionários novamente. Parece que eles estão interessados em finalmente abandonar sua pequena tentativa de comprar uma loja!", "rivals_ingrid_schneider_lowdemand2": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, você está bem confortável? Permita-me corrigir isso. Descobri o que vender na minha nova loja - seus best-sellers, é claro!", "rivals_ingrid_schneider_pricereduction2": "Está aproveitando das margens de lucro, Schätz<PERSON>? <PERSON><PERSON>, estou baixando os preços novamente, então você provavelmente perderá seus clientes novamente. Tão triste.", "rivals_ingrid_schneider_productshortage2": "Tenho ótimas notícias! Acabei de fazer outra maratona de compras. Não deixe de visitar a loja de atacado para aproveitar a escassez que criei!", "employee_contact_complaint_low_satisfaction": "Meus pedidos tem sido ignorados até agora. Honestamente, estava planejando sair em breve, porém {rivalName} me fez uma oferta que inclui minhas exigências, então decidi deixar {businessName} imediatamente.", "employee_contact_complaint_low_skill": "{rivalName} me ofereceu um treinamento gratuito se eu trabalhar para eles. Se estiver disposto a me dar um treinamento nas próximas 24 horas nada vai mudar. <PERSON><PERSON><PERSON> contr<PERSON>, sairei de {businessName}.", "employee_contact_complaint_low_no_task": "Parece que você não tem nenhum trabalho ou treinamento no momento para eu fazer e {rivalName} me ofereceu um emprego. Se você não me der uma tarefa em 24 horas irei aceitar a oferta.", "employee_contact_complaint_unfulfilled_demands": "Minha exigência de {jobDemandName} não foi atendida. {rivalName} me prometeu atender se eu for trabalhar para ele. Vou aguardar 72 horas para ver se algo muda, caso contr<PERSON>, vou sair de {businessName}.", "the_company": "a empresa", "rivals_thierry_laurent_moreau_hirebestemployees2": "<PERSON><PERSON>, bonjour, petit. Enquanto está ocupado lendo esta mensagem, estou roubando seus melhores funcionários novamente.", "rivals_thierry_laurent_moreau_lowdemand2": "Loja impressionante que você tem no meu bairro. Posso dizer que está se sentindo competitivo, mon ami, então abri a mesma loja sua!", "rivals_thierry_laurent_moreau_pricereduction2": "Haha! Os clientes estão reclamando do seus preços novamente? A culpa pode ser minha - agora os seus preços parecem muito altos em comparação com os meus.", "rivals_thierry_laurent_moreau_productshortage2": "Espero que seus armazéns estejam cheios! <PERSON><PERSON>o contr<PERSON><PERSON>, você poderá ficar sem alguns dos produtos favoritos de seus clientes, já que acabei de comprar todo o estoque!", "rivals_defense_icons_active_rivalry_title": "Rivalidade Ativa", "rivals_defense_icons_active_rivalry_description": "Existe uma rivalidade ativa entre você e {rivalName}", "rivals_defense_icons_hire_best_employees_title": "Contratação dos melhores funcionários - {days} dia(s) restante(s)", "rivals_defense_icons_hire_best_employees_description": "{rivalName} está roubando seus funcionários", "rivals_defense_icons_product_shortage_title": "Esca<PERSON>z de produto - {days} dia(s) restante(s)", "rivals_defense_icons_product_shortage_description": "{rivalName} causou uma escassez nos seus produtos mais vendidos", "rivals_defense_icons_product_backorder_title": "Atraso de produto - {days} dia(s) restante(s)", "rivals_defense_icons_product_backorder_description": "{rivalName} causou um atraso nos seus produtos mais vendidos", "rivals_defense_icons_low_demand_title": "<PERSON>manda reduzida", "rivals_defense_icons_low_demand_description": "{rivalName} abriu {numBusinesses} novos negócios em {neighborhood} diminuindo a demanda", "rivals_defense_icons_price_reduction_title": "Redução de preço - {days} dia(s) restante(s)", "rivals_defense_icons_price_reduction_description": "{rivalName} reduziu os preços dos seus produtos mais vendidos para roubar seus clientes", "common_affected_items": "<PERSON><PERSON> afetados", "itemname_singlebookshelf": "<PERSON><PERSON><PERSON> (Pequena)", "itemname_decorativebookshelf": "<PERSON><PERSON><PERSON> (Pequena Decorativa)", "itemname_largebookshelf": "<PERSON><PERSON><PERSON> (Grande)", "itemname_largedecorativebookshelf": "<PERSON><PERSON><PERSON> (Grande Decorativa)", "itemname_woodencocktailbar": "Coquetelaria (em Madeira)", "itemname_woodencocktailbarcorner": "Coquetelaria (Canto em Madeira)", "itemname_woodencocktailbarbeershelf": "Coquetelaria (Prateleira de Cerveja em Madeira)", "itemname_woodencocktailbardrinkscounter": "Coquetelaria (Balcão de Bebidas em Madeira)", "itemname_largewoodencocktailbar": "Coquetelaria (Grande em Madeira)", "itemname_largecocktailbar": "Coquetelaria (Grande)", "wholesalers_hudson": "Hudson Wholesale", "help_wholesalers_hudson_content": "**Hudson Wholesale** é um local de venda por atacado.\n\nEles vendem os seguintes itens:\n* [Livro Edição Limitada](products-limitededitionbook)\n* [Livro Motivacional](products-motivationalbook)\n* [Romance](products-novel)\n* [Livro Ilustrado](products-picturebook)\n* [Livro Técnico](products-technicalmanual)\n* [Quadrinhos](products-youngnovel)\n\nEles estão localizados aqui:\n* [Hudson Wholesale](address:13 12s)", "help_itemname_computermonitor_content": "**Monitor de Computador** pode ser adicionado a uma [Computador Estação de Trabalho](furniture-computerworkstation) para satisfazer o \"Monitor de Computador\" [Exigência de Equipamento de Funcionário](employees-demands-equipment).\n\nA mobília pode ser comprada nos seguintes locais:\n* [Mr. Scott's Office Supplies](address:39 4a)\n* [<PERSON><PERSON> Bohag](address:50 4s)", "help_itemname_picturebook_content": "**<PERSON><PERSON>** é um tipo de produto vendido principalmente em [Livrarias](businesstypes-bookstore).\n\n<PERSON>é<PERSON> disso, pode ser vendido em:\n* [Lojas de Presentes](businesstypes-giftshop).\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> (Pequena)](furniture-singlebookshelf)\n* [<PERSON><PERSON><PERSON> (Grande)](furniture-largebookshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [Hudson Wholesale](address:13 12s)\n\nO produto pode ser importado nos seguintes locais:\n* [BlueStone Importações](address: 4 pier)", "help_itemname_youngnovel_content": "**Quadrinhos** é um tipo de produto vendido principalmente em [Livrarias](businesstypes-bookstore).\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> (Pequena)](furniture-singlebookshelf)\n* [<PERSON><PERSON><PERSON> (Grande)](furniture-largebookshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [Hudson Wholesale](address:13 12s)\n\nO produto pode ser importado nos seguintes locais:\n* [BlueStone Importações](address: 4 pier)", "help_itemname_novel_content": "**Romance** é um tipo de produto vendido principalmente em [Livrarias](businesstypes-bookstore).\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> (Pequena)](furniture-singlebookshelf)\n* [<PERSON><PERSON><PERSON> (Grande)](furniture-largebookshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [Hudson Wholesale](address:13 12s)\n\nO produto pode ser importado nos seguintes locais:\n* [BlueStone Importações](address: 4 pier)", "help_itemname_technicalmanual_content": "**Livro Técnico** é um tipo de produto vendido principalmente em [Livrarias](businesstypes-bookstore).\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> (Pequena)](furniture-singlebookshelf)\n* [<PERSON><PERSON><PERSON> (Grande)](furniture-largebookshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [Hudson Wholesale](address:13 12s)\n\nO produto pode ser importado nos seguintes locais:\n* [BlueStone Importações](address: 4 pier)", "help_itemname_motivationalbook_content": "**Livro Motivacional** é um tipo de produto vendido principalmente em [Livrarias](businesstypes-bookstore).\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> (Pequena)](furniture-singlebookshelf)\n* [<PERSON><PERSON><PERSON> (Grande)](furniture-largebookshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [Hudson Wholesale](address:13 12s)\n\nO produto pode ser importado nos seguintes locais:\n* [BlueStone Importações](address: 4 pier)", "help_itemname_limitededitionbook_content": "**Livro Edição Limitada** é um tipo de produto vendido principalmente em [Livrarias](businesstypes-bookstore).\n\nO produto pode ser colocado à venda nos seguintes móveis:\n* [<PERSON><PERSON><PERSON> (Pequena)](furniture-singlebookshelf)\n* [<PERSON><PERSON><PERSON> (Grande)](furniture-largebookshelf)\n\nO produto pode ser comprado nos seguintes locais:\n* [Hudson Wholesale](address:13 12s)\n\nO produto pode ser importado nos seguintes locais:\n* [BlueStone Importações](address: 4 pier)", "help_itemname_singlebookshelf_content": "**Estante (Pequena)** pode ser usado para vender:\n\n* [Livro Edição Limitada](products-limitededitionbook)\n* [Livro Motivacional](products-motivationalbook)\n* [Romance](products-novel)\n* [Livro Ilustrado](products-picturebook)\n* [Livro Técnico](products-technicalmanual)\n* [Quadrinhos](products-youngnovel)\n\n**Capacidade de Produtos:**\n* Livro Ilustrado & Quadrinhos: 90\n* Romance, Livro Técnico, Livro Motivacional, Livro Edição Limitada: 60\n**Capacidade de Clientes:** 20\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)", "help_itemname_largebookshelf_content": "**<PERSON><PERSON><PERSON> (Grande)** pode ser usado para vender:\n\n* [Livro Edição Limitada](products-limitededitionbook)\n* [Livro Motivacional](products-motivationalbook)\n* [Romance](products-novel)\n* [<PERSON>ro Ilustrado](products-picturebook)\n* [Livro Técnico](products-technicalmanual)\n* [Quadrinhos](products-youngnovel)\n\n**Capacidade de Produtos:**\n* Livro Ilustrado & Quadrinhos: 270\n* Romance, Livro Técnico, Livro Motivacional, Livro Edição Limitada: 180\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ <PERSON>rson & Son](address:13 5a)", "help_businesstype_bookstore_content": "**Livrarias** são negócios de varejo.\n\nOs clientes são egoístas.\n\nO negócio precisa dos seguintes móveis para funcionar:\n\n* [<PERSON><PERSON><PERSON> de Cestas de Compras](furniture-stackofshoppingbaskets)\n* [Ponto de Venda](furniture-itemgrouppointofsale)\n* Pelo menos um produto para vender (veja abaixo)\n\nNegócios deste tipo vendem principalmente:\n\n* [Livro de Edição Limitada](products-limitededitionbook)\n* [Livro Motivacional](products-motivationalbook)\n* [Romance](products-novel)\n* [Livro Ilustrado](products-picturebook)\n* [Manual Técnico](products-technicalmanual)\n* [Romance Juvenil](products-youngnovel)\n\nE também pode vender:\n\n* [Presentes (Baratos)](products-cheapgift)\n* [Flor (Barata)](products-cheapflower)\n* [Noize Boss Fones de ouvido](products-earbuds01)\n* [Guarda-chuva](products-guarda-chuva)\n\nFuncionários com as seguintes habilidades podem ser alocados:\n\n* [Atendimento ao Cliente](skill-customerservice)\n* [Limpeza](skill-cleaning)", "common_readbooks": "<PERSON><PERSON>", "help_itemname_woodencocktailbar_content": "**Coquetelaria (em Madeira)** pode ser usado para armazenar os seguintes itens:\n\n* [<PERSON>aixa Registradora](furniture-cashregister)\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_woodencocktailbarcorner_content": "**Coquetelaria (Canto de Bebidas em Madeira)** pode acoplar um [<PERSON>queta](furniture-barstool) para os clientes sentarem e beberem.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_woodencocktailbardrinkscounter_content": "**Coquetelaria (Balcão de Bebidas em Madeira)** pode ser usado para vender:\n\n* [Margarita](products-margarita)\n* [Martini](products-martini)\n* [Uísque](products-whisky)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "help_itemname_woodencocktailbarbeershelf_content": "**Coquetelaria (Prateleira de Cerveja em Madeira)** pode ser usado para vender:\n\n* [Cerveja](products-beer)\n\n**Capacidade de Produtos:** 50\n**Capacidade de Clientes:** 50\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "employee_skill": "Habilidade / Treinamento do Funcionário", "help_employee_skill_content": "O *nível de habilidade* do seu funcionário impacta no desempenho profissional e Atendimento ao Cliente.\n\n**Treinamento**\n\nHabilidade dos funcionários pode ser aumentada por meio de treinamento.\n\n* **Treinamento Manual** - treinar o funcionário no app [MyEmployees](skill-myemployees) do seu [BizPhone](general-bizphone). Isso aumenta a habilidade mais rapidamente, mas exige que o funcionário não seja atribuído para sua posição durante o treinamento. O treinamento termina às 17:00 (5pm) do dia seguinte ao envio para treinamento.\n* **Treinamento Automático pelo Gerente de RH** - se o seu funcionário for atribuído a um [Gerente de RH](skill-hrmanager), eles aumentarão automaticamente suas habilidades lentamente a cada dia. O funcionário continua trabalhando durante este treinamento.\n* **Treinamento no Trabalho** - enquanto seu funcionário trabalha, ele ganhará habilidades muito lentamente com o tempo, por meio de treinamento natural no trabalho. Esta é a maneira mais lenta de aumentar a habilidade.\n\n**Remunerações**\n\nÀ medida que seus funcionários aumentam em habilidades, seus salários também aumentam.", "rivals_title": "<PERSON><PERSON><PERSON>", "rivals_overview": "Visão dos Rivais", "help_rivals_overview_content": "**A Rivalidade Começa**\n\nVocê pode ser novo na cidade, mas ela já está repleta com 20 rivais experientes nos negócios que não aceitarão muito bem que você entre em seu território!\n\nVocê enfrentará dois tipos de rivais:\n* [Riva<PERSON>ec<PERSON>](special-rivals)\n* [Rivais Secundá<PERSON>](minor-rivals)\n\nConforme você constrói seus negócios, espere que os rivais lancem diversos tipos diferentes de ataques para tentar retardar e atrapalhar seu progresso. Mostre a eles que você veio para ficar e declare seu domínio sobre a Cidade de Nova York!\n\n**App Rivais**\n\nAcompanhe e monitore as ações dos seus rivais no App Rivais do seu [BizPhone](general-bizphone)", "special_rivals": "<PERSON><PERSON><PERSON>", "help_special_rivals_content": "Cada Distrito tem o seu próprio *Rival Especial*:\n* **<PERSON>** em Garment District\n* **<PERSON>** em Hell's Kitchen\n* **<PERSON><PERSON><PERSON>** em <PERSON>\n* **<PERSON>** em Midtown.\n\nEsses rivais tentarão sabotar seu negócio à medida que você cresce. Um rival ativo não permitirá nem mesmo que você alugue dos seus edifícios!\n\n**Ataques de Rival Especial**\n* Preços Reduzidos - o rival irá baixar os preços de alguns itens que você vende, forçando você a baixar os preços ou perder clientes.\n\n* Demanda Reduzida - o rival iniciará novos negócios iguais aos seus, para diminuir a demanda dos produtos que você está vendendo.\n\n* Roubo de Funcionários - o rival pode oferecer aos seus melhores funcionários um salário ainda melhor. Você terá a oportunidade de melhorar o salário se quiser mantê-lo.", "minor_rivals": "<PERSON><PERSON><PERSON>", "help_minor_rivals_content": "Os rivais secundários são diferentes em cada jogada, e estão distribuídos pelos 5 distritos.\n\n**Ataques dos Rivais Secundários**\n\nEsses rivais não mirarão você tão direta e intensamente quanto os rivais especiais, mas ainda podem causar dor, pois podem oferecer melhores condições de trabalho aos seus funcionários.\n\nPara evitar perder seus funcionários para esses rivais:\n* Atenda às demandas dos funcionários\n* Treine sua habilidade primária\n* Mantenha-os atribuídos a uma tarefa.\n\nE, claro, nunca é demais dar aos seus funcionários um bônus no app [MyEmployees](skill-myemployees) do seu [BizPhone](general-bizphone)!", "help_itemname_securitysign_content": "**Placa de Segurança** reduz ligeiramente o roubo em suas lojas. Isso não acrescenta número  para sua [Segurança](building-security), mas pendurar uma placa em sua loja ajuda a diminuir o valor dos roubos.\n\nO móvel pode ser adquirido nos seguintes locais:\n* [Essentials Appliances](address:16 11s)", "currency_suffix_thousand": "K", "currency_suffix_million": "M", "currency_suffix_billion": "B", "currency_suffix_trillion": "T", "common_primary_neighborhood": "<PERSON><PERSON>", "awaiting_replacement": "(aguardando substituição)", "menu_options_others_number_formatting": "Use a formatação de número do sistema operacional", "itemname_woodencocktailbarcornerroof": "Coquetelaria (Canto do Telhado em Madeira)", "itemname_woodencocktailbarroof": "Coquetelaria (Telhado em Madeira)", "awaiting_replacement_from_headhunter": "Aguardando substituição pelo recrutador: {headhunterName}", "rivals_huang_guo_hirebestemployees2": "Ah – você deve conhecer os melhores recrutadores! Obri<PERSON> pelo seu árduo trabalho no treinamento desses funcionários que estarei roubando. <PERSON><PERSON> bom.", "rivals_huang_guo_hirebestemployees3": "Sempre soube que você era inteligente. Sabia que eu precisava de mais funcionários e os preparou pra mim. Vou ligar para eles imediatamente! Bom.", "rivals_huang_guo_lowdemand2": "Mal posso esperar para você conhecer minha nova loja! <PERSON><PERSON> vende as mesmas coisas suas! Sei que isso te deixa preocupado com a demanda do produto, e isso me faz sorrir! <PERSON><PERSON> bom.", "rivals_huang_guo_lowdemand3": "<PERSON><PERSON><PERSON> minha falta, não é? Eu também senti tanto a sua falta que abri uma loja igual à sua! <PERSON><PERSON>, agora poderá ver minha nova loja fazer mais dinheiro, e eu ver a sua perder clientes!", "rivals_huang_guo_pricereduction2": "Ouviu as boas novas, meu amigo? Está na hora de outro desconto especial do Huang - qualquer coisa que você vende, eu posso vender mais balato! Ok, agola você chora. Bom.", "rivals_huang_guo_pricereduction3": "Poderia passar uma mensagem aos seus clientes? Diga-lhes: \"Não precisa comprar caro aqui! Vá para Huang com preços mais balatos!\". <PERSON><PERSON><PERSON>, amigo! Bom!", "rivals_huang_guo_productshortage2": "<PERSON>u amigo, está preocupado com os atrasos nos pedidos que acabei de criar? Não me preocuparia com isso... porque tenho bastante. Você, ao contr<PERSON><PERSON>, deveria estar preocupado. Bom", "rivals_huang_guo_productshortage3": "Meu caro amigo, um conselho: não peça estoque esta semana. Minhas prateleiras estão cheias dos seus itens favoritos, então eles estarão atrasados novamente para você. Mmm bom.", "headhunter_started_searching_for_a_replacement": "Re<PERSON>ru<PERSON><PERSON> come<PERSON> a procurar um substituto.", "headhunter_insufficient_funds": "Recrutador não conseguiu procurar um substituto devido a falta de fundos.", "gasstation_part_repair_header": "Oficina <PERSON>", "gasstation_part_repair_second_line": "<PERSON><PERSON><PERSON> até a garagem para consertar o veículo", "gasstation_part_refuel_header": "Posto de Combustível", "gasstation_part_refuel_second_line": "Abasteça o veículo ou compre Galão", "headhunter_expected_completion_days_range": "Re<PERSON>ru<PERSON><PERSON> come<PERSON><PERSON> a procurar um substituto. Tempo previsto para conclusão: {startingDay}-{days} dias", "headhunter_expected_completion_1_day": "Recrutador começou a procurar um substituto. Tempo previsto para conclusão: 1 dia", "rivalry_activated": "Rivalidade ativada", "rivalry_deactivated": "Rivalidade desativada", "impacted_products": "Produtos afetados: {products}", "rivals_businesses_opened": "Aberto: {amount} {businessType}", "rivals_attempting_to_poach": "Tentativa de roubo: {amount} funcionários", "importers_contract": "Contrato de Importação", "help_importers_contract_content": "**Contrato de Importação**\n\nPrimeiro, atribua o [Representante de Compras](skill-purchasingagent) na agenda da sua [Sede](businesstypes-headquarters). Depois poderá visitar um dos importadores nos píers para firmar um contrato entre eles e seu Representante de Compras para [importar estoque](importers-overview) em seus [armazéns](businesstypes-warehouse).\n\nCada Representante de Compras pode ter um contrato com um importador por vez.\n\nLocais de Importação:\n* [Jet Cargo Importações](importers-jetcargo)\n* [Seaside Internationals](importers-seaside)\n* [Importação United Ocean](importers-unitedocean)\n* [BlueStone Importações](importers-bluestone)\n\nOs preços de importação de estoque são baseados em:\n* Nível de habilidade do Representante de Compra\n* Índice de Preços de Importação, encontrado no App MarketInsider do seu [BizPhone](general-bizphone).\nOs preços atuais do Índice de Importação ficam bloqueados ao firmar um contrato.", "logistics_overview": "Visão Geral da Entrega no Armazém", "help_logistics_overview_content": "**Entregando para Suas Lojas**\n\nCada [Gestor de Logística](skill-logisticsmanager) pode gerenciar o estoque de um armazém para várias lojas ou outros armazéns no App BizMan do seu [BizPhone](general-bizphone).\n\nPara realizar com sucesso suas entregas às 02:00 (2 AM):\n* Você precisa de um veículo no [armazém](businesstypes-warehouse)\n* Um [Motorista de Entrega](skill-deliverydriver) atribuído ao veículo\n* A loja precisa de pelo menos um [Prateleira de Armazenamento](furniture-storageshelf)\n\nO número de locais onde cada Gestor de Logística pode entregar é baseado em:\n* Habilidade do Gestor de Logística\n* Quantos veículos/slots existem no armazém\n* Tipo de veículos de entrega", "contacts_rival_button": "Ver Rival", "business_description_appliance_store_3": "Se precisar, nós temos.", "business_description_wholesalestore_4": "Atacado na hora. Essa é a Promessa Hudson.", "business_description_furniture_store2": "Conceitos em classe alta merecem qualidade luxuosa.", "transactiontype_entrancefee_label": "Entrada", "bizman_marketing_no_contacts": "Você pode visitar os Escritórios de Marketing para pegar seus contatos.", "bankdialog_notification_select_investment_fund": "Selecione um fundo de investimento", "bizman_deliveries_no_contracts": "Você não tem nenhum contrato de entrega para este negócio. Contate um gerente de uma loja de atacado para estabelecer uma.", "itemname_printer": "Impressora", "itemname_walllight01": "Luz de Parede (Domo)", "itemname_walllight02": "Luz de Parede (Alongado)", "business_description_truck_garage": "Precisa de reparos no caminhão? Nós podemos ajudar!", "inventory_pricing_sold_breakdown": "{amount} vendido a {price}", "inventory_pricing_sold_breakdown_in_service": "{amount} usado no serviço", "rivals_leaderboard_defeated": "DERROTADO", "itemname_displaytable": "Tabela de Exibição", "business_description_furniture_store3": "Sua fábrica necessita de melhorias? Nós temos tudo o que você precisa!", "experimental_build_warning_title": "Versão Experimental", "experimental_build_warning_description": "Jogos salvos e progresso não são compatíveis com a versão Original", "itemname_metalshelf": "Prateleira Metálica", "itemname_decorativecarwheels": "Decoração de Rodas de Carro", "itemname_stackofjerrycans01": "Pilha de Latas do Jerry 1", "itemname_stackofjerrycans02": "Pilha de Latas do Jerry 2", "itemname_smalldecorativebox": "Caixa Decorativa Pequena", "itemname_mediumdecorativebox": "Caixa Decorativa Média", "itemname_largedecorativebox": "Caixa Decorativa Grande", "subwaystation_hellskitcheneaststation": "Hell's Kitchen - Estação Leste", "subwaystation_hellskitchenweststation": "Hell's Kitchen - Estação Oeste", "subwaystation_murrayhilleaststation": "Murray Hill - Estação Leste", "subwaystation_murrayhillsouthstation": "Murray Hill - Estação Sul", "subwaystation_murrayhillnorthstation": "Murray Hill - Estação Norte", "subwaystation_midtownnorthweststation": "Midtown - Estação Noroeste", "subwaystation_lowermanhattancenterstation": "Baixa Manhattan - Estação Central", "credits_voiceactors": "<PERSON><PERSON><PERSON>", "main_menu_custom_game_rivals_difficulty_multiplier": "Multiplicador dos ataques Rivais", "main_menu_custom_game_rivals_difficulty_multiplier_tooltip": "Isto afetará a gravidade dos ataques Rivais. Deixe 0 para desativar todos os ataques Rivais", "jobdemand_hascomputermonitor_description": "Funcionário exige ter um monitor de computador extra na mesa", "topbar_money_tooltip": "Total: {totalValue}\nOntem: {yesterdayValue}", "bizman_presentation_show_employees": "Mostrar funcionários", "bizman_presentation_rival_employees_list": "Funcionários que trabalham em {businessName}", "counterattack_rivals": "Contra-ataques", "help_counterattack_rivals_content": "É hora de lutar contra os rivais!\n\n**Contra-ataques**\n* Preços Reduzidos - se você reduzir o preço de venda de itens que os rivais também vendem, todos os rivais naquela vizinhança serão forçados a baixar também os seus preços e perderão parte da sua renda semanal. E se você continuar assim por algumas semanas, também verá a valorização das lojas deles caindo!\n\n* Demanda Reduzida - você pode abrir novos negócios que seus rivais também administram, a fim de diminuir a demanda pelos produtos que vendem, o que também diminuirá sua renda semanal.\n\n* Roubo de Funcionários - você pode encontrar os funcionários de cada rival listados na página BizMan da empresa deles. Aqui você pode tentar roubar os funcionários de um rival negociando um salário melhor para eles. Se você conseguir roubá-los, o rival será forçado a substituí-los por um funcionário inferior, diminuindo a renda semanal da loja.", "contacts_salary_negotiation_start": "<PERSON><PERSON><PERSON> por me considerar para a vaga! Vamos discutir o salário deste trabalho.", "dialog_candidate_salary_offer_hourly_wage": "<PERSON><PERSON><PERSON> por hora: <b>{price}</b>", "dialog_candidate_salary_offer_signing_bonus": "Bônus de contratação: <b>{price}</b>", "dialog_candidate_salary_offer_input_hourly_wage": "<PERSON><PERSON><PERSON> por hora:", "dialog_candidate_salary_offer_input_signing_bonus": "Bônus de contratação:", "dialog_candidate_salary_player_offer": "Proponho um salário por hora de {amount} e um bônus de contratação de {amount2}.", "dialog_candidate_salary_declined_player_offer": "Esta é uma oferta muito ofensiva. Cansei de perder meu tempo aqui. Adeus.", "dialog_candidate_salary_counter_offer": "{employeeName} ofereceu {amount} por hora.", "dialog_candidate_salary_accepted_player_offer": "Acho que é um salário justo, vou aceitar, obrigado! Estou ansioso para começar.", "building_customersovertime": "Clientes ao Longo do Tempo", "help_building_customersovertime_content": "O gráfico **Clientes ao Longo do Tempo** mostra quantos clientes entraram em sua loja em um determinado período de tempo.\n\nCompare essas informações com a [Capacidade de Clientes](building-limit) do edifício. Elas podem ser usadas para ajudar a determinar o melhor horário para abrir, se você deve fechar em determinados dias da semana ou se precisa mais de marketing!\n\nEste gráfico possui dois modos, clique nos botões no canto superior direito do gráfico para alternar entre eles:\n* **Ontem** - fornece um detalhamento hora a hora dos clientes no dia de ontem\n* **7 Dias** - oferece uma visão geral diária dos clientes dos últimos 7 dias\n\n**Nota:** Tenha em mente que só porque os clientes vêm à loja não significa que eles estejam comprando coisas. Seus preços podem ser muito altos ou você pode não ter o que eles procuram disponível.", "businesstype_notimplemented": "Em Construção. Em Breve!", "bizman_notification_invalid_business_name_characters": "Não é possível usar caracteres inválidos no nome da empresa", "itemname_decorativepapers01": "Papéis Decorativos 1", "itemname_decorativepapers02": "Papéis Decorativos 2", "itemname_irscountera": "Balcão de IRS A", "itemname_irscounterb": "Balcão de IRS B", "itemname_irscounterc": "Balcão de IRS C", "itemname_irssign": "Sinal do IRS", "itemname_rug01": "Tapete Sunburst", "itemname_rug02": "Tapete Regal", "itemname_rug03": "Tapet<PERSON> Em<PERSON>", "itemname_rug04": "<PERSON><PERSON><PERSON>", "itemname_rug05": "Tapet<PERSON>", "itemname_inputstation": "Estação de comando", "itemname_conveyorbelt": "Corrente transportadora", "itemname_automatedbakingmachine": "Forno Automatizado para Confeitaria", "itemname_outputstation": "Estação de saída", "itemname_packagingmachine": "Máquina Embaladora", "itemname_foodcuttingmachine": "Máquina de Cortar Alimentos", "itemname_foodassemblymachine": "Máquina de Montagem de Alimentos", "subwaystation_garmentdistrictweststation": "Garment District - Estação Oeste", "vehicletypename_deliverytruck": "Vord Courier D500", "itemname_burgerbun": "<PERSON><PERSON>", "itemname_cookedpatty": "<PERSON><PERSON><PERSON><PERSON><PERSON> G<PERSON>", "itemname_grilledsausage": "<PERSON><PERSON><PERSON>", "itemname_hotdogbun": "Pão de Cachorro-Quente", "itemname_icecreammixture": "Mistura para Sorvete", "itemname_laminateddough": "<PERSON><PERSON>", "itemname_plaincupcake": "Cupcake Simples", "itemname_plaindonut": "Donut Simples", "itemname_rawfries": "Batatas Fritas Cruas", "itemname_rawpizza": "Pizza Crua", "itemname_slicedcheese": "<PERSON><PERSON><PERSON>", "itemname_slicedlettuce": "<PERSON><PERSON>", "itemname_slicedonions": "Cebola Cortada", "itemname_slicedpepper": "<PERSON><PERSON><PERSON>rtado", "itemname_slicedtomato": "<PERSON><PERSON> Cortado", "itemname_tomatopaste": "Pasta de Tomate", "itemname_ketchup": "<PERSON><PERSON><PERSON>", "itemname_dressing": "<PERSON><PERSON><PERSON> para Salada", "itemname_bakingmix": "Mistura para Bolos", "itemname_butter": "Manteiga", "itemname_cheese": "Queijo", "itemname_cream": "Creme de Leite", "itemname_grilledchicken": "<PERSON><PERSON><PERSON>", "itemname_dough": "<PERSON><PERSON>", "itemname_groundbeef": "<PERSON><PERSON>", "itemname_chickenbreast": "<PERSON>eito de Frango", "itemname_marinade": "Marinada", "itemname_milk": "Leite", "itemname_onion": "Cebola", "itemname_pepper": "Pimento", "itemname_russetpotatoes": "Bat<PERSON><PERSON>", "itemname_sugar": "<PERSON><PERSON><PERSON><PERSON>", "itemname_vinaigrette": "Vinagrete", "itemname_rawsausage": "<PERSON><PERSON><PERSON>", "itemname_frosting": "Cobertura", "bizman_add_plan": "Adicionar <PERSON>", "bizman_logisticsmanagers_delete_delivery_plan": "Excluir Plano de Entrega", "bizman_logisticsmanagers_not_assigned": "Gerente de Logística não designado", "bizman_logisticsmanagers_not_assigned_description": "Designar um Gerente de Logística para gerenciar este armazém e seus planos de entrega.", "common_remove": "Remover", "bizman_delete_plan_confirm": "Você realmente deseja excluir este plano?", "bizman_logisticsmanager_lower_skill_confirm": "Este Gerente de Logística não tem habilidades suficientes para gerenciar tantas entregas. Os locais de entrega adicionais não receberão entregas.", "bizman_delete_contract": "Excluir contrato", "bizman_purchasingagent_change_confirm": "Os preços de compra serão ajustados com base no nível de habilidade deste Agente de Compras.", "bizman_purchasingagents_not_assigned": "Agente de Compras não designado.", "bizman_purchasingagents_not_assigned_description": "Designar um Agente de Compras para gerenciar este contrato de importação.", "bizman_purchasingagents_one_time_delivery_header": "Entrega única", "bizman_purchasingagents_repeating_delivery_header": "Auto-reabastecimento", "bizman_hrmanagers_cant_handle_all_employees_confirm": "Este Gerente de RH não tem habilidades suficientes para supervisionar tantos funcionários. Os funcionários adicionais serão desassociados.", "bizman_hrmanagers_cant_handle_health_insurance_confirm": "Este Gerente de RH não tem habilidades suficientes para este plano de saúde. O plano de saúde será cancelado.", "bizman_hrmanagers_cant_handle_all_employees_and_health_insurance_confirm": "Este Gerente de RH não tem habilidades suficientes para supervisionar tantos funcionários ou para manter este plano de saúde. Os funcionários adicionais serão desassociados e o plano de saúde será cancelado.", "bizman_headhunter_fewer_points_confirm": "Este Recrutador não tem habilidades suficientes para realizar este recrutamento. As configurações de recrutamento serão redefinidas.", "bizman_headhunter_cant_handle_hrmanagers_confirm": "Este Recrutador não tem habilidades suficientes para supervisionar tantos Gerentes de RH. Os Gerentes de RH adicionais serão desassociados deste Plano de Recrutamento.", "bizman_headhunter_fewer_points_and_cant_handle_hrmanagers_confirm": "Este Recrutador não tem habilidades suficientes para supervisionar tantos Gerentes de RH ou para manter essas configurações de recrutamento. Os Gerentes de RH adicionais serão desassociados e as configurações de recrutamento serão redefinidas.", "bizman_delete_plan": "Excluir plano", "bizman_headhunter_not_assigned": "Recrutador não designado", "bizman_headhunter_not_assigned_description": "Designar um Recrutador para alterar essas configurações e gerenciar os funcionários designados.", "bizman_headhunter_unassigned_hr_plan_name": "Plano não atribuído {number} ({headquartersName})", "bizman_hrmanager_not_assigned": "Gerente de RH não designado", "bizman_hrmanager_not_assigned_description": "Designar um Gerente de RH para alterar essas configurações e gerenciar os funcionários designados.", "interior_designer_color_design": "Design de Cor", "interior_designer_security_rating": "Classificação de Segurança {amount}", "interior_designer_security_coverage": "Cobertura de Segurança", "interior_designer_no_producers_in_building": "Não há prateleiras neste edifício.", "interior_designer_queue_designer": "Designer <PERSON> Filas de Clientes", "interior_designer_queue_description": "Clique para adicionar ou editar pontos na fila", "interior_designer_no_point_of_sales_in_building": "Não há pontos de venda neste edifício.", "business_description_moving_company": "Mudança facilitada, sempre. Mude com confiança.", "dialog_moving_company_npc_name": "Agente de Serviço de Mudanças", "dialog_moving_service_start": "<PERSON><PERSON><PERSON> e bem-vindo à Manhattan Movers. Como posso ajudá-lo hoje?", "dialog_moving_service_settings_header": "Moving Settings", "dialog_moving_service_origin_address": "De", "dialog_moving_service_destination_address": "<PERSON><PERSON>", "dialog_moving_service_day": "Dia da mudança", "dialog_moving_service_fee": "Taxa de Mudança", "dialog_moving_service_fee_per_item": "Taxa por item", "dialog_moving_service_cant_move_in_non_rented_building": "<PERSON><PERSON><PERSON>, tínhamos uma mudança planejada para o prédio em {businessName}, mas parece que o prédio não está mais registrado em seu nome. Cancelamos a mudança.", "dialog_moving_service_cant_move_while_inside_building": "Não conseguimos realizar a mudança em {businessName} devido a atividades inesperadas no local. Tentaremos novamente amanhã.", "dialog_moving_service_not_enough_money": "<PERSON><PERSON><PERSON>, parece que não conseguimos cobrar o custo da mudança de {businessName} para {businessName2}. Cancelamos o pedido. Por favor, certifique-se de que possui fundos suficientes para nossos serviços.", "transactiontype_movingservice": "Serviço de Mudança por {businessName}", "transactiontype_movingservice_label": "Serviço de Mudança", "businesstype_movingservice": "Serviço de Mudança", "specialbuilding_movingservice": "Serviço de Mudança", "dialog_moving_service_contract_set_player": "Gostaria que o endereço {businessName} fosse transferido para {businessName2} no dia {day}", "dialog_moving_service_contract_set_manager": "<PERSON><PERSON> obri<PERSON>. Iniciaremos a mudança no dia {day}. Por favor, note que os endereços não estarão acessíveis até a meia-noite.", "dialog_moving_service_select_moving_day": "Selecione um dia de mudança", "dialog_moving_service_accept_sell_items_in_building": "Você aceita que venderemos todos os itens existentes no endereço de destino e subtrai-los do seu preço total?", "moving_service_notification_select_origin_address": "Selecione o endereço de origem", "moving_service_notification_select_destination_address": "Selecione o endereço de destino", "dialog_moving_service_done": "Estamos felizes em informar que acabamos de mover o último item interior de {businessName} para {businessName2}. Ambos os edifícios estão novamente acessíveis.\n\nObrigado por usar {businessName3}!", "dialog_moving_service_no_addresses_available": "Nenhum endereço disponível", "businesstype_factory": "Fábrica", "itemoverlay_input": "Entrada", "itemoverlay_output": "<PERSON><PERSON><PERSON>", "itemoverlay_production_speed": "Velocidade: <b>{itemsPerHour}</b> itens por hora", "itemoverlay_inventory": "Estoque: {amount}", "bizman_menu_factory": "Fábrica", "bizman_factory_output_stations": "Estações de Saída", "bizman_factory_running": "Em execução", "bizman_factory_not_running": "Parado", "bizman_factory_production_flow_title": "Fluxo de Produção", "bizman_factory_state_title": "Estado", "bizman_factory_state_label_running": "Em execução", "bizman_factory_state_label_not_running": "Parado", "bizman_factory_state_label_not_running_missing_inventory": "Parado (falta de estoque)", "bizman_factory_state_label_not_running_inventory_full": "Parado (estoque cheio)", "bizman_factory_state_label_not_running_no_employee": "Parado (sem funcionário)", "bizman_factory_production_time_per_product_title": "Tempo de produção por produto", "bizman_factory_production_time_per_product_label": "{minutes} minutos", "bizman_factory_total_production_per_hour_title": "Produção total por hora", "bizman_factory_total_production_per_hour_label": "{amount} produtos", "bizman_factory_material_item": "<PERSON><PERSON>", "bizman_factory_material_hourly_consumption": "Consumo por hora", "bizman_factory_material_daily_consumption": "<PERSON><PERSON><PERSON>", "bizman_factory_material_in_stock": "Em estoque", "bizman_factory_material_runs_out_in": "Acaba em", "bizman_factory_material_consumption_title": "Consumo de material", "itemname_conveyorbeltsplitter": "Divisor de Correia Transportadora", "interior_designer_cannot_apply_colors": "Não foi possível aplicar as cores selecionadas,", "menu_options_data_deletion_requested": "Exclusão de dados solicitada", "menu_options_others_allow_tracking": "<PERSON><PERSON><PERSON> anônimo de jogo", "menu_options_others_request_data_deletion": "Solicitação de Exclusão de Dados", "main_menu_welcome_tracking": "Ajude-nos a melhorar o jogo permitindo o rastreamento anônimo da gameplay", "main_menu_data_tracking": "Rastreamento de dados", "main_menu_data_tracking_description": "Ajude-nos a melhorar o jogo permitindo o rastreamento anônimo do gameplay. Você pode alterar o rastreamento do gameplay a qualquer momento no menu de opções.", "itemname_hardeningtunnel": "Túnel de Endurecimento", "itemname_automatedfryingmachine": "Máquina de Fritura Automatizada", "itemname_batchpasteurizer": "Pasteurizador em Batelada", "skillname_factoryworker": "Operário de Fábrica", "itemname_conveyorfoodgrill": "Grelhador de Alimentos com Transportadora", "restui_start_button": "Começar a descansar", "restui_stop_button": "Parar de descansar", "restui_bench_headline": "Descansar no banco", "restui_chair_headline": "Descansar na cadeira", "restui_slider_label": "Descansar por {restHours} horas, {restMinutes} minutos.\nParará de descansar em {time}", "click_to_rest": "Clique para descansar", "bench": "Banco", "common_reject": "<PERSON><PERSON><PERSON><PERSON>", "common_allow": "<PERSON><PERSON><PERSON>", "notification_confirm_shut_down_business": "Tem certeza de que deseja encerrar este negócio?", "employee_name_with_skill": "{name} (Habilidade: {skillValue}%)", "itemname_bench01": "Banco", "interior_designer_no_custom_colors": "Este item não possui cores personalizadas configuradas ainda!", "interior_designer_producer_panel": "<PERSON><PERSON><PERSON> de Produtos", "itemoverlay_click_to_expand": "Clique para expandir", "itemoverlay_employee_details_demand": "<PERSON><PERSON><PERSON>", "itemname_cupcakebatter": "Massa de Cupcake", "contacts_mark_all_as_read_button": "Marcar tudo como lido", "contacts_delete_all_button": "Apagar tudo", "contacts_delete_all_button_confirm": "Isso excluirá todos os contatos visíveis.", "contacts_schedule_demands": "Demandas de horário", "contacts_wage_value": "{wage}/h", "interiordesigner_hand_tool": "Ferramenta de Mão", "interiordesigner_eyedropper_tool": "<PERSON><PERSON><PERSON>", "interiordesigner_palette_tool": "Ferramenta Paleta de Cores", "interiordesigner_sell_tool": "Ferramenta de venda", "interiordesigner_wall_tool": "Ferramenta de parede", "interiordesigner_floor_tool": "Ferramenta de chão", "interiordesigner_producer_tool": "Ferramenta de seleção de produtos", "interiordesigner_queue_tool": "Ferramenta de fila", "interiordesigner_security_tool": "Ferramenta de segurança", "interiordesigner_blueprint_tool": "Ferramenta de projeto", "interiordesigner_timeofday_tool": "Ferramenta de hora do dia", "menu_options_graphics_hbao": "Oclusão ambiental", "building_movingservice": "Serviços de mudança", "help_building_movingservice_content": "**Manhattan Movers** pode mover qualquer residencial, varejo, escritório, armazém ou fábrica para outro local de tamanho similar ou maior!\n\nApós concordar com seus serviços por uma taxa base, mais uma taxa por item movido, a mudança será configurada para o dia que você selecionar.\n\n<PERSON>esse dia, ambos os negócios ficarão inacessíveis até que a mudança seja concluída. Quaisquer itens no edifício de destino serão vendidos e o preço será deduzido do total ou reembolsado a você se o total exceder o valor devido.\n\nVisite o agente no escritório [Manhattan Movers] (endereço: 15 12s) para configurar sua mudança.", "help_businesstype_factory_content": "**Fábricas** são usadas para fabricar produtos criados a partir de ingredientes crus. Elas são colocadas em prédios de depósito.\n\nAs fábricas exigem os seguintes móveis para funcionar:\n\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n* Pelo menos uma outra Máquina de fábrica.\n\nAs fábricas podem fabricar muitos dos itens que você pode vender em suas lojas. Produtos crus podem ser importados para sua fábrica com seu [Agente de compras](skill-purchasingagent).\n\nA distribuição de produtos manufaturados pode ser gerenciada por seu [Gerente de logística](skill-logisticsmanager).\n\nO negócio exige pelo menos um veículo para fazer entregas. Ele pode usar qualquer veículo, embora veículos maiores sejam mais eficazes.\n\nFuncionários com as seguintes habilidades podem ser designados:\n\n* [Trabal<PERSON>or de fábrica](skill-factoryworker)\n* [Motorista de entrega](skill-deliverydriver)\n* [Limpeza](skill-cleaning)", "help_skillname_factoryworker_content": "Funcionários com a habilidade **Trabalhador de Fábrica** são usados para [Fábricas](businesstypes-factory).\n\nCada Trabalhador de Fábrica é designado para uma Máquina de Fábrica na Fábrica e irá operar essa máquina.\n\nEles podem ser designados para:\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\nEles podem ser contratados de:\n* [Anderson Recruitment Corp.](endereço: 16 5a)", "common_factoryrecipes": "Receitas para Fábricas", "recipes_burgerrecipe": "<PERSON><PERSON><PERSON>", "help_recipes_burgerrecipe_content": "**Receita de Hambúrguer** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes crus necessários:**\n\n* [<PERSON><PERSON><PERSON>](products-cheese)\n* [<PERSON>a](products-dough)\n* [<PERSON>ne moída](products-groundbeef)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de panificação automatizada](furniture-automatedbakingmachine)\n* [Grelha de alimentos transportadora](furniture-conveyorfoodgrill)\n* [Máquina de montagem de alimentos](furniture-foodassemblymachine)\n* [Máquina de corte de alimentos](furniture-foodcuttingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [Pão de hambúrguer](products-burgerbun)\n* <PERSON><PERSON>r [Hambúrguer cozido](products-cookedpatty)\n* Criar [Fatias Queijo](products-slicedcheese)\n\n* Crie [Hambúrguer](products-hambúrguer) na [Máquina de Montagem de Alimentos](furniture-foodassemblymachine)", "recipes_kabobrecipe": "Receita de Espeto de Frango", "help_recipes_kabobrecipe_content": "**Receita de Espeto de Frango** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes crus necessários:**\n\n* [Peito de frango](products-chickenbreast)\n* [Cebola](products-onion)\n* [Pimenta](products-pepper)\n* [Vinagrete](products-vinaigrette)\n\n**Máquinas de fábrica necessárias:**\n\n* [Grelha de alimentos transportadora](furniture-conveyorfoodgrill)\n* [Máquina de montagem de alimentos](furniture-foodassemblymachine)\n* [Máquina de corte de alimentos](furniture-foodcuttingmachine)\n* [Máquina de mistura industrial](furniture-industrialblendingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [Frango grelhado](products-grilledchicken)\n* <PERSON><PERSON>r [Marinada](products-marinade)\n* <PERSON>rie [Cebolas Fatiadas](products-slicedonions)\n* Crie [Pimentão Fatiado](products-slicedpepper)\n\n* Crie [Espeto de Espeto de Frango](products-kabob) na [Máquina de Montagem de Alimentos](furniture-foodassemblymachine)", "recipes_croissantrecipe": "<PERSON><PERSON><PERSON> de Croissant", "help_recipes_croissantrecipe_content": "**Receita de Croissant** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [Creme](products-cream)\n* [Massa](products-dough)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de panificação automatizada](furniture-automatedbakingmachine)\n* [Máquina de mistura industrial](furniture-industrialblendingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [Manteiga](products-butter)\n* Criar [Massa laminada](products-laminateddough)\n\n* Criar [Croissant](products-croissant) na [Máquina de panificação automatizada](furniture-automatedbakingmachine)", "recipes_cupcakerecipe": "<PERSON><PERSON>ita de Cupcake", "help_recipes_cupcakerecipe_content": "**Receita de Cupcake** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [Mistura para assar](products-bakingmix)\n* [Leite](products-milk)\n* [Açúcar](products-sugar)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de assar automatizada](furniture-automatedbakingmachine)\n* [Máquina de montagem de alimentos](furniture-foodassemblymachine)\n* [Máquina de mistura industrial](furniture-industrialblendingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [Massa para cupcake](products-cupcakebatter)\n* Criar [Cupcake simples](products-plaincupcake)\n* Criar [Cobertura](products-frosting)\n\n* Criar [Cupcake](products-cupcake) na [Montagem de alimentos Máquina](máquina de montagem de móveis e alimentos)", "recipes_donutrecipe": "<PERSON><PERSON>ita de Donut", "help_recipes_donutrecipe_content": "**Receita de Donut** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [<PERSON><PERSON>](products-dough)\n* [Leite](products-milk)\n* [<PERSON><PERSON><PERSON>car](products-sugar)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de fritura automatizada](furniture-automatedfryingmachine)\n* [Máquina de montagem de alimentos](furniture-foodassemblymachine)\n* [Máquina de mistura industrial](furniture-industrialblendingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Donut simples](products-plaindonut)\n* Crie [Cobertura](products-frosting)\n\n* Crie [Donut](products-donut) na [Máquina de montagem de alimentos](furniture-foodassemblymachine)", "recipes_frenchfriesrecipe": "Receita de Batatas Fritas", "help_recipes_frenchfriesrecipe_content": "**Receita de Batata Frita** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes Crus Obrigatórios:**\n\n* [Batatas Russet](products-russetpotatoes)\n\n**Máquinas de Fábrica Obrigatórias:**\n\n* [Máquina de Fritura Automatizada](furniture-automatedfryingmachine)\n* [Máquina de Corte de Alimentos](furniture-foodcuttingmachine)\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Batatas Fritas Cruas](products-rawfries)\n\n* Crie [Batatas Fritas](products-frenchfries) na [Máquina de Fritura Automatizada](furniture-automatedfryingmachine)", "recipes_hotdogrecipe": "Receita de Cachorro <PERSON>nte", "recipes_icecreamrecipe": "<PERSON><PERSON><PERSON> de Sorvete", "help_recipes_icecreamrecipe_content": "**Receita de Sorvete** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [Creme](products-cream)\n* [Leite](products-milk)\n* [Açúcar](products-sugar)\n\n**Máquinas de Fábrica Necessárias:**\n\n* [Pasteurizador de Lote](furniture-batchpasteurizer)\n* [Túnel de Endurecimento](furniture-hardeningtunnel)\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Mistura de Sorvete](products-icecreammixture)\n\n* Crie [Sorvete](products-icecream) no [Túnel de Endurecimento](furniture-hardeningtunnel)", "recipes_pizzarecipe": "Receita de Pizza", "recipes_saladrecipe": "Receita de Salada", "help_recipes_saladrecipe_content": "**Salad Recipe** pode ser fabricado em uma [Factory](businesstypes-factory).\n\n**Ingredientes crus necessários:**\n\n* [Saco de alface](products-rawlettuce)\n* [Cebola](products-onion)\n* [Pimenta](products-pepper)\n* [Saco de tomates](products-rawtomato)\n* [Vinagrete](products-vinaigrette)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de montagem de alimentos](furniture-foodassemblymachine)\n* [Usinagem de corte de alimentos](furniture-foodcuttingmachine) \n* [Máquina de mistura industrial](furniture-industrialblendingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [Molho](products-dressing)\n* Criar [<PERSON><PERSON>](products-slicedlettuce)\n* Criar [Cebolas Fatiadas](products-slicedonions)\n* Criar [Tomate Fatiado](products-slicedtomato)\n\n* Criar [Salada](products-salada) na [Máquina de Montagem de Alimentos](furniture-foodassemblymachine)", "recipes_classiccheapfemaleclothingrecipe": "Re<PERSON>ita de Roupa (Clássica Barata Feminina)", "help_recipes_classiccheapfemaleclothingrecipe_content": "**Re<PERSON>ita de Roupas (Clássicas Baratas Femininas)** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [<PERSON><PERSON><PERSON> (Barato)](products-fabriccheap)\n\n**Máquinas de Fábrica Necessárias:**\n\n* [Máquina de Corte a Laser](furniture-lasercuttingmachine)\n* [Máquina de Costura Industrial](furniture-industrialsewingmachine)\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Corte de Tecido (Clássico Barato)](products-cutfabricclassiccheap)\n\n* Crie [Roupas (Clássicas Baratas Femininas)](products-classiccheapfemaleclothing) na [Máquina de Costura Industrial](furniture-industrialsewingmachine)", "recipes_classiccheapmaleclothingrecipe": "Receita de Roupa (Clássica Barata Masculina)", "help_recipes_classiccheapmaleclothingrecipe_content": "**Re<PERSON><PERSON> de Roupas (Masculino Clássico Barato)** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [<PERSON><PERSON><PERSON> (Barato)](products-fabriccheap)\n\n**Máquinas de Fábrica Necessárias:**\n\n* [Máquina de Corte a Laser](furniture-lasercuttingmachine)\n* [Máquina de Costura Industrial](furniture-industrialsewingmachine)\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Corte Tecido (Masculino Clássico Barato)](products-cutfabricclassiccheap)\n\n* Crie [Roupas (Masculino Clássico Barato)](products-classiccheapmaleclothing) na [Máquina de Costura Industrial](furniture-industrialsewingmachine)", "recipes_classicexpensivefemaleclothingrecipe": "<PERSON><PERSON><PERSON> R<PERSON>pa (Clássica Cara Feminina)", "help_recipes_classicexpensivefemaleclothingrecipe_content": "**A receita de roupas (clássicas caras femininas)** pode ser fabricada em uma [fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [<PERSON><PERSON><PERSON> (caro)](products-fabricexpensive)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de corte a laser](furniture-lasercuttingmachine)\n* [Máquina de costura industrial](furniture-industrialsewingmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Corte de tecido (clássicas caras femininas)](products-cutfabricclassicexpensive)\n\n* Crie [roupas (clássicas caras femininas)](products-classicexpensivefemaleclothing) na [máquina de costura industrial](furniture-industrialsewingmachine)", "recipes_classicexpensivemaleclothingrecipe": "<PERSON><PERSON><PERSON> de roupa (clássica cara masculina)", "recipes_moderncheapfemaleclothingrecipe": "<PERSON><PERSON><PERSON>pas (Modernas e Baratas Femininas)", "recipes_cheapjewelryrecipe": "Re<PERSON>ita de Jóias (Baratas)", "recipes_headphones01recipe": "<PERSON><PERSON>ita Ritmo por Tre", "help_recipes_earbuds01recipe_content": "**Receita de fones de ouvido Noize Boss** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [Capacitores](products-capacitors)\n* [Laminado revestido de cobre](products-coppercladlaminate)\n* [Circuitos integrados](products-integratedcircuits)\n* [Microfone](products-microphone)\n* [Plástico](products-plastic)\n* [Resistores](products-resistors)\n* [Alto-falante](products-speaker)\n* [Transistores](products-transistors)\n\n**Máquinas de fábrica necessárias:**\n\n* [Máquina de montagem de bens de consumo](furniture-consumergoodsassemblymachine)\n* [<PERSON>áquina de forno](furniture-kilnmachine)\n* [Estação de entrada](furniture-inputstation)\n* [Estação de saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [<PERSON><PERSON><PERSON><PERSON> (Pequeno)](products-smallaudiomodule)\n* Criar [Placa de circuito (pequeno)](products-smallcircuitboard)\n* Criar [Fones de ouvido moldados](products-moldedearbuds)\n\n* Criar [Fones de ouvido Noize Boss](products-earbuds01) na [Máquina de montagem de bens de consumo](furniture-consumergoodsassemblymachine)", "help_recipes_cigarrecipe_content": "**Receita de Charuto** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [<PERSON><PERSON>ru<PERSON>](products-cigarpaper)\n* [Tabaco](products-tobacco)\n\n**Máquinas de Fábrica necessárias:**\n\n* [Máquina de Enrolar Tabaco](furniture-tobaccorollingmachine)\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\n**Receita**\n\n* Crie [Charuto](products-cigar) na [Máquina de Enrolar Tabaco](furniture-tobaccorollingmachine)", "recipes_coffeerecipe": "Receita de café", "recipes_beerrecipe": "Receita de Cerveja", "help_recipes_martinirecipe_content": "**Receita de Martini** pode ser produzida em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [Cevada](produtos-cevada)\n* [Bagas de Zimbro](produtos-bagas de Zimbro)\n* [Vermute](produtos-vermute)\n* [Água](produtos-água)\n* [Fermento](produtos-fermento)\n\n**Máquinas de Fábrica Necessárias:**\n\n* [Máquina de Engarrafamento](móveis-máquina de engarrafamento)\n* [Máquina de Mistura Industrial](móveis-máquina de mistura industrial)\n* [Estação de Entrada](móveis-estação de entrada)\n* [Estação de Saída](móveis-estação de saída)\n\n**Receita**\n\n* <PERSON><PERSON>r [Gin](produtos-gin)\n* C<PERSON>r [<PERSON><PERSON>](produtos-martini misturado)\n\n* <PERSON><PERSON>r [<PERSON><PERSON>](produtos-martini) na [Máquina de Engarrafamento](móveis-máquina de engarrafamento)", "recipes_sodarecipe": "Receita de Refrigerante", "help_recipes_whiskyrecipe_content": "**Receita de Whisky** pode ser fabricada em uma [Fábrica](businesstypes-factory).\n\n**Ingredientes-primas necessários:**\n\n* [Cevada](products-barley)\n* [Água](products-water)\n* [Fermento](products-yeast)\n\n**Máquinas de Fábrica Necessárias:**\n\n* [Máquina de Engarrafamento](furniture-bottlingmachine)\n* [Máquina de Mistura Industrial](furniture-industrialblendingmachine)\n* [Estação de Entrada](furniture-inputstation)\n* [Estação de Saída](furniture-outputstation)\n\n**Receita**\n\n* Criar [Whisky Fermentado](products-fermentedwhisky)\n\n* Criar [Whisky](products-whisky) na [Máquina de Engarrafamento](furniture-bottlingmachine)", "recipes_winerecipe": "<PERSON><PERSON><PERSON>", "common_factorymachines": "Máquinas de Fábrica", "help_itemname_automatedfryingmachine_content": "**Automated Frying Machine** é uma Factory Machine.\n\nA máquina recebe itens de uma [Input Station](furniture-inputstation) ou outra Factory Machine para fabricar os seguintes itens:\n\n* [French Fries](products-frenchfries)\n* [Plain Donut](products-plaindonut)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Factory Supply Depot](address:57 5a)", "help_itemname_batchpasteurizer_content": "**Pasteurizador de lote** é uma Máquina de fábrica.\n\nA máquina recebe itens de uma [Estação de entrada](furniture-inputstation) ou outra Máquina de fábrica para fabricar os seguintes itens:\n\n* [Mistura de sorvete](products-icecreammixture)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Depósito de suprimentos de fábrica](endereço: 57 5a)", "help_itemname_hardeningtunnel_content": "**Hardening Tunnel** é uma Factory Machine.\n\nA máquina recebe itens de uma [Input Station](furniture-inputstation) ou outra Factory Machine para fabricar os seguintes itens:\n\n* [Ice Cream Recipe](recipes-icecreamrecipe)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Factory Supply Depot](address:57 5a)", "help_itemname_polishingmachine_content": "**Máquina de Polimento de Gemas** é uma Máquina de Fábrica.\n\nA máquina recebe itens de uma [Estação de Entrada](furniture-inputstation) ou outra Máquina de Fábrica para fabricar os seguintes itens:\n\n* [Gemas Polidas (Baratas)](products-polishedgemscheap)\n* [<PERSON><PERSON>s Polidas (Caras)](products-polishedgemsexpensive)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Depósito de Suprimentos de Fábrica](endereço:57 5a)", "common_factory_ingredients": "Ingredientes de fábrica", "itemname_rawlettuce": "Saco de Alface", "help_itemname_sugar_content": "**Açúcar** um ingrediente bruto usado por [Fábricas](businesstypes-factory).\n\nO produto pode ser usado pelas seguintes máquinas de fábrica:\n\n* [Pasteurizador de lote](furniture-batchpasteurizer)\n* [Máquina de engarrafamento](furniture-bottlingmachine)\n* [Máquina de mistura industrial](furniture-industrialblendingmachine)\n* [Estação de entrada](furniture-inputstation)\n\nO produto pode ser usado para fabricar os seguintes produtos:\n\n* [Refrigerante carbonatado](products-carbonatedsoda)\n* [Massa de cupcake](products-cupcakebatter)\n* [Vinho fermentado](products-fermentedwine)\n* [Mistura de sorvete](products-icecreammixture)\n* [Glacê](products-frosting)\n* [Triple Sec](products-triplesec)\n\nO produto pode ser importado dos seguintes locais:\n* [Envios da Lunar Tide](endereço: 6 pier)", "help_itemname_hotdogbun_content": "**Hotdog Bun** é um ingrediente fabricado usado por [Factories](businesstypes-factory).\n\nO produto pode ser usado pelas seguintes máquinas de fábrica:\n\n* [Food Assembly Machine](furniture-foodassemblymachine)\n* [Input Station](furniture-inputstation)\n\nO produto pode ser usado para fabricar os seguintes produtos:\n\n* [Hotdog](products-hotdog)\n\nO produto pode ser fabricado a partir dos seguintes ingredientes:\n* [Dough](products-dough)\n\nO produto pode ser fabricado usando as seguintes máquinas:\n* [Automated Baking Machine](furniture-automatedbakingmachine)", "help_itemname_rawpizza_content": "**Pizza Crua** é um ingrediente fabricado usado por [Fábricas](businesstypes-factory).\n\nO produto pode ser usado pelas seguintes Máquinas de Fábrica:\n\n* [Máquina de Panificação Automatizada](furniture-automatedbakingmachine)\n* [Estação de Entrada](furniture-inputstation)\n\nO produto pode ser usado para fabricar os seguintes produtos:\n\n* [Pizza](products-pizza)\n\nO produto pode ser fabricado a partir dos seguintes ingredientes:\n* [Massa](products-dough)\n* [Queijo Fatiado](products-slicedcheese)\n* [Pasta de Tomate](products-tomatopaste)\n\nO produto pode ser fabricado usando as seguintes máquinas:\n* [Máquina de Montagem de Alimentos](furniture-foodassemblymachine)", "help_itemname_polishedgemsexpensive_content": "**<PERSON><PERSON><PERSON>idas (Caras)** são um ingrediente manufaturado usado por [Fábricas](businesstypes-factory).\n\nO produto pode ser usado pelas seguintes Máquinas de Fábrica:\n\n* [Máquina de Montagem de Bens de Consumo](furniture-consumergoodsassemblymachine)\n* [Estação de Entrada](furniture-inputstation)\n\nO produto pode ser usado para fabricar os seguintes produtos:\n\n* [Joias (Caras)](products-expensivejewelry)\n\nO produto pode ser fabricado com os seguintes ingredientes:\n* [G<PERSON>s <PERSON> (Caras)](products-cutgemscarpensive)\n\nO produto pode ser fabricado usando as seguintes máquinas:\n* [Máquina de Polimento](furniture-polishingmachine)", "importertypename_aquaticbay": "Aquatic Bay Cargo", "help_importertypename_aquaticbay_content": "**Aquatic Bay Cargo** é um local de importação.\n\nEles importam os seguintes itens:\n* [Cevada](produtos-barley)\n* [Agave Azul](produtos-blueagave)\n* [Dióxido de Carbono](produtos-carbondioxide)\n* [Papel para Charuto](produtos-cigarpaper)\n* [Papel para Cigarro](produtos-cigarettepaper)\n* [Aromatizante de Cola](produtos-colaflavoring)\n* [Uvas](produtos-uvas)\n* [Grãos de Café Moídos](produtos-groundcoffeebeans)\n* [L<PERSON><PERSON><PERSON>](produtos-lúpulo)\n* [Bagas de Zimbro](produtos-juniperberries)\n* [Suco de Limão](produtos-limejuice)\n* [Cascas de Laranja](produtos-orangepeels)\n* [Tabaco](produtos-tobacco)\n* [Vermute](produtos-vermute)\n* [Água](produtos-água)\n* [Levedura](produtos-levedura)\n\nEles estão localizados aqui:\n* [Aquatic Bay Cargo](endereço: 8 pier)", "itemname_deliveryspot": "Local de Entregas", "item_location_cannot_be_changed_in_building_notification": "A localização do {itemname} não pode ser alterada para este edifício.", "itempanelui_hud_confirm_sell_vehicle_with_cargo": "Tem certeza de que deseja vender {type} e sua carga por {price}?", "input_key_openhandtool": "Ferramenta de Mão", "input_key_openeyedroppertool": "Conta-Gotas", "input_key_openpalettetool": "Ferramenta paleta", "input_key_openfloortool": "Ferramenta de chão", "input_key_openwalltool": "Ferramenta de Parede", "input_key_openqueuetool": "Ferramenta de fila", "input_key_opensecuritytool": "Ferramenta de segurança", "input_label_ui": "Interface de Usuário", "input_label_vehicle": "Veí<PERSON>lo", "input_key_undo": "<PERSON><PERSON><PERSON>", "input_key_redo": "<PERSON><PERSON><PERSON>", "input_key_openselltool": "Ferramenta de Venda", "input_key_specialbehavior": "Comportamento especial", "workpanel_business_opening_soon": "O negócio abrirá em breve", "new_contact_added_notification": "{name} foi adicionado ao seu aplicativo Contatos.", "transfer_bizman_settings": "Transferir configurações do BizMan", "workpanel_business_shift_starts_soon": "Seu turno começa em breve", "itemname_bedsidetable": "Mesa de Cabeceira", "itemname_industrialsewingmachine": "Máquina de Costura Industrial", "itemname_cigarette": "Maço de Cigarros", "itemname_ceilinglampwarehouse": "Luminária de teto industrial", "itemname_tobacco": "Tabaco", "itemname_cigarpaper": "Papel para Charuto", "itemname_cigarettepaper": "Papel para Cigarro", "itemname_uncutgemscheap": "Pedras Precio<PERSON>s Brutas (Baratas)", "itemname_uncutgemsexpensive": "Pedras Precio<PERSON> Bru<PERSON> (Caras)", "itemname_cutgemscheap": "Pedras Pre<PERSON>s (Baratas)", "itemname_cutgemsexpensive": "Pedras Pre<PERSON> (Caras)", "itemname_polishedgemscheap": "Pedras Preciosas Polidas (Baratas)", "itemname_polishedgemsexpensive": "Pedras Preciosas Polidas (Caras)", "itemname_lasercuttingmachine": "Máquina de Corte a Laser", "itemname_polishingmachine": "Polimento de Pedras Preciosas", "itemname_metalband": "Banda de Metal", "itemname_resistors": "Resistores", "itemname_capacitors": "Capacitores", "itemname_transistors": "Transistores", "itemname_integratedcircuits": "Circuitos Integrados", "itemname_coppercladlaminate": "Laminado Revestido de Cobre", "itemname_smallcircuitboard": "Placa de Circuito (Pequena)", "itemname_mediumcircuitboard": "Placa de Circuito (Média)", "itemname_glass": "<PERSON><PERSON><PERSON>", "itemname_smallscreen": "Tela (Pequena)", "itemname_mediumscreen": "Tela (Média)", "itemname_microphone": "Microfone", "itemname_speaker": "Alto falante", "itemname_smallaudiomodule": "<PERSON><PERSON><PERSON><PERSON> (Pequeno)", "itemname_mediumaudiomodule": "<PERSON><PERSON><PERSON><PERSON> (Médio)", "itemname_battery": "Bateria", "itemname_plastic": "Plástico", "itemname_moldedsmartphone": "Smartwatch Moldado", "itemname_moldedsmartwatch": "Smartwatch Moldado", "itemname_moldedheadphones": "Fones de Ouvido <PERSON>", "itemname_moldedearbuds": "Fones de Ouvido <PERSON>", "itemname_bottlingmachine": "Máquina de Engarrafamento", "itemname_groundcoffeebeans": "Grãos de Café Moídos", "itemname_brewedcoffee": "Café Coado", "itemname_water": "Água", "itemname_colaflavoring": "Aromatizante de Cola", "itemname_carbondioxide": "Dióxido de <PERSON>o", "itemname_carbonatedsoda": "Refrigerante Gaseificado", "itemname_barley": "Cevada", "itemname_yeast": "Levedura", "itemname_fermentedwhisky": "Whisky Fermentado", "itemname_grapes": "<PERSON><PERSON>", "itemname_fermentedwine": "<PERSON><PERSON>", "itemname_hops": "<PERSON><PERSON><PERSON><PERSON>", "itemname_fermentedbeer": "Cerveja Fermentada", "itemname_carbonatedbeer": "Cerveja Gaseificada", "itemname_juniperberries": "Bagas de Zimbro", "itemname_gin": "Gin", "itemname_vermouth": "Vermute", "itemname_blendedmartini": "<PERSON><PERSON>", "itemname_orangepeels": "Cascas de Laranja", "itemname_triplesec": "Triple Sec", "itemname_blueagave": "Agave Azul", "itemname_tequila": "Tequila", "itemname_limejuice": "Suco de Lima", "itemname_blendedmargarita": "<PERSON><PERSON><PERSON>", "click_to_add_inventory": "Clique para adicionar ao inventário", "click_to_manage": "Clique para gerenciar", "click_to_grab": "Clique para pegar", "enter_vehicle": "Entrar no veículo", "click_to_add_to_storage": "Clique para adicionar ao armazenamento", "add_handtruck_to_storage": "Adicionar <PERSON><PERSON>ho ao armazenamento", "click_to_exercise": "Clique para se exercitar", "click_to_view_job_offer": "Clique para ver a oferta de emprego", "click_to_use": "Clique para usar", "click_to_talk": "Clique para conversar", "use_cart": "<PERSON><PERSON>", "click_to_drive": "Clique para dirigir", "click_to_purchase": "Clique para comprar", "itemname_clay": "<PERSON><PERSON><PERSON>", "itemname_ceramicmug": "Caneca de cerâmica", "itemname_cutfabricclassiccheap": "<PERSON><PERSON><PERSON> (clássico barato)", "itemname_cutfabricclassicexpensive": "<PERSON><PERSON><PERSON> (Clássico Caro)", "itemname_kilnmachine": "Máquina de forno", "help_itemname_consumergoodsassemblymachine_content": "**Máquina de montagem de bens de consumo** é uma máquina de fábrica.\n\nA máquina recebe itens de uma [Estação de entrada](furniture-inputstation) ou de outra Máquina de fábrica para fabricar os seguintes itens:\n\n* [Arty Fish Phone](products-smartphone1)\n* [Arty Fish Smartwatch](products-smartwatch2)\n* [<PERSON><PERSON><PERSON><PERSON> (médio)](products-mediumaudiomodule)\n* [<PERSON><PERSON><PERSON><PERSON> (pequeno)](products-smallaudiomodule)\n* [Placa de circuito (média)](products-mediumcircuitboard)\n* [Placa de circuito (pequena)](products-smallcircuitboard)\n* [Fones de ouvido Noize Boss](products-earbuds01)\n* [Presente (caro)](products-expensivegift)\n* [Joias (baratas)](products-cheapjewelry)\n* [<PERSON><PERSON> (Caro)](products-expensivejewelry)\n* [Fones de ouvido Rhythm By Tre](products-headphones01)\n* [Telefone ZanaMan](products-smartphone2)\n* [Smartwatch ZanaMan](products-smartwatch1)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Factory Supply Depot](endereço: 57 5a)", "help_itemname_kilnmachine_content": "**Kiln Machine** é uma Máquina de Fábrica.\n\nA máquina recebe itens de uma [Estação de Entrada](furniture-inputstation) ou de outra Máquina de Fábrica para fabricar os seguintes itens:\n\n* [Caneca de Cerâmica](products-ceramicmug)\n* [Fones de Ouvido Moldados](products-moldedearbuds)\n* [Fones de Ouvido Moldados](products-moldedheadphones)\n* [Smartphone Moldado](products-moldedsmartphone)\n* [Smartwatch Moldado](products-moldedsmartwatch)\n* [Base de Globo de Neve Moldada](products-moldedsnowglobebase)\n* [Figura de Globo de Neve Moldada](products-moldedsnowglobefigure)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Depósito de Suprimentos de Fábrica](endereço:57 5a)", "help_itemname_industrialblendingmachine_content": "**Máquina de Mistura Industrial** é uma Máquina de Fábrica.\n\nA máquina recebe itens de uma [Estação de Entrada](furniture-inputstation) ou outra Máquina de Fábrica para fabricar os seguintes itens:\n\n* [Margarita Misturada](products-blendedmargarita)\n* [Martini <PERSON>](products-blendedmartini)\n* [Café Coado](products-brewedcoffee)\n* [Manteiga](products-butter)\n* [Cerveja Carbonatada](products-carbonatedbeer)\n* [Refrigerante Carbonatado](products-carbonatedsoda)\n* [Massa para Cupcake](products-cupcakebatter)\n* [Molho](products-dressing)\n* [Cerveja Fermentada](products-fermentedbeer)\n* [Uísque Fermentado](products-fermentedwhisky)\n* [Vinho Fermentado](products-fermentedwine)\n* [Cobertura](products-frosting)\n* [Gin](products-gin)\n* [Ketchup](products-ketchup)\n* [Massa Laminada](products-laminateddough)\n* [Marinada](products-marinada)\n* [Tequila](products-tequila)\n* [Pasta de Tomate](products-tomatopaste)\n* [Triple Sec](products-triplesec)\n\nA máquina pode ser comprada nos seguintes locais:\n* [Factory Supply Depot](endereço:57 5a)", "click_to_learn": "Clique para aprender", "itemoverlay_fridge_is_empty": "A geladeira está vazia", "click_to_preview_designs": "Clique para visualizar os designs", "tickethouse_open_info": "O próximo barco cassino chega em {dayOfWeek} entre {fromTime} e {toTime}", "tickethouse_not_open": "Não há barco cassino disponível no momento. O próximo barco cassino chega {dayOfWeek} entre {fromTime} e {toTime}", "notification_storage_is_full": "O armazenamento está cheio", "irs_no_taxes_due": "Nenhum imposto devido", "click_to_return": "Clique para retornar", "common_ingredients": "Ingredientes", "common_outputs": "<PERSON><PERSON><PERSON>", "itemoverlay_no_recipe_selected": "Nenhuma receita selecionada", "itemoverlay_no_product_on_belt": "Nenhum produto na correia transportadora", "itemoverlay_machine_speed": "{amount} minutos para produzir", "itemname_fabriccheap": "<PERSON><PERSON><PERSON> (barato)", "common_travel": "<PERSON><PERSON>", "subwaystations": "Estações de Metrô", "click_to_get_haircut": "Clique para cortar o cabelo", "click_to_order": "Clique para encomendar", "itemname_decorativebarrel": "Barril Decorativo", "help_itemname_fabricclassiccheap_content": "**<PERSON><PERSON><PERSON> (Classic Cheap)** é um ingrediente bruto usado por [Factories](businesstypes-factory).\n\nO produto pode ser usado pelas seguintes máquinas de fábrica:\n\n* [Laser Cutting Machine](furniture-lasercuttingmachine)\n* [Input Station](furniture-inputstation)\n\nO produto pode ser usado para fabricar os seguintes produtos:\n\n* [Cut Fabric (Classic Cheap)](products-cutfabricclassiccheap)\n\nO produto pode ser importado dos seguintes locais:\n* [Maritime Freight Line](address: 7 pier)", "employeehelper_notification_employee_amount_finished_training": "{amount} funcionários concluíram o treinamento", "interiordesigner_are_you_sure_apply": "Tem certeza? Depois que as alterações forem aplicadas, a compra não poderá ser revertida.", "interiordesigner_are_you_sure_discard": "Tem certeza? Descartar suas alterações desfará suas alterações no Interior Designer.", "common_apply_changes": "<PERSON>p<PERSON><PERSON>", "common_discard_changes": "Descar<PERSON>", "click_to_pay_taxes": "Clique para pagar impostos", "businessschool_holding_item": "Você não pode entrar na sala de aula segurando este item", "itemname_smallsignwithtext": "Pequeno sinal com texto", "itemname_cardreader": "<PERSON><PERSON>", "itemname_schooldoor": "Porta da escola", "itemname_productdisplaystandtiered": "Suporte de exposição (produto em camadas)", "itemname_specialemployeedesksmall": "Escrivaninh<PERSON>", "itemname_gymlockers": "Armários de Ginásio", "itemname_turnstilearm": "Braço de catraca", "itemname_turnstilepillar": "<PERSON><PERSON> da catraca", "itemname_publicshower": "Chuveiro <PERSON>", "workouttype_kettlebellpull": "Puxada com Kettlebell", "itemname_gymcovercharge": "Taxa de entrada no ginásio", "itemname_nightclubweekendcovercharge": "Taxa de entrada para o fim de semana em boate", "skillname_gymtrainer": "Treinador de Ginástica", "gym_confirm_personalized_workout": "Você quer começar um plano de treino personalizado para hoje?", "gym_confirm_change_personalized_workout": "Você quer um novo plano de treino personalizado?", "click_to_get_a_personalized_workout_plan": "Clique para obter um plano de treino personalizado", "workout_plan": "Plano de treino", "itemname_fitnessplanningboard": "Quadro de Planejamento de Treinos", "itemname_floorarrow": "Flecha de chão", "notification_clickpark_cannotpark": "Não é possível estacionar o veículo nesta posição", "help_itemname_itemgroupworkoutmachine_content": "Diferentes exercícios podem ser feitos com cada tipo de máquina de exercícios\n* Boxe usando [Saco de Boxe](furniture-boxingbag)\n* Abdominais usando [Fitball](furniture-fitball)\n* Salto usando [Trampolim Fitness](furniture-fitnesstrampoline)\n* Abdominais usando [Tapete de Ginástica](furniture-gymmat)\n* Puxada com Kettlebell usando [Kettlebells](furniture-kettlebells)\n* Levantamento terra usando [Barra Carregada](furniture-loadedbarbell)\n* Puxadas usando [Barra de Pull-Up](furniture-pullupbar)\n* Agachamento usando [Estação de Agachamento](furniture-squatsstation)\n* Corrida usando [Esteira](furniture-treadmill)\n* <PERSON><PERSON><PERSON> usando [Banco de Treino](furniture-workoutbench)", "itemname_kettlebells": "<PERSON><PERSON><PERSON>", "common_character": "Personagem", "common_economy": "Economia", "custom_game_presets_cannot_delete_default": "Não é possível excluir a predefinição padrão {nome}", "custom_game_presets_deleted_preset": "Predefinição {nome} excluída com sucesso", "custom_game_presets_copied_to_clipboard": "Configurações do jogo copiadas para a área de transferência", "custom_game_presets_paste_error": "Não foi possível colar as configurações do jogo da área de transferência. Certifique-se de que os dados copiados são válidos.", "custom_game_presets_pasted_preset": "Predefinição {nome} salva com sucesso da área de transferência", "custom_game_presets_saved_preset": "Predefinição {nome} salva com sucesso", "custom_game_presets_save_preset": "<PERSON><PERSON> prede<PERSON>", "custom_game_presets_are_you_sure_delete": "Tem certeza de que deseja excluir a predefinição {nome}?", "custom_game_presets_preset_already_exists": "Predefinição com nome '{name}' já existe. Você quer substituir?", "custom_game_presets_tooltip_delete": "Excluir predefinição", "custom_game_presets_tooltip_copy": "Copiar para área de transferência", "custom_game_presets_tooltip_paste": "Colar da área de transferência", "custom_game_presets_tooltip_save": "<PERSON><PERSON> prede<PERSON>", "main_menu_new_game_story_recommendation": "Melhor para novos jogadores", "itemname_flatbedspawnerstacked": "<PERSON><PERSON><PERSON>", "main_menu_custom_game_disable_vehicle_damage": "Desativar danos em veículos", "main_menu_custom_game_disable_vehicle_fuel": "Desativar consumo de combustível de veículos", "main_menu_custom_game_save_preset_as": "<PERSON>var predefinição como...", "common_enter_display_text": "Insira o texto de exibição", "itemname_specialgift2024": "Presente Especial 2024", "notification_missing_diploma_poach_employee": "<PERSON>r lei, você precisa de um diploma em {name} para recrutar funcionários", "notification_missing_diploma_overtake_business": "<PERSON>r lei, você precisa de um diploma em {name} para comprar uma empresa", "npc_expression_no_gym_trainers": "Não há instrutores de academia para me motivar!", "click_to_toggle": "Clique para alternar", "itemname_flatbed": "Flatbed", "itemname_halloweenneon": "Neon de Halloween", "itemname_christmasneon": "Neon de Natal", "common_compiling_shaders": "Compilando shaders...", "compiling_shaders_description": "Este processo pode levar até 3 minutos", "notification_deleted_all_contacts": "<PERSON><PERSON> as mensagens apagadas", "notification_deleted_all_contacts_from_category": "<PERSON><PERSON> as mensagens de {name} exc<PERSON><PERSON><PERSON>", "main_menu_custom_game_unlock_all_contacts": "Desbloquear todos os contatos", "phone_logistics_manager_delivery_no_stock": "<PERSON>do à falta de estoque em <noparse>{businessName}</noparse>, os seguintes itens não puderam ser entregues:<br>{list}", "phone_logistics_manager_delivery_no_available_space": "<PERSON>do à falta de espaço disponível no destino, os seguintes itens não puderam ser entregues:<br>{list}", "gasstationoverlay_wash": "Lavar ({price})", "input_key_vehicleleftblinker": "Pisca-pisca esquerdo", "input_key_vehiclerightblinker": "Piscando à direita", "menu_options_graphics_showfps": "Mostrar FPS", "menu_options_controls_steering_assist": "Ativar assistência de direção", "menu_options_others_seasonal_decorations": "Decorações sazonais", "citymap_filter_hide_closed": "Ocultar empresas fechadas", "menu_options_audio_sfx_volume": "VOLUME SFX", "character_creation_blendshape_mouth_corners": "cantos", "bizman_schedule_auto_fill_day": "Preencher dia automaticamente", "hud_confirm_autofill_schedule_all": "Tem certeza de que deseja preencher automaticamente toda a programação?", "hud_confirm_autofill_schedule_day": "Tem certeza de que deseja preencher automaticamente a agenda deste dia?", "bizman_schedule_auto_fill_loading": "Preenchendo automaticamente a agenda...", "bizman_schedule_auto_fill_unassigned": "Alguns funcionários não puderam ser alocados a uma estação que correspondesse às suas habilidades, ou não havia horas de trabalho suficientes para todos os funcionários. Continuar?", "bizman_schedule_auto_fill_partial_title": "O cronograma não é o ideal", "bizman_schedule_auto_fill_partial": "Não foi possível otimizar totalmente a agenda. Revise-a e faça os ajustes necessários.", "bizman_schedule_auto_fill_day_partial": "Não foi possível otimizar a completamente a agenda. <b>Dica:</b> Para melhores resultados, tente \"Preencher tudo automaticamente\".", "bizman_schedule_auto_fill_info": "Você pode continuar jogando enquanto a agenda é preenchida. Você receberá uma notificação quando o processo for concluído.", "bizman_schedule_auto_fill_notify": "A agenda de {businessName} foi preenchida.", "bizman_schedule_auto_fill_notify_partial": "A agenda de {businessName} foi preenchida, mas não pôde ser totalmente otimizada.", "bizman_schedule_auto_fill_notify_cancel": "O preenchimento automático da agenda de {businessName} foi cancelado.", "itemname_jewelrybox": "Caixa de joias", "action_take": "Leva", "itemname_umbrella": "Guarda-chuva", "itemname_umbrellastand": "Suporte de Guarda-chuva", "itemname_wardrobewall": "Cabide<PERSON>", "action_equip": "Equipamento", "happinessmodifiertype_wet": "<PERSON><PERSON><PERSON><PERSON>", "bizman_set_up_uniforms": "Configurar uniformes", "bizman_schedule_search_workstation": "Pesquisar estação de trabalho", "bizman_schedule_search_placeholder": "por exemplo \"Computador\"", "bizman_schedule_customers_per_hour": "{amount} clientes por hora", "bizman_schedule_one_customer_per_hour": "1 cliente por hora", "bizman_schedule_employee_demands": "<PERSON><PERSON><PERSON>", "bizman_schedule_employee_skills": "Habilidades:", "bizman_schedule_employee_schedule": "Agendar:", "bizman_schedule_hourly_wage": "{wage}/h", "bizman_schedule_employee_schedule_preferences": "Preferências de agendamento", "bizman_schedule_employee_type": "Tipo", "bizman_schedule_employee_sort_by": "Ordenar por", "bizman_schedule_employee_scheduled_hours": "Horários programados", "bizman_schedule_employee_equipment": "EQUIPAMENTO", "bizman_schedule_employee_doesnt_have_required_skill": "Este funcionário não possui a habilidade necessária para esta estação de trabalho", "bizman_schedule_rename_workstation": "Renomear estação de trabalho", "bizman_schedule_clear_schedule_day": "Tem certeza de que deseja limpar a agenda deste dia?", "bizman_schedule_clear_schedule_all": "Tem certeza de que deseja limpar toda a agenda deste negócio?", "bizman_schedule_employee_search_placeholder": "por exemplo \"Tio Fred\"", "bizman_schedule_employee_header": "Clique com o botão esquerdo para atribuir um funcionário a {workstationName}", "bizman_schedule_cannot_toggle_open": "Para garantir o bom funcionamento do negócio, o horário de funcionamento não pode ser alterado", "bizman_schedule_cannot_change_working_hours": "Para garantir o bom funcionamento do negócio, o horário de trabalho deste funcionário não pode ser alterado.", "bizman_schedule_auto_scheduler_not_available_tooltip": "Designe um gerente de RH para preencher a agenda para você", "bizman_schedule_auto_scheduler_all_tooltip": "Preenchimento automático de toda a programação", "bizman_schedule_clear_schedule_all_tooltip": "Limpar agenda inteira", "bizman_schedule_auto_scheduler_day_tooltip": "Preenchimento automático", "bizman_schedule_clear_schedule_day_tooltip": "Agenda limpa", "bizman_schedule_copy_schedule_day_tooltip": "Copiar programação", "bizman_schedule_paste_schedule_day_tooltip": "Colar programação", "bizman_schedule_manage_employee_tooltip": "Gerenciar funcionário", "bizman_schedule_employee_not_available": "Não é possível atribuir um funcionário a este intervalo de tempo. Ele não possui as habilidades necessárias ou já está atribuído a outra estação de trabalho.", "bizman_schedule_factory_assigned_ingredient_tooltip": "Ingrediente atribuído", "current_building_ui_closing_in_hours": "<PERSON><PERSON><PERSON> em {hours} horas ({time})", "help_itemname_umbrella_content": "**Guarda-chuva** é um tipo de produto vendido principalmente em [Lojas de Presentes](businesstypes-giftshop).\n\nAlém disso, pode ser vendido em:\n\n* [Floristas](businesstypes-florist)\n* [Livraria](businesstypes-bookstore)\n\nO produto pode ser colocado nos seguintes móveis:\n* [Painel de Produtos](furniture-productpanel)\n\nO produto pode ser usado como um [Acessório de Personagem](common_accessories) para bloquear os efeitos negativos da chuva.\nO acessório pode ser armazenado em um [Suporte para Guarda-chuva](furniture-umbrellastand)\n\nO produto pode ser adquirido nos seguintes locais:\n* [NY Distro Inc](endereço: 37 1s)\n\nO produto pode ser importado dos seguintes locais:\n* [JetCargo Imports](endereço: 1 pier)", "help_itemname_umbrellastand_content": "**Suporte para Guarda-chuva** pode armazenar [Guarda-chuva](products-umbrella) para uso pessoal.\n\nEste móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>](endereço: 50 4s)", "help_itemname_wardrobewall_content": "O **Cabi<PERSON><PERSON> de parede** pode guardar roupas e ser usado para trocar de roupa.\n\n*Observação:* É necessário vestir as roupas de exercícios antes de [exercitar-se](common_exercise).\n\nO móvel pode ser adquirido no seguinte local:\n* [Ika Bo<PERSON>g](adress: 50 4s)", "help_itemname_headphonesstand_content": "**Suporte para Headphones** pode armazenar [Rhythm By Tre](products-headphones01) para uso pessoal.\n\nEste móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](endereço: 50 4s)", "help_itemname_earbudsstand_content": "**Suporte para Fones de Ouvido** pode armazenar [Fones de Ouvido Noize Boss](products-earbuds01) para uso pessoal.\n\nEste móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>g](endereço: 50 4s)", "help_itemname_jewelrybox_content": "**Porta jóias** pode guardar joias.\n\nEste móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>] (endereço: 50 4s)", "help_itemname_fruitbowl_content": "**Fruteira** pode armazenar frutas para uso pessoal.\n\nEste móvel pode ser adquirido nos seguintes locais:\n* [<PERSON><PERSON>] (endereço: 50 4s)", "bizman_schedule_employee_worked_week": "Horas programadas e dias trabalhados\nHoras trabalhadas e dias trabalhados", "bizman_schedule_employee_assigned": "{hours} horas, {days} dias", "bizman_schedule_employee_hours": "O funcionário foi designado para trabalhar {hoursAssigned} horas e trabalhou {hoursWorked} horas esta semana", "bizman_schedule_employee_days": "O funcionário foi designado para trabalhar {daysAssigned} dias e trabalhou {daysWorked} dias esta semana", "start_editing_seasons_warning": "Você está tentando editar uma empresa sem mudar as decorações sazonais. Tem certeza?", "common_upgrade": "<PERSON><PERSON><PERSON>", "bizman_schedule_employee_no_employees_found_filter": "Nenhum funcionário encontrado. Tente ajustar suas configurações de filtro.", "bizman_schedule_employee_no_employees": "Nenhum funcionário encontrado. Entre em contato com um recrutador para contratar funcionários para esta empresa.", "bizman_schedule_employee_header_no_workstation": "Lista de funcionários", "bizman_schedule_employee_list_tooltip": "Lista de funcionários", "bizman_schedule_paste_all_schedule_day_tooltip": "Colar programação em todos os dias", "purchaseui_pay_taxes": "<PERSON><PERSON> impostos", "bizman_schedule_paste_to_all_days_confirm": "Tem certeza de que deseja colar esta programação em todos os dias? Is<PERSON> substituirá as programações de todos os outros dias desta empresa.", "common_hours_abbr": "{hours} h", "itemname_itemgroupcoatcheck": "Guarda volumes", "bizman_schedule_clear_filter": "Limpar filtro", "help_itemname_uniformlocker_content": "O **Armário de Uniformes** é usado para configurar os uniformes dos funcionários da sua empresa.\n\nCom um Armário de Uniformes na sua empresa, você pode configurar os uniformes para os funcionários de cada empresa na página Configurações do aplicativo BizMan no seu [BizPhone](general-bizphone).\n\nOs móveis podem ser adquiridos nos seguintes locais:\n* [AJ Pederson & Son](endereço: 13 5a)", "marketing_notification_no_money_campaigns_disabled_multiple": "{amount} campanhas de marketing foram desativadas devido à falta de dinheiro", "employee_contact_message_low_satisfaction_context": "<PERSON><PERSON><PERSON>, chefe!\n\nPreciso informar que recentemente não tenho me sentido satisfeito com meu emprego, pois minha solicitação para {jobDemandName} não foi atendida.\n\nPara ser sincero, se a situação não mudar em breve, serei forçado a procurar outro emprego em outra empresa.\n\nEspero que você entenda e aplique as mudanças necessárias.\n\nAtenciosamente,\n{employeeName} ({businessName})", "dialog_installation_firm_manage_contracts": "Gerenciar contratos de instalação", "dialog_installation_firm_on_cancel_contract_firm": "<PERSON><PERSON><PERSON>, iremos cancelar esse contrato imediatamente.", "dialog_installation_firm_contracts_list_info": "{businessName}, {installationTime}<br> Design: {designName}", "dialog_installation_firm_contract_time_slot": "{day} (Dia {number})", "intro_tooltip_toggle_animations": "Ativar animações", "intro_tooltip_toggle_auto_zoom": "Ativar zoom automático", "intro_tooltip_zoom_in": "Aumentar o Zoom", "intro_tooltip_zoom_out": "Reduzir o zoom", "intro_tooltip_randomize_appearance": "Aparência aleatória", "intro_tooltip_randomize_name": "Nome aleatório", "help_itemname_jobboard_content": "O **Quadro de Empregos** pode ser usado para contratar funcionários, colocando-o na parede da loja onde você deseja contratá-los.\n\nVocê pode gerenciar seus candidatos no [Aplicativo MyEmployees](skill-myemployees) no seu [BizPhone](general-bizphone).\n\nO recrutamento pelo Quadro de Empregos é mais lento do que usar uma Agência de Empregos ou [Caçador de Talentos](skill-headhunter), mas, após a compra do quadro, o uso é gratuito!\n\nO móvel pode ser adquirido nos seguintes locais:\n* [AJ Pederson & Son](address:13 5a)\n* [Mr. <PERSON>'s Office Supplies](address:39 4a)", "view_candidates": "<PERSON><PERSON> candida<PERSON>", "change_text": "Alterar texto", "click_to_change_text": "Clique para alterar o texto", "notification_cannot_use_outside_of_home": "Você só pode realizar esta atividade em casa.", "interior_designer_cannot_be_sold": "{itemname} não pode ser vendido.", "econoview_row_fees": "Impostos", "transactiontype_headhunterreplacement_label": "Substituições do Caçador de Talentos", "bizman_schedule_fast_autofill": "Preenchimento automático rápido (pode produzir uma agenda fragmentada)"}