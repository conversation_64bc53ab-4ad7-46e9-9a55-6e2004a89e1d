{"root": [{"assemblyName": "OdinSerializer", "nameSpace": "OdinSerializer", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "OdinSerializer", "nameSpace": "OdinSerializer.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "BehaviorDesigner.Runtime", "nameSpace": "BehaviorDesigner.Runtime", "className": "Behavior", "methodName": "DomainReset", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "BehaviorDesigner.Runtime", "nameSpace": "BehaviorDesigner.Runtime", "className": "BehaviorManager", "methodName": "DomainReset", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "BehaviorDesigner.Runtime", "nameSpace": "BehaviorDesigner.Runtime", "className": "GlobalVariables", "methodName": "DomainReset", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "BigAmbitions", "nameSpace": "", "className": "DebugLogCollector", "methodName": "Init", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine.PostFX", "className": "CinemachineVolumeSettings", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.MemoryProfiler", "nameSpace": "Unity.MemoryProfiler", "className": "MetadataInjector", "methodName": "PlayerInitMetadata", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "HDRuntimeReflectionSystem", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.ResourceManager", "nameSpace": "UnityEngine.ResourceManagement.ResourceProviders", "className": "AssetBundleProvider", "methodName": "Init", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Services.Analytics", "nameSpace": "", "className": "Ua2CoreInitializeCallback", "methodName": "Register", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core", "nameSpace": "Unity.Services.Core", "className": "UnityThreadUtils", "methodName": "CaptureUnityThreadInfo", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "TaskAsyncOperation", "methodName": "SetScheduler", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "CreateStaticInstance", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "EnableServicesInitializationAsync", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Registration", "nameSpace": "Unity.Services.Core.Registration", "className": "CorePackageInitializer", "methodName": "InitializeOnLoad", "loadTypes": 1, "isUnityClass": true}]}