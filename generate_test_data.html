<!DOCTYPE html>
<html>
<head>
    <title>生成测试数据</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        button {
            background: #ff9500;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #e6850e;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 成长计划测试数据生成器</h1>
        <p>点击下面的按钮为您的成长计划应用生成30天的测试数据</p>
        
        <button onclick="generateTestData()">🚀 生成30天测试数据</button>
        <button onclick="clearAllData()">🗑️ 清空所有数据</button>
        <button onclick="openMainApp()">📊 打开主应用</button>
        
        <div id="status" class="status"></div>
        
        <div style="margin-top: 30px; text-align: left; font-size: 14px; color: #666;">
            <h3>生成的数据包括：</h3>
            <ul>
                <li>✅ 30天完整的生活数据（睡眠、心情、体重等）</li>
                <li>✅ P90X训练记录（与睡眠质量相关）</li>
                <li>✅ 英语学习数据（时长和单词数）</li>
                <li>✅ 阅读记录（时长和页数）</li>
                <li>✅ 冥想练习数据</li>
                <li>✅ 不良习惯记录（刷抖音、熬夜、吃零食）</li>
                <li>✅ 卡路里摄入和热量差数据</li>
                <li>✅ 具有真实相关性的智能数据模式</li>
            </ul>
        </div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${isError ? 'error' : 'success'}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function generateTestData() {
            try {
                console.log('开始生成增强测试数据...');

                // Set challenge start date to 30 days ago
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - 29); // 30 days of data
                const startDateStr = startDate.toISOString().split('T')[0];
                localStorage.setItem('challenge-start-date', startDateStr);

                // Also set it in sessionStorage for the main app to pick up
                sessionStorage.setItem('test-start-date', startDateStr);

                // Use the same planId format as the app
                const date = new Date(startDateStr);
                const planId = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

                // First, add some test habits to make insights more interesting
                const testHabits = [
                    { id: 1, name: '刷抖音', frequency: 'daily' },
                    { id: 2, name: '熬夜', frequency: 'daily' },
                    { id: 3, name: '吃零食', frequency: 'daily' }
                ];
                
                // Clear existing habits and add test habits
                localStorage.setItem('habitList', JSON.stringify(testHabits));

                // Generate data for each day with realistic correlations
                for (let day = 1; day <= 30; day++) {
                    const currentDate = new Date(startDate);
                    currentDate.setDate(startDate.getDate() + day - 1);
                    const dateStr = currentDate.toISOString().split('T')[0];

                    // Base sleep quality (affects many other metrics)
                    const sleepQuality = Math.random(); // 0-1, higher = better sleep
                    const sleepHours = 6 + sleepQuality * 3; // 6-9 hours based on quality
                    
                    // Sleep data with realistic times
                    const sleepStart = 22 + Math.random() * 2; // 22:00-24:00
                    const sleepEnd = sleepStart + sleepHours;
                    const actualSleepEnd = sleepEnd > 24 ? sleepEnd - 24 + 6 : sleepEnd; // Handle overnight
                    
                    localStorage.setItem(`${planId}-life-sleep-start-day${day}`, 
                        `${String(Math.floor(sleepStart)).padStart(2, '0')}:${String(Math.floor((sleepStart % 1) * 60)).padStart(2, '0')}`);
                    localStorage.setItem(`${planId}-life-sleep-end-day${day}`, 
                        `${String(Math.floor(actualSleepEnd)).padStart(2, '0')}:${String(Math.floor((actualSleepEnd % 1) * 60)).padStart(2, '0')}`);

                    // Mood rating (strongly correlated with sleep quality)
                    const baseMood = 3 + sleepQuality * 5; // 3-8 base mood from sleep
                    const moodVariation = (Math.random() - 0.5) * 2; // ±1 random variation
                    const finalMood = Math.max(1, Math.min(10, Math.round(baseMood + moodVariation)));
                    localStorage.setItem(`${planId}-mood-rating-day${day}`, finalMood);

                    // P90X data (affects mood positively, reduces bad habits)
                    const p90xMotivation = 0.7 + sleepQuality * 0.3; // Better sleep = more motivation
                    const didP90X = Math.random() < p90xMotivation;
                    if (didP90X) {
                        const p90xTime = 45 + Math.random() * 30; // 45-75 minutes
                        localStorage.setItem(`${planId}-p90x-time-day${day}`, Math.round(p90xTime));
                        localStorage.setItem(`${planId}-p90x-donetime-day${day}`, `${dateStr}T${String(Math.floor(Math.random() * 3) + 6).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`);
                        localStorage.setItem(`${planId}-p90x-kcal-day${day}`, Math.floor(p90xTime * 6) + 200); // ~6 kcal/min
                        localStorage.setItem(`${planId}-p90x-check-day${day}`, 'true');
                    }

                    // English learning (correlated with overall discipline)
                    const studyMotivation = sleepQuality * 0.6 + (didP90X ? 0.3 : 0) + Math.random() * 0.4;
                    if (studyMotivation > 0.4) {
                        const engTime = 20 + studyMotivation * 50; // 20-70 minutes
                        const engWords = Math.round(engTime * 0.8); // ~0.8 words per minute
                        localStorage.setItem(`${planId}-eng-time-day${day}`, Math.round(engTime));
                        localStorage.setItem(`${planId}-eng-words-day${day}`, engWords);
                        localStorage.setItem(`${planId}-eng-donetime-day${day}`, `${dateStr}T${String(Math.floor(Math.random() * 4) + 19).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`);
                        localStorage.setItem(`${planId}-eng-content-day${day}`, `Unit ${Math.floor(day/3) + 1} - Lesson ${(day % 3) + 1}`);
                        localStorage.setItem(`${planId}-eng-notes-day${day}`, `复习了${engWords}个单词`);
                    }

                    // Reading data (correlated with mood and discipline)
                    const readingMotivation = (finalMood / 10) * 0.6 + studyMotivation * 0.4;
                    if (readingMotivation > 0.3) {
                        const readTime = 20 + readingMotivation * 60; // 20-80 minutes
                        const readPages = Math.round(readTime / 2.5); // ~2.5 min per page
                        localStorage.setItem(`${planId}-read-time-day${day}`, Math.round(readTime));
                        localStorage.setItem(`${planId}-read-pages-day${day}`, readPages);
                        localStorage.setItem(`${planId}-read-notes-day${day}`, `阅读心得第${day}天`);
                    }

                    // Meditation (helps with mood, inversely related to stress)
                    const meditationChance = sleepQuality * 0.5 + (finalMood / 10) * 0.3 + Math.random() * 0.3;
                    if (meditationChance > 0.4) {
                        const medTime = 10 + Math.random() * 20; // 10-30 minutes
                        localStorage.setItem(`${planId}-med-check-day${day}`, 'true');
                        localStorage.setItem(`${planId}-med-time-day${day}`, Math.round(medTime));
                        localStorage.setItem(`${planId}-med-donetime-day${day}`, `${dateStr}T${String(Math.floor(Math.random() * 2) + 6).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`);
                    }

                    // Bad habits (inversely correlated with good habits and mood)
                    const stressLevel = 1 - sleepQuality; // Poor sleep = more stress
                    const disciplineLevel = (didP90X ? 0.3 : 0) + (studyMotivation > 0.4 ? 0.2 : 0) + (meditationChance > 0.4 ? 0.2 : 0);
                    
                    // 刷抖音 (inversely related to discipline, positively to stress)
                    const douyinProbability = stressLevel * 0.7 + (1 - disciplineLevel) * 0.5;
                    if (Math.random() < douyinProbability) {
                        const douyinTime = 30 + stressLevel * 90; // 30-120 minutes when stressed
                        const douyinCount = Math.ceil(douyinTime / 15); // ~15 min per session
                        localStorage.setItem(`${planId}-habit-h1-d${day}-time`, Math.round(douyinTime));
                        localStorage.setItem(`${planId}-habit-h1-d${day}-count`, douyinCount);
                    }

                    // 熬夜 (related to poor sleep quality)
                    if (sleepQuality < 0.6) { // Poor sleep often means staying up late
                        const lateNightTime = (1 - sleepQuality) * 120; // 0-120 minutes past bedtime
                        localStorage.setItem(`${planId}-habit-h2-d${day}-time`, Math.round(lateNightTime));
                        localStorage.setItem(`${planId}-habit-h2-d${day}-count`, 1);
                    }

                    // 吃零食 (stress eating, inversely related to discipline)
                    const snackingProbability = stressLevel * 0.6 + (1 - disciplineLevel) * 0.4;
                    if (Math.random() < snackingProbability) {
                        const snackTime = 20 + stressLevel * 40; // 20-60 minutes
                        const snackCount = Math.ceil(snackTime / 10); // ~10 min per snacking session
                        localStorage.setItem(`${planId}-habit-h3-d${day}-time`, Math.round(snackTime));
                        localStorage.setItem(`${planId}-habit-h3-d${day}-count`, snackCount);
                    }

                    // Other lifestyle data
                    localStorage.setItem(`${planId}-life-reflection-day${day}`, `今天完成了计划的${Math.floor(disciplineLevel * 100)}%，感觉${finalMood > 6 ? '不错' : '一般'}`);
                    localStorage.setItem(`${planId}-life-gratitude-day${day}`, `感谢今天的${['学习机会', '健康身体', '家人支持', '工作进展', '美好天气'][Math.floor(Math.random() * 5)]}`);
                    localStorage.setItem(`${planId}-life-weight-day${day}`, (70 + Math.random() * 10).toFixed(1)); // 70-80 kg
                    localStorage.setItem(`${planId}-life-fat-day${day}`, (15 + Math.random() * 10).toFixed(1)); // 15-25%
                    localStorage.setItem(`${planId}-life-body-status-day${day}`, finalMood > 6 ? '良好' : finalMood > 4 ? '一般' : '疲劳');
                    
                    // Calorie data (affected by mood and discipline)
                    const baseCalories = 1800;
                    const moodEffect = (finalMood - 5) * 50; // ±250 calories based on mood
                    const totalIntake = baseCalories + moodEffect + (Math.random() - 0.5) * 200;
                    localStorage.setItem(`${planId}-life-breakfast-day${day}`, Math.round(totalIntake * 0.25)); // 25%
                    localStorage.setItem(`${planId}-life-lunch-day${day}`, Math.round(totalIntake * 0.4)); // 40%
                    localStorage.setItem(`${planId}-life-dinner-day${day}`, Math.round(totalIntake * 0.35)); // 35%
                    localStorage.setItem(`${planId}-life-total-intake-day${day}`, Math.round(totalIntake));
                    
                    // Calorie deficit (affected by P90X and total intake)
                    const burnedCalories = 1600 + (didP90X ? 400 : 0) + Math.random() * 200;
                    const deficit = burnedCalories - totalIntake;
                    localStorage.setItem(`${planId}-life-deficit-day${day}`, Math.round(deficit));
                }

                // Add some books
                const books = [
                    {title: '深度工作', pages: 280},
                    {title: '原子习惯', pages: 320},
                    {title: '思考快与慢', pages: 450},
                    {title: '刻意练习', pages: 280},
                    {title: '心流', pages: 350}
                ];

                books.forEach((book, index) => {
                    localStorage.setItem(`${planId}-book-goal-${index + 1}`, book.title);
                    localStorage.setItem(`${planId}-book-goal-${index + 1}-pages`, book.pages);
                });

                // Set reading book selections for each day
                for (let day = 1; day <= 30; day++) {
                    const bookIndex = Math.floor((day - 1) / 6) + 1; // Change book every 6 days
                    localStorage.setItem(`${planId}-read-book-day${day}`, bookIndex);
                }

                console.log('测试数据生成完成！');
                showStatus('✅ 30天测试数据生成成功！包含睡眠、心情、运动、学习、习惯等完整数据，具有真实的相关性模式。');
                
            } catch (error) {
                console.error('生成测试数据时出错:', error);
                showStatus('❌ 生成测试数据时出错: ' + error.message, true);
            }
        }

        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可撤销！')) {
                localStorage.clear();
                showStatus('🗑️ 所有数据已清空');
            }
        }

        function openMainApp() {
            window.open('code (4).html', '_blank');
        }
    </script>
</body>
</html>
