{"Keys": ["com.unity.services.core.version", "com.unity.services.core.initializer-assembly-qualified-names", "com.unity.services.analytics.version", "com.unity.services.analytics.initializer-assembly-qualified-names", "com.unity.services.core.all-package-names", "com.unity.services.core.environment-name"], "Values": [{"m_Value": "1.14.0", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Core.Registration.CorePackageInitializer, Unity.Services.Core.Registration, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null;Unity.Services.Core.Internal.IInitializablePackageV2, Unity.Services.Core.Internal, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "6.0.2", "m_IsReadOnly": true}, {"m_Value": "Ua2CoreInitializeCallback, Unity.Services.Analytics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "com.unity.services.core;com.unity.services.analytics", "m_IsReadOnly": false}, {"m_Value": "production", "m_IsReadOnly": false}]}