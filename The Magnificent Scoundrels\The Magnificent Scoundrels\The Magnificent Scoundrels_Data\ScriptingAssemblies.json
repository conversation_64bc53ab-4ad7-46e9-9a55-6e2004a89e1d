{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp-firstpass.dll", "Assembly-CSharp.dll", "UniTask.Addressables.dll", "com.rlabrecque.steamworks.net.dll", "Unity.2D.IK.Runtime.dll", "Excel.dll", "IngameDebugConsole.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "YooAsset.dll", "Unity.ScriptableBuildPipeline.dll", "ICSharpCode.SharpZipLib.dll", "Drawing.dll", "Fungus.dll", "Unity.RenderPipelines.Universal.Config.Runtime.dll", "CFXRDemo.dll", "Unity.VisualScripting.Flow.dll", "SimpleWebTransport.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "MessagePack.dll", "Unity.Collections.dll", "AstarPathfindingProject.dll", "UniTask.dll", "Unity.2D.SpriteShape.Runtime.dll", "UniTask.TextMeshPro.dll", "Unity.2D.PixelPerfect.dll", "Unity.TextMeshPro.dll", "CFXRRuntime.dll", "FungusExamples.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "UniTask.Linq.dll", "LitJson.dll", "PackageTools.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.Burst.dll", "HotUpdate.dll", "Unity.VisualScripting.Core.dll", "Unity.2D.Animation.Runtime.dll", "Unity.2D.Tilemap.Extras.dll", "UnityEngine.UI.dll", "Unity.InternalAPIEngineBridge.001.dll", "Unity.2D.Common.Runtime.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Unity.Timeline.dll", "AstarPathfindingProjectExamples.dll", "Unity.Mathematics.dll", "kcp.dll", "Unity.RenderPipelines.Universal.Shaders.dll", "Telepathy.dll", "KinoBloom.Runtime.dll", "Unity.VisualScripting.State.dll", "Elringus.SpriteGlow.Runtime.dll", "UniTask.DOTween.dll", "Pathfinding.Poly2Tri.dll", "Unity.Collections.LowLevel.ILSupport.dll", "I18N.dll", "I18N.MidEast.dll", "Pathfinding.ClipperLib.dll", "Unity.VisualScripting.Antlr3.Runtime.dll", "Unity.Burst.Unsafe.dll", "I18N.Rare.dll", "I18N.Other.dll", "Pathfinding.Ionic.Zip.Reduced.dll", "I18N.West.dll", "I18N.CJK.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}