[
  {
    "CategoryLocalizorKey": "help_general",
    "pages": [
	  {
        "slug": "general-movement",
        "pageLocalizorKeyPrefix": "common_movement"
      },
      {
        "slug": "general-energy",
        "pageLocalizorKeyPrefix": "common_energy"
      },
      {
        "slug": "general-hunger",
        "pageLocalizorKeyPrefix": "common_hunger"
      },
      {
        "slug": "general-happiness",
        "pageLocalizorKeyPrefix": "common_happiness"
      },
      {
        "slug": "general-personal-goals",
        "pageLocalizorKeyPrefix": "common_personal_goals"
      },      	  
	  {
        "slug": "general-bizphone",
        "pageLocalizorKeyPrefix": "common_bizphone"
      },
	  {
        "slug": "general-accessories",
        "pageLocalizorKeyPrefix": "common_accessories"
      }
    ]
  },  
  {
    "CategoryLocalizorKey": "common_finance",
  	"pages": [
	  {
		"slug": "finance-econoview",
		"pageLocalizorKeyPrefix": "bizphone_econoview"
	  },
	  {
        "slug": "finance-banks",
        "pageLocalizorKeyPrefix": "finance_banks"
      },
	  {
        "slug": "finance-negativeinterest",
        "pageLocalizorKeyPrefix": "finance_negativeinterest"
      },
	  {
		"slug": "finace-taxes",
		"pageLocalizorKeyPrefix": "finance_taxes"
  	  }
	]
  },
  {
    "CategoryLocalizorKey": "building_title",
  	"pages": [	  
	  {
        "slug": "building-types",
        "pageLocalizorKeyPrefix": "building_types"
      },
	  {
        "slug": "building-traffic",
        "pageLocalizorKeyPrefix": "building_traffic"
      },
	  {
        "slug": "building-limit",
        "pageLocalizorKeyPrefix": "building_limit"
      },
	  {
        "slug": "building-customerdemands",
        "pageLocalizorKeyPrefix": "building_customerdemands"
      },
	  {
        "slug": "building-customersovertime",
        "pageLocalizorKeyPrefix": "building_customersovertime"
      },		
	  {
        "slug": "building-interiordesigner",
        "pageLocalizorKeyPrefix": "building_interiordesigner"
      },
	  {
        "slug": "building-blueprints",
        "pageLocalizorKeyPrefix": "building_blueprints"
      },	  
	  {
        "slug": "building-interiorinstallationfirms",
        "pageLocalizorKeyPrefix": "building_installationfirms"
      },
	  {
        "slug": "building-movingservice",
        "pageLocalizorKeyPrefix": "building_movingservice"
      },
	  {
        "slug": "building-security",
        "pageLocalizorKeyPrefix": "building_security"
      }	  
	]
  }, 
  {
    "CategoryLocalizorKey": "common_business_types",
    "pages": [      
      {
        "slug": "businesstypes-bookstore",
        "pageLocalizorKeyPrefix": "businesstype_bookstore"
      },
	  {
        "slug": "businesstypes-clothingstore",
        "pageLocalizorKeyPrefix": "businesstype_clothingstore"
      },
	  {
        "slug": "businesstypes-coffeeshop",
        "pageLocalizorKeyPrefix": "businesstype_coffeeshop"
      },
	  {
        "slug": "businesstypes-electronicsstore",
        "pageLocalizorKeyPrefix": "businesstype_electronicsstore"
      },	
	  {
        "slug": "businesstypes-factory",
        "pageLocalizorKeyPrefix": "businesstype_factory"
      },
      {
        "slug": "businesstypes-fastfoodrestaurant",
        "pageLocalizorKeyPrefix": "businesstype_fastfoodrestaurant"
      },
	  {
        "slug": "businesstypes-florist",
        "pageLocalizorKeyPrefix": "businesstype_florist"
      },
	  {
        "slug": "businesstypes-fruitandvegetablestore",
        "pageLocalizorKeyPrefix": "businesstype_fruitandvegetablestore"
      },
	  {
        "slug": "businesstypes-giftshop",
        "pageLocalizorKeyPrefix": "businesstype_giftshop"
      },
	  {
        "slug": "businesstypes-graphicdesigner",
        "pageLocalizorKeyPrefix": "businesstype_graphicdesigner"
      },
	  {
        "slug": "businesstypes-gym",
        "pageLocalizorKeyPrefix": "businesstype_gym"
      },
	  {
        "slug": "businesstypes-hairdresser",
        "pageLocalizorKeyPrefix": "businesstype_hairdresser"
      },
	  {
        "slug": "businesstypes-headquarters",
        "pageLocalizorKeyPrefix": "businesstype_headquarters"
      },
	  {
        "slug": "businesstypes-jewelrystore",
        "pageLocalizorKeyPrefix": "businesstype_jewelrystore"
      },
	  {
        "slug": "businesstypes-lawfirm",
        "pageLocalizorKeyPrefix": "businesstype_lawfirm"
      },
      {
        "slug": "businesstypes-liquorstore",
        "pageLocalizorKeyPrefix": "businesstype_liquorstore"
      },
	  {
        "slug": "businesstypes-nightclub",
        "pageLocalizorKeyPrefix": "businesstype_nightclub"
      },
      {
        "slug": "businesstypes-supermarket",
        "pageLocalizorKeyPrefix": "businesstype_supermarket"
      },
	  {
        "slug": "businesstypes-warehouse",
        "pageLocalizorKeyPrefix": "businesstype_warehouse"
      },
      {
        "slug": "businesstypes-webdevelopmentagency",
        "pageLocalizorKeyPrefix": "businesstype_webdevelopmentagency"
      }	  
    ]
  },
  {
	"CategoryLocalizorKey": "employee_types",
	"pages": [	  	  		  	  
	  {
		"slug": "skill-cleaning",
		"pageLocalizorKeyPrefix": "skillname_cleaning"
	  },
	  {
		"slug": "skill-customerservice",
		"pageLocalizorKeyPrefix": "skillname_customerservice"
	  },
	  {
		"slug": "skill-deliverydriver",
		"pageLocalizorKeyPrefix": "skillname_deliverydriver"
	  },
	  {
		"slug": "skill-dj",
		"pageLocalizorKeyPrefix": "skillname_dj"
	  },
	  {
		"slug": "skill-factoryworker",
		"pageLocalizorKeyPrefix": "skillname_factoryworker"
	  },
	  {
		"slug": "skill-graphicdesigner",
		"pageLocalizorKeyPrefix": "skillname_graphicdesigner"
	  },
	  {
		"slug": "skill-gymtrainer",
		"pageLocalizorKeyPrefix": "skillname_gymtrainer"
	  },		
	  {
		"slug": "skill-hairstylist",
		"pageLocalizorKeyPrefix": "skillname_hairstylist"
	  },
	  {
		"slug": "skill-headhunter",
		"pageLocalizorKeyPrefix": "skillname_headhunter"
	  },
	  {
		"slug": "skill-hrmanager",
		"pageLocalizorKeyPrefix": "skillname_hrmanager"
	  },
	  {
		"slug": "skill-lawyer",
		"pageLocalizorKeyPrefix": "skillname_lawyer"
	  },	  
	  {
		"slug": "skill-logisticsmanager",
		"pageLocalizorKeyPrefix": "skillname_logisticsmanager"
	  },
	  {
		"slug": "skill-programmer",
		"pageLocalizorKeyPrefix": "skillname_programmer"
	  },
	  {
		"slug": "skill-purchasingagent",
		"pageLocalizorKeyPrefix": "skillname_purchasingagent"
	  },	  
	  {
		"slug": "skill-securityguard",
		"pageLocalizorKeyPrefix": "skillname_securityguard"
	  }
	]
  },
  {
    "CategoryLocalizorKey": "employee_management",
  	"pages": [
	  {
		"slug": "skill-myemployees",
		"pageLocalizorKeyPrefix": "bizphone_myemployees"
	  },
	  {
		"slug": "employee_skill",
		"pageLocalizorKeyPrefix": "employee_skill"
	  },
	  {
		"slug": "employees-demands-overview",
		"pageLocalizorKeyPrefix": "employee_demands_overview"
	  },
	  {
		"slug": "employees-demands-benefits",
		"pageLocalizorKeyPrefix": "employee_demands_benefits"
	  },
	  {
		"slug": "employees-demands-environment",
		"pageLocalizorKeyPrefix": "employee_demands_environment"
	  },
	  {
		"slug": "employees-demands-equipment",
		"pageLocalizorKeyPrefix": "employee_demands_equipment"
	  },
	  {
		"slug": "employees-demands-schedule",
		"pageLocalizorKeyPrefix": "employee_demands_schedule"
	  }	  	  
	]
  },
  {
    "CategoryLocalizorKey": "common_sellable_products",
    "pages": [	  
	  {
        "slug": "products-smartphone1",
        "pageLocalizorKeyPrefix": "itemname_smartphone1"
      },
	  {
        "slug": "products-smartwatch2",
        "pageLocalizorKeyPrefix": "itemname_smartwatch2"
      },
	  {
        "slug": "products-apple",
        "pageLocalizorKeyPrefix": "itemname_apple"
      },
	  {
        "slug": "products-banana",
        "pageLocalizorKeyPrefix": "itemname_banana"
      },
	  {
        "slug": "products-carrot",
        "pageLocalizorKeyPrefix": "itemname_carrot"
      },
	  {
        "slug": "products-lettuce",
        "pageLocalizorKeyPrefix": "itemname_lettuce"
      },
	  {
        "slug": "products-pear",
        "pageLocalizorKeyPrefix": "itemname_pear"
      },
	  {
        "slug": "products-tomato",
        "pageLocalizorKeyPrefix": "itemname_tomato"
      },
	  {
        "slug": "products-beer",
        "pageLocalizorKeyPrefix": "itemname_beer"
      },
      {
        "slug": "products-bottleofwine",
        "pageLocalizorKeyPrefix": "itemname_bottleofwine"
      },
	  {
        "slug": "products-burger",
        "pageLocalizorKeyPrefix": "itemname_burger"
      },	  
	  {
        "slug": "products-kabob",
        "pageLocalizorKeyPrefix": "itemname_kabob"
      },
	  {
        "slug": "products-cigar",
        "pageLocalizorKeyPrefix": "itemname_cigar"
      },
	  {
        "slug": "products-cigarette",
        "pageLocalizorKeyPrefix": "itemname_cigarette"
      },
	  {
        "slug": "products-classiccheapfemaleclothing",
        "pageLocalizorKeyPrefix": "itemname_classiccheapfemaleclothing"
      },
	  {
        "slug": "products-classiccheapmaleclothing",
        "pageLocalizorKeyPrefix": "itemname_classiccheapmaleclothing"
      },
	  {
        "slug": "products-classicexpensivefemaleclothing",
        "pageLocalizorKeyPrefix": "itemname_classicexpensivefemaleclothing"
      },
	  {
        "slug": "products-classicexpensivemaleclothing",
        "pageLocalizorKeyPrefix": "itemname_classicexpensivemaleclothing"
      },
	  {
        "slug": "products-moderncheapfemaleclothing",
        "pageLocalizorKeyPrefix": "itemname_moderncheapfemaleclothing"
      },
	  {
        "slug": "products-moderncheapmaleclothing",
        "pageLocalizorKeyPrefix": "itemname_moderncheapmaleclothing"
      },
	  {
        "slug": "products-modernexpensivefemaleclothing",
        "pageLocalizorKeyPrefix": "itemname_modernexpensivefemaleclothing"
      },
	  {
        "slug": "products-modernexpensivemaleclothing",
        "pageLocalizorKeyPrefix": "itemname_modernexpensivemaleclothing"
      },
	  {
        "slug": "fees-coatcheckfee",
        "pageLocalizorKeyPrefix": "itemname_coatcheckfee"
      },	  
      {
        "slug": "products-croissant",
        "pageLocalizorKeyPrefix": "itemname_croissant"
      },
	  {
        "slug": "products-cupofcoffee",
        "pageLocalizorKeyPrefix": "itemname_cupofcoffee"
      },
      {
        "slug": "products-cupcake",
        "pageLocalizorKeyPrefix": "itemname_cupcake"
      },	  
      {
        "slug": "products-donut",
        "pageLocalizorKeyPrefix": "itemname_donut"
      },
	  {
        "slug": "products-cheapflower",
        "pageLocalizorKeyPrefix": "itemname_cheapflower"
      },
	  {
        "slug": "products-expensiveflower",
        "pageLocalizorKeyPrefix": "itemname_expensiveflower"    
	  },
	  {
        "slug": "products-frenchfries",
        "pageLocalizorKeyPrefix": "itemname_frenchfries"
      },
      {
        "slug": "products-freshfood",
        "pageLocalizorKeyPrefix": "itemname_freshfood"
      },
      {
        "slug": "products-frozenfood",
        "pageLocalizorKeyPrefix": "itemname_frozenfood"
      },	  		
	  {
        "slug": "products-cheapgift",
        "pageLocalizorKeyPrefix": "itemname_cheapgift"
      },	  
		{
        "slug": "products-expensivegift",
        "pageLocalizorKeyPrefix": "itemname_expensivegift"
      },	  		
	  {
        "slug": "fees-hourlygraphicdesignerfee",
        "pageLocalizorKeyPrefix": "itemname_hourlygraphicdesignerfee"
      },
	  {
        "slug": "fees-gymcovercharge",
        "pageLocalizorKeyPrefix": "itemname_gymcovercharge"
      },		
	  {
        "slug": "products-haircareproduct",
        "pageLocalizorKeyPrefix": "itemname_haircareproduct"
      },
	  {
        "slug": "fees-hairchemicalfee",
        "pageLocalizorKeyPrefix": "itemname_hairchemicalfee"
      },
	  {
        "slug": "fees-haircuttingfee",
        "pageLocalizorKeyPrefix": "itemname_haircuttingfee"
      },
	  {
        "slug": "fees-hairshampooingfee",
        "pageLocalizorKeyPrefix": "itemname_hairshampooingfee"
      },		
	  {
        "slug": "fees-hairstylingfee",
        "pageLocalizorKeyPrefix": "itemname_hairstylingfee"
      },
	  {
        "slug": "products-hotdog",
        "pageLocalizorKeyPrefix": "itemname_hotdog"
      },
		
	  {
        "slug": "products-icecream",
        "pageLocalizorKeyPrefix": "itemname_icecream"
      },
      {
        "slug": "products-cheapjewelry",
        "pageLocalizorKeyPrefix": "itemname_cheapjewelry"
      },
	  {
        "slug": "products-expensivejewelry",
        "pageLocalizorKeyPrefix": "itemname_expensivejewelry"
      },	  
	  {
        "slug": "fees-hourlylawyerfee",
        "pageLocalizorKeyPrefix": "itemname_hourlylawyerfee"
      },	  
	  {
		"slug": "products-limitededitionbook",
		"pageLocalizorKeyPrefix" : "itemname_limitededitionbook"
	  },
	  {
        "slug": "products-margarita",
        "pageLocalizorKeyPrefix": "itemname_margarita"
      },
	  {
        "slug": "products-martini",
        "pageLocalizorKeyPrefix": "itemname_martini"
      },			  
	  {
		"slug": "products-motivationalbook",
		"pageLocalizorKeyPrefix" : "itemname_motivationalbook"
	  },
	  {
        "slug": "fees-nightclubcovercharge",
        "pageLocalizorKeyPrefix": "itemname_nightclubcovercharge"
      },
	  {
        "slug": "products-earbuds01",
        "pageLocalizorKeyPrefix": "itemname_earbuds01"
      },
	  {
		"slug": "products-novel",
		"pageLocalizorKeyPrefix" : "itemname_novel"
	  },	
	  {
        "slug": "products-paperbag",
        "pageLocalizorKeyPrefix": "itemname_paperbag"
      },	  
      {
		"slug": "products-picturebook",
		"pageLocalizorKeyPrefix" : "itemname_picturebook"
	  },
      {
        "slug": "products-pizza",
        "pageLocalizorKeyPrefix": "itemname_pizza"
      },             
	  {
        "slug": "fees-hourlyprogrammerfee",
        "pageLocalizorKeyPrefix": "itemname_hourlyprogrammerfee"
      },
	  {
        "slug": "products-headphones01",
        "pageLocalizorKeyPrefix": "itemname_headphones01"
      },
      {
        "slug": "products-salad",
        "pageLocalizorKeyPrefix": "itemname_salad"
      },
	  {
        "slug": "products-sodacan",
        "pageLocalizorKeyPrefix": "itemname_sodacan"
      },	  
	  {
		"slug": "products-technicalmanual",
		"pageLocalizorKeyPrefix" : "itemname_technicalmanual"
	  },
	  {
		"slug": "products-umbrella",
		"pageLocalizorKeyPrefix" : "itemname_umbrella"
	  },
	  {
        "slug": "products-whisky",
        "pageLocalizorKeyPrefix": "itemname_whisky"
      },
	  {
		"slug": "products-youngnovel",
		"pageLocalizorKeyPrefix" : "itemname_youngnovel"
	  },
	  {
        "slug": "products-smartphone2",
        "pageLocalizorKeyPrefix": "itemname_smartphone2"
      },
	  {
        "slug": "products-smartwatch1",
        "pageLocalizorKeyPrefix": "itemname_smartwatch1"
      },
    ]
  },  
  {
    "CategoryLocalizorKey": "help_importers",
    "pages": [
	  {
        "slug": "wholesalers-shortages",
        "pageLocalizorKeyPrefix": "wholesalers_shortages"
      },
	  {
        "slug": "wholesalers-overview",
        "pageLocalizorKeyPrefix": "wholesalers_overview"
      },
	  {
        "slug": "importers-contract",
        "pageLocalizorKeyPrefix": "importers_contract"
      },
	  {
        "slug": "importers-overview",
        "pageLocalizorKeyPrefix": "importers_overview"
      },
	  {
        "slug": "logistics-overview",
        "pageLocalizorKeyPrefix": "logistics_overview"
      },
	  {
        "slug": "wholesalers-hudson",
        "pageLocalizorKeyPrefix": "wholesalers_hudson"
      },
	  {
        "slug": "wholesalers-metro",
        "pageLocalizorKeyPrefix": "wholesalers_metro"
      },
	  {
        "slug": "wholesalers-nydistro",
        "pageLocalizorKeyPrefix": "wholesalers_nydistro"
      },
	  {
        "slug": "wholesalers-totalproduce",
        "pageLocalizorKeyPrefix": "wholesalers_totalproduce"
      },	  
      {
        "slug": "importers-jetcargo",
        "pageLocalizorKeyPrefix": "importertypename_jetcargo"
      },
      {
        "slug": "importers-seaside",
        "pageLocalizorKeyPrefix": "importertypename_seaside"
      },
      {
        "slug": "importers-unitedocean",
        "pageLocalizorKeyPrefix": "importertypename_unitedocean"
      },
      {
        "slug": "importers-bluestone",
        "pageLocalizorKeyPrefix": "importertypename_bluestone"
      },
	  {
        "slug": "importers-lunartide",
        "pageLocalizorKeyPrefix": "importertypename_lunartide"
      },
	  {
        "slug": "importers-maritimefreight",
        "pageLocalizorKeyPrefix": "importertypename_maritimefreight"
      },
	  {
        "slug": "importers-aquaticbay",
        "pageLocalizorKeyPrefix": "importertypename_aquaticbay"
      },	  
    ]
  },  
  {
    "CategoryLocalizorKey": "common_furniture",
    "pages": [	  	  
	  {
        "slug": "furniture-bakeryshowcase",
        "pageLocalizorKeyPrefix": "itemname_bakeryshowcase"
      },	  
	  {
        "slug": "furniture-barstool",
        "pageLocalizorKeyPrefix": "itemname_barstool"
      },
	  {
        "slug": "furniture-gamingcomputer",
        "pageLocalizorKeyPrefix": "itemname_gamingcomputer"
      },
	  {
        "slug": "furniture-singlebookshelf",
        "pageLocalizorKeyPrefix": "itemname_singlebookshelf"
      },
	  {
        "slug": "furniture-decorativebookshelf",
        "pageLocalizorKeyPrefix": "itemname_decorativebookshelf"
      },
	  {
        "slug": "furniture-largebookshelf",
        "pageLocalizorKeyPrefix": "itemname_largebookshelf"
      },
	  {
        "slug": "furniture-largedecorativebookshelf",
        "pageLocalizorKeyPrefix": "itemname_largedecorativebookshelf"
      },
	  {
        "slug": "furniture-boxingbag",
        "pageLocalizorKeyPrefix": "itemname_boxingbag"
      },
	  {
        "slug": "furniture-cabinet",
        "pageLocalizorKeyPrefix": "itemname_cabinet"
      },
	  {
        "slug": "furniture-cashregister",
        "pageLocalizorKeyPrefix": "itemname_cashregister"
      },
	  {
        "slug": "furniture-chairgroup",
        "pageLocalizorKeyPrefix": "itemname_chairgroup"
      },
	  {
        "slug": "furniture-cheapcoffeemachine",
        "pageLocalizorKeyPrefix": "itemname_cheapcoffeemachine"
      },
	  {
        "slug": "furniture-checkoutcounterleft",
        "pageLocalizorKeyPrefix": "itemname_checkoutcounterleft"
      },
	  {
        "slug": "furniture-checkoutcounterright",
        "pageLocalizorKeyPrefix": "itemname_checkoutcounterright"
      },	  
 	  {
        "slug": "furniture-classicloudspeakerbig",
        "pageLocalizorKeyPrefix": "itemname_classicloudspeakerbig"
      },
	  {
        "slug": "furniture-classicloudspeakermedium",
        "pageLocalizorKeyPrefix": "itemname_classicloudspeakermedium"
      },
	  {
        "slug": "furniture-classicloudspeakersmall",
        "pageLocalizorKeyPrefix": "itemname_classicloudspeakersmall"
      },
	  {
        "slug": "furniture-classicphone",
        "pageLocalizorKeyPrefix": "itemname_classicphone"
      },
	  {
        "slug": "furniture-cleaningstation",
        "pageLocalizorKeyPrefix": "itemname_cleaningstation"
      },
	  {
        "slug": "furniture-clothingrack",
        "pageLocalizorKeyPrefix": "itemname_clothingrack"
      },	  
      {
        "slug": "furniture-clothingrackangled",
        "pageLocalizorKeyPrefix": "itemname_clothingrackangled"
      },
	  {
        "slug": "furniture-coatcheckleft",
        "pageLocalizorKeyPrefix": "itemname_coatcheckleft"
      },
	  {
        "slug": "furniture-coatcheckright",
        "pageLocalizorKeyPrefix": "itemname_coatcheckright"
      },	
	  {
        "slug": "furniture-cocktailbar",
        "pageLocalizorKeyPrefix": "itemname_cocktailbar"
      },
	  {
        "slug": "furniture-woodencocktailbar",
        "pageLocalizorKeyPrefix": "itemname_woodencocktailbar"
      },
	  {
        "slug": "furniture-largecocktailbar",
        "pageLocalizorKeyPrefix": "itemname_largecocktailbar"
      },			
	  {
        "slug": "furniture-largewoodencocktailbar",
        "pageLocalizorKeyPrefix": "itemname_largewoodencocktailbar"
      },
	  {
        "slug": "furniture-cocktailbarbeershelf",
        "pageLocalizorKeyPrefix": "itemname_cocktailbarbeershelf"
      },
	  {
        "slug": "furniture-woodencocktailbarbeershelf",
        "pageLocalizorKeyPrefix": "itemname_woodencocktailbarbeershelf"
      },
	  {
        "slug": "furniture-cocktailbarcorner",
        "pageLocalizorKeyPrefix": "itemname_cocktailbarcorner"
      },
	  {
        "slug": "furniture-woodencocktailbarcorner",
        "pageLocalizorKeyPrefix": "itemname_woodencocktailbarcorner"
      },
	  {
        "slug": "furniture-cocktailbardrinkscounter",
        "pageLocalizorKeyPrefix": "itemname_cocktailbardrinkscounter"
      },	 	  	  	  
	  	  
	  {
        "slug": "furniture-woodencocktailbardrinkscounter",
        "pageLocalizorKeyPrefix": "itemname_woodencocktailbardrinkscounter"
      },
	  {
        "slug": "furniture-computer",
        "pageLocalizorKeyPrefix": "itemname_computer"
      },
	  {
        "slug": "furniture-computermonitor",
        "pageLocalizorKeyPrefix": "itemname_computermonitor"
      },
	  {
        "slug": "furniture-computergroup",
        "pageLocalizorKeyPrefix": "itemname_computergroup"
      },
	  {
        "slug": "furniture-computerworkstation",
        "pageLocalizorKeyPrefix": "itemname_computerworkstation"
      },	  
	  {
        "slug": "furniture-deskgroup",
        "pageLocalizorKeyPrefix": "itemname_deskgroup"
      },	  
	  {
        "slug": "furniture-diningtablegroup",
        "pageLocalizorKeyPrefix": "itemname_diningtablegroup"
      },
	  {
        "slug": "furniture-flatwoodendisplaystand",
        "pageLocalizorKeyPrefix": "itemname_flatwoodendisplaystand"
      },
	  {
        "slug": "furniture-angledwoodendisplaystand",
        "pageLocalizorKeyPrefix": "itemname_angledwoodendisplaystand"
      },
	  {
        "slug": "furniture-productdisplaystandtiered",
        "pageLocalizorKeyPrefix": "itemname_productdisplaystandtiered"
      },	  
	  {
        "slug": "furniture-productdisplaytable",
        "pageLocalizorKeyPrefix": "itemname_productdisplaytable"
      },
	  {
        "slug": "furniture-djbooth",
        "pageLocalizorKeyPrefix": "itemname_djbooth"
      },	  
	  {
        "slug": "furniture-drinksfridge",
        "pageLocalizorKeyPrefix": "itemname_drinksfridge"
      },
	  {
        "slug": "furniture-largedrinksfridge",
        "pageLocalizorKeyPrefix": "itemname_largedrinksfridge"
      },
	  {
        "slug": "furniture-drinksshelf",
        "pageLocalizorKeyPrefix": "itemname_drinksshelf"
      },
	  {
        "slug": "furniture-earbudsstand",
        "pageLocalizorKeyPrefix": "itemname_earbudsstand"
      },		
	  {
        "slug": "furniture-wallmountedtv",
        "pageLocalizorKeyPrefix": "itemname_wallmountedtv"
      },
	  {
        "slug": "furniture-officedesk2left",
        "pageLocalizorKeyPrefix": "itemname_officedesk2left"
      },
	  {
        "slug": "furniture-officedesk2right",
        "pageLocalizorKeyPrefix": "itemname_officedesk2right"
      },
	  {
        "slug": "furniture-fitball",
        "pageLocalizorKeyPrefix": "itemname_fitball"
      },
	  {
        "slug": "furniture-fitnessplanningboard",
        "pageLocalizorKeyPrefix": "itemname_fitnessplanningboard"
      },
	  {
        "slug": "furniture-fitnesstrampoline",
        "pageLocalizorKeyPrefix": "itemname_fitnesstrampoline"
      },	  
	  {
        "slug": "furniture-fruitbowl",
        "pageLocalizorKeyPrefix": "itemname_fruitbowl"
      },	  
	  {
        "slug": "furniture-gamingchair",
        "pageLocalizorKeyPrefix": "itemname_gamingchair"
      },		
	  {
        "slug": "furniture-armchair2",
        "pageLocalizorKeyPrefix": "itemname_armchair2"
      },
	  {
        "slug": "furniture-graphictablet",
        "pageLocalizorKeyPrefix": "itemname_graphictablet"
      },
	  {
        "slug": "furniture-graphictabletwithscreen",
        "pageLocalizorKeyPrefix": "itemname_graphictabletwithscreen"
      },
	  {
        "slug": "furniture-gymlockers",
        "pageLocalizorKeyPrefix": "itemname_gymlockers"
      },
	  {
        "slug": "furniture-gymmat",
        "pageLocalizorKeyPrefix": "itemname_gymmat"
      },
	  {
        "slug": "furniture-hairdresserchair",
        "pageLocalizorKeyPrefix": "itemname_hairdresserchair"
      },	
	  {
        "slug": "furniture-hairdresserchairmodern",
        "pageLocalizorKeyPrefix": "itemname_hairdresserchairmodern"
      },	
	  {
        "slug": "furniture-hairdresserheadwash",
        "pageLocalizorKeyPrefix": "itemname_hairdresserheadwash"
      },
	  {
        "slug": "furniture-hairdressershelf",
        "pageLocalizorKeyPrefix": "itemname_hairdressershelf"
      },
	  {
        "slug": "furniture-headphonesstand",
        "pageLocalizorKeyPrefix": "itemname_headphonesstand"
      },	  
	  {
        "slug": "furniture-hightable",
        "pageLocalizorKeyPrefix": "itemname_hightable"
      },
	  {
        "slug": "furniture-hotdoggrill",
        "pageLocalizorKeyPrefix": "itemname_hotdoggrill"
      },
	  {
        "slug": "furniture-icecreamcounter",
        "pageLocalizorKeyPrefix": "itemname_icecreamcounter"
      },			  
	  {
        "slug": "furniture-industrialcoffeemachine",
        "pageLocalizorKeyPrefix": "itemname_industrialcoffeemachine"
      },	  
	  {
        "slug": "furniture-industrialfreezer",
        "pageLocalizorKeyPrefix": "itemname_industrialfreezer"
      },
	  {
        "slug": "furniture-smallindustrialfreezer",
        "pageLocalizorKeyPrefix": "itemname_smallindustrialfreezer"
      },
      {
        "slug": "furniture-industrialfridge",
        "pageLocalizorKeyPrefix": "itemname_industrialfridge"
      },            
      {
        "slug": "furniture-industrialfryermachine",
        "pageLocalizorKeyPrefix": "itemname_industrialfryermachine"
      },      
      {
        "slug": "furniture-industrialgrill",
        "pageLocalizorKeyPrefix": "itemname_industrialgrill"
      },
	  {
        "slug": "furniture-eameschair",
        "pageLocalizorKeyPrefix": "itemname_eameschair"
      },
	  {
        "slug": "furniture-jewelrybox",
        "pageLocalizorKeyPrefix": "itemname_jewelrybox"
      },	  
	  {
        "slug": "furniture-loudspeaker2",
        "pageLocalizorKeyPrefix": "itemname_loudspeaker2"
      },
	  {
        "slug": "furniture-loudspeaker1",
        "pageLocalizorKeyPrefix": "itemname_loudspeaker1"
      },	  
	  {
        "slug": "furniture-loudspeaker4",
        "pageLocalizorKeyPrefix": "itemname_loudspeaker4"
      },	  
	  {
        "slug": "furniture-jewelryfloorshowcase",
        "pageLocalizorKeyPrefix": "itemname_jewelryfloorshowcase"
      },
	  {
		"slug": "furniture-jobboard",
        "pageLocalizorKeyPrefix": "itemname_jobboard"
      },
	  {
        "slug": "furniture-kettlebells",
        "pageLocalizorKeyPrefix": "itemname_kettlebells"
      },
	  {
        "slug": "furniture-kingsizebed",
        "pageLocalizorKeyPrefix": "itemname_kingsizebed"
      },
	  {
        "slug": "furniture-laptop",
        "pageLocalizorKeyPrefix": "itemname_laptop"
      },
	  {
        "slug": "furniture-loadedbarbell",
        "pageLocalizorKeyPrefix": "itemname_loadedbarbell"
      },
	  {
        "slug": "furniture-largemeetingtable",
        "pageLocalizorKeyPrefix": "itemname_largemeetingtable"
      },	  
	  {
        "slug": "furniture-modularsofa1l",
        "pageLocalizorKeyPrefix": "itemname_modularsofa1l"
      },
	  {
        "slug": "furniture-modularsofa1m",
        "pageLocalizorKeyPrefix": "itemname_modularsofa1m"
      },
  	  {
        "slug": "furniture-modularsofa1r",
        "pageLocalizorKeyPrefix": "itemname_modularsofa1r"
      },
	  
	  {
        "slug": "furniture-mousepad",
        "pageLocalizorKeyPrefix": "itemname_mousepad"
      },
	  {
        "slug": "furniture-multipurposechair",
        "pageLocalizorKeyPrefix": "itemname_multipurposechair"
      },
	  {
        "slug": "furniture-musicgroup",
        "pageLocalizorKeyPrefix": "itemname_musicgroup"
      },
	  {
        "slug": "furniture-officechair",
        "pageLocalizorKeyPrefix": "itemname_officechair"
      },
	  {
        "slug": "furniture-officephone",
        "pageLocalizorKeyPrefix": "itemname_officephone"
      },
	  {
        "slug": "furniture-loudspeaker3",
        "pageLocalizorKeyPrefix": "itemname_loudspeaker3"
      },
	  {
        "slug": "furniture-expensivetv",
        "pageLocalizorKeyPrefix": "itemname_expensivetv"
      },
	  {
        "slug": "furniture-palletshelf",
        "pageLocalizorKeyPrefix": "itemname_palletshelf"
      },
	  {
        "slug": "furniture-itemgrouppointofsale",
        "pageLocalizorKeyPrefix": "itemname_itemgrouppointofsale"
      },		
	  {
        "slug": "furniture-pizzaoven",
        "pageLocalizorKeyPrefix": "itemname_pizzaoven"
      },	  
	  {
        "slug": "furniture-cornersofa01",
        "pageLocalizorKeyPrefix": "itemname_cornersofa01"
      },
	  {
        "slug": "furniture-cornersofa02left",
        "pageLocalizorKeyPrefix": "itemname_cornersofa02left"
      },
	  {
        "slug": "furniture-cornersofa02right",
        "pageLocalizorKeyPrefix": "itemname_cornersofa02right"
      },		  
	  {
        "slug": "furniture-productpanel",
        "pageLocalizorKeyPrefix": "itemname_productpanel"
      },
	  {
        "slug": "furniture-publicshower",
        "pageLocalizorKeyPrefix": "itemname_publicshower"
      },
	  {
        "slug": "furniture-pullupbar",
        "pageLocalizorKeyPrefix": "itemname_pullupbar"
      },
	  {
        "slug": "furniture-regularchair",
        "pageLocalizorKeyPrefix": "itemname_regularchair"
      },	
	  {
        "slug": "furniture-restaurantbooth",
        "pageLocalizorKeyPrefix": "itemname_restaurantbooth"
      },	  
	  {
        "slug": "furniture-roundtable",
        "pageLocalizorKeyPrefix": "itemname_roundtable"
      },
	  {
        "slug": "furniture-roundedshelf",
        "pageLocalizorKeyPrefix": "itemname_roundedshelf"
      },	  
	  {
        "slug": "furniture-saladbar",
        "pageLocalizorKeyPrefix": "itemname_saladbar"
      },	  
	  {
        "slug": "furniture-securitycamera",
        "pageLocalizorKeyPrefix": "itemname_securitycamera"
      },
	  {
        "slug": "furniture-securitycameraroof",
        "pageLocalizorKeyPrefix": "itemname_securitycameraroof"
      },
	  {
        "slug": "furniture-securityguardlocker",
        "pageLocalizorKeyPrefix": "itemname_securityguardlocker"
      },
	  {
        "slug": "furniture-securitypanel",
        "pageLocalizorKeyPrefix": "itemname_securitypanel"
      },
	  {
        "slug": "furniture-securitysign",
        "pageLocalizorKeyPrefix": "itemname_securitysign"
      },
	  {
        "slug": "furniture-scorpiogamingsetup",
        "pageLocalizorKeyPrefix": "itemname_scorpiogamingsetup"
      },	  
	  {
        "slug": "furniture-sofagroup",
        "pageLocalizorKeyPrefix": "itemname_sofagroup"
      },
	  {
        "slug": "furniture-armchair1",
        "pageLocalizorKeyPrefix": "itemname_armchair1"
      },
	  {
        "slug": "furniture-squatsstation",
        "pageLocalizorKeyPrefix": "itemname_squatsstation"
      },
      {
        "slug": "furniture-stackofshoppingbaskets",
        "pageLocalizorKeyPrefix": "itemname_stackofshoppingbaskets"
      },
	  {
        "slug": "furniture-bed1",
        "pageLocalizorKeyPrefix": "itemname_bed1"
      },
	  {
        "slug": "furniture-standardfridge",
        "pageLocalizorKeyPrefix": "itemname_standardfridge"
      },
	  {
        "slug": "furniture-officedesk1",
        "pageLocalizorKeyPrefix": "itemname_officedesk1"
      },
	  {
        "slug": "furniture-table1",
        "pageLocalizorKeyPrefix": "itemname_table1"
      },
	  {
        "slug": "furniture-standingdigitalscale",
        "pageLocalizorKeyPrefix": "itemname_standingdigitalscale"
      },
	  {
        "slug": "furniture-storageshelf",
        "pageLocalizorKeyPrefix": "itemname_storageshelf"
      },
	  {
        "slug": "furniture-officechair2",
        "pageLocalizorKeyPrefix": "itemname_officechair2"
      },
	  {
		"slug": "furniture-tobaccoshelf1",
        "pageLocalizorKeyPrefix": "itemname_tobaccoshelf1"
      },
	  {
		"slug": "furniture-tobaccoshelf2",
        "pageLocalizorKeyPrefix": "itemname_tobaccoshelf2"
      },
	  {
		"slug": "furniture-tobaccoshelf3",
        "pageLocalizorKeyPrefix": "itemname_tobaccoshelf3"
      },
	  {
        "slug": "furniture-trashbin",
        "pageLocalizorKeyPrefix": "itemname_trashbin"
      },
	  {
        "slug": "furniture-treadmill",
        "pageLocalizorKeyPrefix": "itemname_treadmill"
      },
	  {
        "slug": "furniture-umbrellastand",
        "pageLocalizorKeyPrefix": "itemname_umbrellastand"
      },
	  {
        "slug": "furniture-uniformlocker",
        "pageLocalizorKeyPrefix": "itemname_uniformlocker"
      },
		
	  {
        "slug": "furniture-wardrobe",
        "pageLocalizorKeyPrefix": "itemname_wardrobe"
      },
	  {
        "slug": "furniture-wardrobewall",
        "pageLocalizorKeyPrefix": "itemname_wardrobewall"
      },
	  {
        "slug": "furniture-watercooler",
        "pageLocalizorKeyPrefix": "itemname_watercooler"
      },
      {
        "slug": "furniture-wineshelf",
        "pageLocalizorKeyPrefix": "itemname_wineshelf"
      },	  	  
	  {
        "slug": "furniture-woodenproductcrate",
        "pageLocalizorKeyPrefix": "itemname_woodenproductcrate"
      },
	  {
        "slug": "furniture-workoutbench",
        "pageLocalizorKeyPrefix": "itemname_workoutbench"
      },
	  {
		"slug": "furniture-itemgroupworkoutmachine",
        "pageLocalizorKeyPrefix": "itemname_itemgroupworkoutmachine"
      },
	  {
        "slug": "furniture-desktopcomputer",
        "pageLocalizorKeyPrefix": "itemname_desktopcomputer"
      }	  
    ]
  },
  {
    "CategoryLocalizorKey": "rivals_title",
    "pages": [
      {
        "slug": "rivals-overview",
        "pageLocalizorKeyPrefix": "rivals_overview"
	  },
	  {
        "slug": "special-rivals",
        "pageLocalizorKeyPrefix": "special_rivals"
	  },
	  {
        "slug": "minor-rivals",
        "pageLocalizorKeyPrefix": "minor_rivals"
	  },
	  {
        "slug": "counterattack-rivals",
        "pageLocalizorKeyPrefix": "counterattack_rivals"
	  },
	]
  },
  {
    "CategoryLocalizorKey": "common_factoryrecipes",
    "pages": [
	  {
        "slug": "recipes-smartphone1recipe",
        "pageLocalizorKeyPrefix": "recipes_smartphone1recipe"
      },
	  {
        "slug": "recipes-smartwatch2recipe",
        "pageLocalizorKeyPrefix": "recipes_smartwatch2recipe"
      },
	  {
        "slug": "recipes-burgerrecipe",
        "pageLocalizorKeyPrefix": "recipes_burgerrecipe"
      },
	  {
        "slug": "recipes-beerrecipe",
        "pageLocalizorKeyPrefix": "recipes_beerrecipe"
      },
	  {
        "slug": "recipes-kabobrecipe",
        "pageLocalizorKeyPrefix": "recipes_kabobrecipe"
      },
	  {
        "slug": "recipes-cigarrecipe",
        "pageLocalizorKeyPrefix": "recipes_cigarrecipe"
      },
	  {
        "slug": "recipes-cigaretterecipe",
        "pageLocalizorKeyPrefix": "recipes_cigaretterecipe"
      },
	  {
        "slug": "recipes-coffeerecipe",
        "pageLocalizorKeyPrefix": "recipes_coffeerecipe"
      },
	  {
        "slug": "recipes-croissantrecipe",
        "pageLocalizorKeyPrefix": "recipes_croissantrecipe"
      },
	  {
        "slug": "recipes-cupcakerecipe",
        "pageLocalizorKeyPrefix": "recipes_cupcakerecipe"
      },	  
      {
        "slug": "recipes-classiccheapfemaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_classiccheapfemaleclothingrecipe"
      },
	  {
        "slug": "recipes-classiccheapmaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_classiccheapmaleclothingrecipe"
      },
	  {
        "slug": "recipes-classicexpensivefemaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_classicexpensivefemaleclothingrecipe"
      },
	  {
        "slug": "recipes-classicexpensivemaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_classicexpensivemaleclothingrecipe"
      },
	  {
        "slug": "recipes-moderncheapfemaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_moderncheapfemaleclothingrecipe"
      },
	  {
        "slug": "recipes-moderncheapmaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_moderncheapmaleclothingrecipe"
      },
	  {
        "slug": "recipes-modernexpensivefemaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_modernexpensivefemaleclothingrecipe"
      },
	  {
        "slug": "recipes-modernexpensivemaleclothingrecipe",
        "pageLocalizorKeyPrefix": "recipes_modernexpensivemaleclothingrecipe"
      },	
	  {
        "slug": "recipes-donutrecipe",
        "pageLocalizorKeyPrefix": "recipes_donutrecipe"
      },
	  {
        "slug": "recipes-frenchfriesrecipe",
        "pageLocalizorKeyPrefix": "recipes_frenchfriesrecipe"
      },
	  {
        "slug": "recipes-cheapgiftrecipe",
        "pageLocalizorKeyPrefix": "recipes_cheapgiftrecipe"
      },
	  {
        "slug": "recipes-expensivegiftrecipe",
        "pageLocalizorKeyPrefix": "recipes_expensivegiftrecipe"
      },
	  {
        "slug": "recipes-hotdogrecipe",
        "pageLocalizorKeyPrefix": "recipes_hotdogrecipe"
      },
	  {
        "slug": "recipes-icecreamrecipe",
        "pageLocalizorKeyPrefix": "recipes_icecreamrecipe"
      },
	  {
        "slug": "recipes-cheapjewelryrecipe",
        "pageLocalizorKeyPrefix": "recipes_cheapjewelryrecipe"
      },
	  {
        "slug": "recipes-expensivejewelryrecipe",
        "pageLocalizorKeyPrefix": "recipes_expensivejewelryrecipe"
      },
	  {
        "slug": "recipes-margaritarecipe",
        "pageLocalizorKeyPrefix": "recipes_margaritarecipe"
      },
	  {
        "slug": "recipes-martinirecipe",
        "pageLocalizorKeyPrefix": "recipes_martinirecipe"
      },
	  {
        "slug": "recipes-earbuds01recipe",
        "pageLocalizorKeyPrefix": "recipes_earbuds01recipe"
      },
      {
        "slug": "recipes-headphones01recipe",
        "pageLocalizorKeyPrefix": "recipes_headphones01recipe"
      },
	  {
        "slug": "recipes-pizzarecipe",
        "pageLocalizorKeyPrefix": "recipes_pizzarecipe"
      },
	  {
        "slug": "recipes-saladrecipe",
        "pageLocalizorKeyPrefix": "recipes_saladrecipe"
      },
	  {
        "slug": "recipes-sodarecipe",
        "pageLocalizorKeyPrefix": "recipes_sodarecipe"
      },
	  {
        "slug": "recipes-whiskyrecipe",
        "pageLocalizorKeyPrefix": "recipes_whiskyrecipe"
      },
	  {
        "slug": "recipes-winerecipe",
        "pageLocalizorKeyPrefix": "recipes_winerecipe"
      },
	  {
        "slug": "recipes-smartphone2recipe",
        "pageLocalizorKeyPrefix": "recipes_smartphone2recipe"
      },
	  {
        "slug": "recipes-smartwatch1recipe",
        "pageLocalizorKeyPrefix": "recipes_smartwatch1recipe"
      },
	]
  },
  {
    "CategoryLocalizorKey": "common_factorymachines",
    "pages": [
	  {
        "slug": "furniture-automatedbakingmachine",
        "pageLocalizorKeyPrefix": "itemname_automatedbakingmachine"
      },
	  {
        "slug": "furniture-automatedfryingmachine",
        "pageLocalizorKeyPrefix": "itemname_automatedfryingmachine"
      },	
	  {
        "slug": "furniture-batchpasteurizer",
        "pageLocalizorKeyPrefix": "itemname_batchpasteurizer"
      },
	  {
        "slug": "furniture-bottlingmachine",
        "pageLocalizorKeyPrefix": "itemname_bottlingmachine"
      },
	  {
        "slug": "furniture-tobaccorollingmachine",
        "pageLocalizorKeyPrefix": "itemname_tobaccorollingmachine"
      },
	  {
        "slug": "furniture-consumergoodsassemblymachine",
        "pageLocalizorKeyPrefix": "itemname_consumergoodsassemblymachine"
      },
	  {
        "slug": "furniture-conveyorbelt",
        "pageLocalizorKeyPrefix": "itemname_conveyorbelt"
      },
	  {
        "slug": "furniture-conveyorbeltsplitter",
        "pageLocalizorKeyPrefix": "itemname_conveyorbeltsplitter"
      },	  
	  {
        "slug": "furniture-conveyorfoodgrill",
        "pageLocalizorKeyPrefix": "itemname_conveyorfoodgrill"
      },
	  {
        "slug": "furniture-foodassemblymachine",
        "pageLocalizorKeyPrefix": "itemname_foodassemblymachine"
      },
	  {
        "slug": "furniture-foodcuttingmachine",
        "pageLocalizorKeyPrefix": "itemname_foodcuttingmachine"
      },
	  {
        "slug": "furniture-polishingmachine",
        "pageLocalizorKeyPrefix": "itemname_polishingmachine"
      },
	  {
        "slug": "furniture-hardeningtunnel",
        "pageLocalizorKeyPrefix": "itemname_hardeningtunnel"
      },
	  {
        "slug": "furniture-industrialblendingmachine",
        "pageLocalizorKeyPrefix": "itemname_industrialblendingmachine"
      },
	  {
        "slug": "furniture-inputstation",
        "pageLocalizorKeyPrefix": "itemname_inputstation"
      },
	  {
        "slug": "furniture-industrialsewingmachine",
        "pageLocalizorKeyPrefix": "itemname_industrialsewingmachine"
      },
	  {
        "slug": "furniture-kilnmachine",
        "pageLocalizorKeyPrefix": "itemname_kilnmachine"
      },
	  {
        "slug": "furniture-lasercuttingmachine",
        "pageLocalizorKeyPrefix": "itemname_lasercuttingmachine"
      },
	  {
        "slug": "furniture-outputstation",
        "pageLocalizorKeyPrefix": "itemname_outputstation"
      },
	]
  },
  {
    "CategoryLocalizorKey": "common_factory_ingredients",
    "pages": [	  	  
	  {
        "slug": "products-mediumaudiomodule",
        "pageLocalizorKeyPrefix": "itemname_mediumaudiomodule"
      },
	  {
        "slug": "products-smallaudiomodule",
        "pageLocalizorKeyPrefix": "itemname_smallaudiomodule"
      },
	  {
        "slug": "products-bakingmix",
        "pageLocalizorKeyPrefix": "itemname_bakingmix"
      },
	  {
        "slug": "products-barley",
        "pageLocalizorKeyPrefix": "itemname_barley"
      },
	  {
        "slug": "products-battery",
        "pageLocalizorKeyPrefix": "itemname_battery"
      },
	  {
        "slug": "products-blueagave",
        "pageLocalizorKeyPrefix": "itemname_blueagave"
      },
	  {
        "slug": "products-blendedmargarita",
        "pageLocalizorKeyPrefix": "itemname_blendedmargarita"
      },
	  {
        "slug": "products-blendedmartini",
        "pageLocalizorKeyPrefix": "itemname_blendedmartini"
      },
	  {
        "slug": "products-brewedcoffee",
        "pageLocalizorKeyPrefix": "itemname_brewedcoffee"
      },
	  {
        "slug": "products-burgerbun",
        "pageLocalizorKeyPrefix": "itemname_burgerbun"
      },
	  {
        "slug": "products-butter",
        "pageLocalizorKeyPrefix": "itemname_butter"
      },
	  {
        "slug": "products-capacitors",
        "pageLocalizorKeyPrefix": "itemname_capacitors"
      },
	  {
        "slug": "products-carbondioxide",
        "pageLocalizorKeyPrefix": "itemname_carbondioxide"
      },
	  {
        "slug": "products-carbonatedsoda",
        "pageLocalizorKeyPrefix": "itemname_carbonatedsoda"
      },
	  {
        "slug": "products-carbonatedbeer",
        "pageLocalizorKeyPrefix": "itemname_carbonatedbeer"
      },
	  {
        "slug": "products-ceramicmug",
        "pageLocalizorKeyPrefix": "itemname_ceramicmug"
      },
	  {
        "slug": "products-cheese",
        "pageLocalizorKeyPrefix": "itemname_cheese"
      },
	  {
        "slug": "products-chickenbreast",
        "pageLocalizorKeyPrefix": "itemname_chickenbreast"
      },
	  {
        "slug": "products-cigarpaper",
        "pageLocalizorKeyPrefix": "itemname_cigarpaper"
      },
	  {
        "slug": "products-cigarettepaper",
        "pageLocalizorKeyPrefix": "itemname_cigarettepaper"
      },
	  {
        "slug": "products-mediumcircuitboard",
        "pageLocalizorKeyPrefix": "itemname_mediumcircuitboard"
      },
	  {
        "slug": "products-smallcircuitboard",
        "pageLocalizorKeyPrefix": "itemname_smallcircuitboard"
      },
	  {
        "slug": "products-clay",
        "pageLocalizorKeyPrefix": "itemname_clay"
      },
	  {
        "slug": "products-colaflavoring",
        "pageLocalizorKeyPrefix": "itemname_colaflavoring"
      },
	  {
        "slug": "products-cookedpatty",
        "pageLocalizorKeyPrefix": "itemname_cookedpatty"
      },
	  {
        "slug": "products-coppercladlaminate",
        "pageLocalizorKeyPrefix": "itemname_coppercladlaminate"
      },
	  {
        "slug": "products-cream",
        "pageLocalizorKeyPrefix": "itemname_cream"
      },	  
	  {
        "slug": "products-cupcakebatter",
        "pageLocalizorKeyPrefix": "itemname_cupcakebatter"
      },
	  {
        "slug": "products-cutfabricclassiccheap",
        "pageLocalizorKeyPrefix": "itemname_cutfabricclassiccheap"
      },
	  {
        "slug": "products-cutfabricclassicexpensive",
        "pageLocalizorKeyPrefix": "itemname_cutfabricclassicexpensive"
      },
	  {
        "slug": "products-cutfabricmoderncheap",
        "pageLocalizorKeyPrefix": "itemname_cutfabricmoderncheap"
      },
	  {
        "slug": "products-cutfabricmodernexpensive",
        "pageLocalizorKeyPrefix": "itemname_cutfabricmodernexpensive"
      },
	  {
        "slug": "products-cutgemscheap",
        "pageLocalizorKeyPrefix": "itemname_cutgemscheap"
      },
	  {
        "slug": "products-cutgemsexpensive",
        "pageLocalizorKeyPrefix": "itemname_cutgemsexpensive"
      },
	  {
        "slug": "products-dough",
        "pageLocalizorKeyPrefix": "itemname_dough"
      },
	  {
        "slug": "products-dressing",
        "pageLocalizorKeyPrefix": "itemname_dressing"
      },
	  {
        "slug": "products-fabriccheap",
        "pageLocalizorKeyPrefix": "itemname_fabriccheap"
      },
	  {
        "slug": "products-fabricexpensive",
        "pageLocalizorKeyPrefix": "itemname_fabricexpensive"
      }, 
      {
        "slug": "products-fermentedbeer",
        "pageLocalizorKeyPrefix": "itemname_fermentedbeer"
      },
	  {
        "slug": "products-fermentedwhisky",
        "pageLocalizorKeyPrefix": "itemname_fermentedwhisky"
      },
	  {
        "slug": "products-fermentedwine",
        "pageLocalizorKeyPrefix": "itemname_fermentedwine"
      },
	  {
        "slug": "products-frosting",
        "pageLocalizorKeyPrefix": "itemname_frosting"
      },
	  {
        "slug": "products-gin",
        "pageLocalizorKeyPrefix": "itemname_gin"
      },
	  {
        "slug": "products-glass",
        "pageLocalizorKeyPrefix": "itemname_glass"
      },
	  {
        "slug": "products-smallglassdome",
        "pageLocalizorKeyPrefix": "itemname_smallglassdome"
      },
	  {
        "slug": "products-grapes",
        "pageLocalizorKeyPrefix": "itemname_grapes"
      },
	  {
        "slug": "products-grilledchicken",
        "pageLocalizorKeyPrefix": "itemname_grilledchicken"
      },
	  {
        "slug": "products-grilledsausage",
        "pageLocalizorKeyPrefix": "itemname_grilledsausage"
      },
	  {
        "slug": "products-groundbeef",
        "pageLocalizorKeyPrefix": "itemname_groundbeef"
      },
	  {
        "slug": "products-groundcoffeebeans",
        "pageLocalizorKeyPrefix": "itemname_groundcoffeebeans"
      },
	  {
        "slug": "products-hops",
        "pageLocalizorKeyPrefix": "itemname_hops"
      },	  
	  {
        "slug": "products-hotdogbun",
        "pageLocalizorKeyPrefix": "itemname_hotdogbun"
      },
	  {
        "slug": "products-icecreammixture",
        "pageLocalizorKeyPrefix": "itemname_icecreammixture"
      },
	  {
        "slug": "products-integratedcircuits",
        "pageLocalizorKeyPrefix": "itemname_integratedcircuits"
      },
	  {
        "slug": "products-juniperberries",
        "pageLocalizorKeyPrefix": "itemname_juniperberries"
      },
	  {
        "slug": "products-ketchup",
        "pageLocalizorKeyPrefix": "itemname_ketchup"
      },
	  {
        "slug": "products-laminateddough",
        "pageLocalizorKeyPrefix": "itemname_laminateddough"
      },
	  {
        "slug": "products-rawlettuce",
        "pageLocalizorKeyPrefix": "itemname_rawlettuce"
      },
	  {
        "slug": "products-limejuice",
        "pageLocalizorKeyPrefix": "itemname_limejuice"
      },
	  {
        "slug": "products-marinade",
        "pageLocalizorKeyPrefix": "itemname_marinade"
      },
	  {
        "slug": "products-metalband",
        "pageLocalizorKeyPrefix": "itemname_metalband"
      },
	  {
        "slug": "products-microphone",
        "pageLocalizorKeyPrefix": "itemname_microphone"
      },
	  {
        "slug": "products-milk",
        "pageLocalizorKeyPrefix": "itemname_milk"
      },
      {
        "slug": "products-moldedearbuds",
        "pageLocalizorKeyPrefix": "itemname_moldedearbuds"
      },
	  {
        "slug": "products-moldedheadphones",
        "pageLocalizorKeyPrefix": "itemname_moldedheadphones"
      },
	  {
        "slug": "products-moldedsmartphone",
        "pageLocalizorKeyPrefix": "itemname_moldedsmartphone"
      },
	  {
        "slug": "products-moldedsmartwatch",
        "pageLocalizorKeyPrefix": "itemname_moldedsmartwatch"
      },
	  {
        "slug": "products-moldedsnowglobebase",
        "pageLocalizorKeyPrefix": "itemname_moldedsnowglobebase"
      },
	  {
        "slug": "products-moldedsnowglobefigure",
        "pageLocalizorKeyPrefix": "itemname_moldedsnowglobefigure"
      },
	  {
        "slug": "products-onion",
        "pageLocalizorKeyPrefix": "itemname_onion"
      },
	  {
        "slug": "products-orangepeels",
        "pageLocalizorKeyPrefix": "itemname_orangepeels"
      },
	  {
        "slug": "products-pepper",
        "pageLocalizorKeyPrefix": "itemname_pepper"
      },
	  {
        "slug": "products-plaincupcake",
        "pageLocalizorKeyPrefix": "itemname_plaincupcake"
      },
	  {
        "slug": "products-plaindonut",
        "pageLocalizorKeyPrefix": "itemname_plaindonut"
      },
	  {
        "slug": "products-plastic",
        "pageLocalizorKeyPrefix": "itemname_plastic"
      },
	  {
        "slug": "products-polishedgemscheap",
        "pageLocalizorKeyPrefix": "itemname_polishedgemscheap"
      },
	  {
        "slug": "products-polishedgemsexpensive",
        "pageLocalizorKeyPrefix": "itemname_polishedgemsexpensive"
      },
	  {
        "slug": "products-rawfries",
        "pageLocalizorKeyPrefix": "itemname_rawfries"
      },
		{
        "slug": "products-rawpizza",
        "pageLocalizorKeyPrefix": "itemname_rawpizza"
      },
	  {
        "slug": "products-rawsausage",
        "pageLocalizorKeyPrefix": "itemname_rawsausage"
      },
	  {
        "slug": "products-resistors",
        "pageLocalizorKeyPrefix": "itemname_resistors"
      },
	  {
        "slug": "products-russetpotatoes",
        "pageLocalizorKeyPrefix": "itemname_russetpotatoes"
      },
	  {
        "slug": "products-mediumscreen",
        "pageLocalizorKeyPrefix": "itemname_mediumscreen"
      },
	  {
        "slug": "products-smallscreen",
        "pageLocalizorKeyPrefix": "itemname_smallscreen"
      },	  	  
	  {
        "slug": "products-slicedcheese",
        "pageLocalizorKeyPrefix": "itemname_slicedcheese"
      },
	  {
        "slug": "products-slicedlettuce",
        "pageLocalizorKeyPrefix": "itemname_slicedlettuce"
      },
	  {
        "slug": "products-slicedonions",
        "pageLocalizorKeyPrefix": "itemname_slicedonions"
      },
	  {
        "slug": "products-slicedpepper",
        "pageLocalizorKeyPrefix": "itemname_slicedpepper"
      },
	  {
        "slug": "products-slicedtomato",
        "pageLocalizorKeyPrefix": "itemname_slicedtomato"
      },
	  {
        "slug": "products-speaker",
        "pageLocalizorKeyPrefix": "itemname_speaker"
      },
	  {
        "slug": "products-sugar",
        "pageLocalizorKeyPrefix": "itemname_sugar"
      },
	  {
        "slug": "products-tobacco",
        "pageLocalizorKeyPrefix": "itemname_tobacco"
      },
	  {
        "slug": "products-rawtomato",
        "pageLocalizorKeyPrefix": "itemname_rawtomato"
      },
	  {
        "slug": "products-tequila",
        "pageLocalizorKeyPrefix": "itemname_tequila"
      },
	  {
        "slug": "products-tomatopaste",
        "pageLocalizorKeyPrefix": "itemname_tomatopaste"
      },
	  {
        "slug": "products-transistors",
        "pageLocalizorKeyPrefix": "itemname_transistors"
      },
	  {
        "slug": "products-triplesec",
        "pageLocalizorKeyPrefix": "itemname_triplesec"
      },
	  {
        "slug": "products-uncutgemscheap",
        "pageLocalizorKeyPrefix": "itemname_uncutgemscheap"
      },
	  {
        "slug": "products-uncutgemsexpensive",
        "pageLocalizorKeyPrefix": "itemname_uncutgemsexpensive"
      },
	  {
        "slug": "products-vinaigrette",
        "pageLocalizorKeyPrefix": "itemname_vinaigrette"
      },
	  {
        "slug": "products-vermouth",
        "pageLocalizorKeyPrefix": "itemname_vermouth"
      },
	  {
        "slug": "products-water",
        "pageLocalizorKeyPrefix": "itemname_water"
      },
	  {
        "slug": "products-yeast",
        "pageLocalizorKeyPrefix": "itemname_yeast"
      },
	]
  },
  {
    "CategoryLocalizorKey": "vehicles_title",
    "pages": [
      {
        "slug": "vehicles-alternatetraveloptions",
        "pageLocalizorKeyPrefix": "vehicletypename_alternatetraveloptions"
      },
	  {
        "slug": "vehicles-anselmoaf90",
        "pageLocalizorKeyPrefix": "vehicletypename_anselmoaf90"
      },
      {
        "slug": "vehicles-bima320",
        "pageLocalizorKeyPrefix": "vehicletypename_bima320"
      },	  
      {
        "slug": "vehicles-ferdinand112",
        "pageLocalizorKeyPrefix": "vehicletypename_ferdinand112"
      },
      {
        "slug": "vehicles-freighttruckt1",
        "pageLocalizorKeyPrefix": "vehicletypename_freighttruckt1"
      },
      {
        "slug": "vehicles-honzamimic",
        "pageLocalizorKeyPrefix": "vehicletypename_honzamimic"
      },
	  {
        "slug": "vehicles-missamvillian",
        "pageLocalizorKeyPrefix": "vehicletypename_missamvillian"
      },
      {
        "slug": "vehicles-mersaidimgagt",
        "pageLocalizorKeyPrefix": "vehicletypename_mersaidimgagt"
      },
      {
        "slug": "vehicles-mersaidis500",
        "pageLocalizorKeyPrefix": "vehicletypename_mersaidis500"
      },      
      {
        "slug": "vehicles-petrollsfanton",
        "pageLocalizorKeyPrefix": "vehicletypename_petrollsfanton"
      },
      {
        "slug": "vehicles-umcdesert",
        "pageLocalizorKeyPrefix": "vehicletypename_umcdesert"
      },
      {
        "slug": "vehicles-umcnunavut",
        "pageLocalizorKeyPrefix": "vehicletypename_umcnunavut"
      },
	  {
        "slug": "vehicles-deliverytruck",
        "pageLocalizorKeyPrefix": "vehicletypename_deliverytruck"
      },
      {
        "slug": "vehicles-vordpony",
        "pageLocalizorKeyPrefix": "vehicletypename_vordpony"
      },
      {
        "slug": "vehicles-vordtiaravic",
        "pageLocalizorKeyPrefix": "vehicletypename_vordtiaravic"
      },
      {
        "slug": "vehicles-vordv150",
        "pageLocalizorKeyPrefix": "vehicletypename_vordv150"
      }
    ]
  }
]