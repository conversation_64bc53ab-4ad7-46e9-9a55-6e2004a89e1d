{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "GameManager", "methodName": "InitGameManager", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "GameManager", "methodName": "InitScene", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "SteamManager", "methodName": "InitOnPlayMode", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "AstarPathfindingProject", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1285913884", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "AstarPathfindingProject", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Drawing", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2556216553", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Drawing", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Fungus", "nameSpace": "Fungus", "className": "TMProLinkAnimator", "methodName": "RegisterAutoAddTMPLinkAnim", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Fungus", "nameSpace": "Fungus", "className": "TMProLinkAnimator", "methodName": "RegisterDefaultTMPLinkAnims", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2151063247", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.2D.Animation.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.2D.SpriteShape.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1305236987", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.2D.SpriteShape.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1542543861", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Universal.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__284746279", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}]}