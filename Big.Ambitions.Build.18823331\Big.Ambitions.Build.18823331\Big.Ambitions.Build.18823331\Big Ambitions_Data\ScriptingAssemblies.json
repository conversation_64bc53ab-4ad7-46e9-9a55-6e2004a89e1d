{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "HBAO.Demo.Runtime.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.AI.Navigation.dll", "BigAmbitions.GameAnalytics.dll", "BigAmbitions.InputSystem.dll", "Unity.ResourceManager.dll", "Unity.Services.Core.Environments.Internal.dll", "HGPlugins.dll", "Unity.ScriptableBuildPipeline.dll", "Unity.Services.Core.dll", "Unity.InputSystem.RebindingUI.dll", "Unity.Services.Core.Networking.dll", "DayNightCycle.dll", "Unity.Formats.Fbx.Runtime.dll", "BigAmbitions.dll", "BigAmbitions.InteriorDesigner.dll", "HBAO.Runtime.dll", "BigAmbitions.Seasons.dll", "Unity.Services.Core.Scheduler.dll", "Unity.Services.Core.Environments.dll", "NaughtyAttributes.Test.dll", "Unity.Services.Core.Configuration.dll", "Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "DOTween.Modules.dll", "BigAmbitions.Items.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "HBAO.HighDefinition.Runtime.dll", "BigAmbitions.AI.dll", "Unity.TextMeshPro.dll", "Unity.Services.Core.Analytics.dll", "Unity.Recorder.dll", "Unity.Animation.Rigging.dll", "FbxBuildTestAssets.dll", "Unity.Profiling.Core.dll", "BigAmbitions.Characters.dll", "Unity.MemoryProfiler.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "BigAmbitions.Factories.dll", "HBAO.Demo.HighDefinition.Runtime.dll", "Unity.Burst.dll", "Unity.Services.Core.Internal.dll", "Unity.Services.Core.Device.dll", "JimmysUnityUtilities.dll", "Unity.Recorder.Base.dll", "Unity.Services.Core.Threading.dll", "HGExtensions.dll", "UnityEngine.UI.dll", "Unity.Services.Core.Telemetry.dll", "BigAmbitions.DebugMode.dll", "NaughtyAttributes.Core.dll", "Unity.Timeline.dll", "Unity.Services.Core.Components.dll", "Unity.InputSystem.dll", "Unity.InputSystem.ForUI.dll", "Unity.Services.Analytics.dll", "Unity.Animation.Rigging.DocCodeExamples.dll", "Autodesk.Fbx.dll", "ExternalPlugins.dll", "BigAmbitions.Neighborhoods.dll", "Unity.Mathematics.dll", "BehaviorDesigner.Runtime.dll", "BigAmbitions.SoundSystem.dll", "Unity.Services.Core.Registration.dll", "UnityUIExtensions.dll", "Cinemachine.dll", "Unity.RenderPipelines.HighDefinition.Runtime.dll", "Unity.Addressables.dll", "BigAmbitions.PlacementSystem.dll", "Google.OrTools.dll", "OdinSerializer.dll", "System.Runtime.CompilerServices.Unsafe.dll", "DOTween.dll", "Google.Protobuf.dll", "Unity.Burst.Unsafe.dll", "Mono.HttpUtility.dll", "Newtonsoft.Json.dll", "Facepunch.Steamworks.Win64.dll", "OdinAOTSupport.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}